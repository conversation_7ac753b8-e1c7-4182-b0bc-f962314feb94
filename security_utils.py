import base64
import hashlib
import json

import requests

from paraty import Config
from paraty_commons_3.decorators.retry import retry
from paraty_commons_3.logging.my_gae_logging import logging

from Crypto.Cipher import AES
from paraty_commons_3.datastore import datastore_communicator

'''
NOTE (fmatheis): If you want to be able to decrypt reservations you need the following:

- Add google-cloud-secret-manager==2.0.0 to requirements.txt
- Make sure there is a secret in security-seeker for your project (use test.test_secret_utils.TestSecretUtils.test_create_secret to create it)
'''

SECURITY_PROJECT = 'security-seeker'
SECURITY_SERVER = 'https://%s.appspot.com' % SECURITY_PROJECT

#This header is safe to keep, we use it to create the security copies
ENCRYPTION_HEADER = "2jac8n3kmna!jdondgo"

#Comment me in production, just for local steing
# SECURITY_SERVER = 'http://0.0.0.0:8080'


def get_secret(project_id, secret_id, version_id=1):
	"""
	Access the payload for the given secret version if one exists. The version
	can be a version number as a string (e.g. "5") or an alias (e.g. "latest").
	"""

	# Import the Secret Manager client library.
	from google.cloud import secretmanager

	# Create the Secret Manager client.
	client = secretmanager.SecretManagerServiceClient()

	# Build the resource name of the secret version.
	name = f"projects/{project_id}/secrets/{secret_id}/versions/{version_id}"

	# Access the secret version.
	response = client.access_secret_version(request={"name": name})

	payload = response.payload.data.decode("UTF-8")
	return payload

AUTHENTICATION = ''
try:
	AUTHENTICATION = get_secret(SECURITY_PROJECT, Config.PROJECT)
except:
	logging.warning("This project will not be able to decrypt reservations")

APPLICATION = 'unit_test'
FORCE_OLD_BOOKINGS = 'True'


try:
	from cryptography.fernet import Fernet

	key = Fernet.generate_key()
	cipher_suite = Fernet(key)
except:
	logging.warning("This project will not be able to basic encrypt or decrypt a string")

#Note that this is to be used only with things that aren't so risky
BASIC_ENCRYPTION_KEY = b'FQ-sFKb4ULwu3jJGKQsOrG3leR-LJkGopJvdNIEwmwE='

def basic_text_encrypt(text):
	cipher_suite = Fernet(BASIC_ENCRYPTION_KEY)
	encoded_text = cipher_suite.encrypt(text.encode('utf8'))
	return encoded_text.decode('utf8')

def basic_text_decrypt(encoded_text):
	cipher_suite = Fernet(BASIC_ENCRYPTION_KEY)
	return cipher_suite.decrypt(encoded_text.encode('utf8')).decode("utf8")


def chunks(lst, n):
	"""Yield successive n-sized chunks from lst."""
	for i in range(0, len(lst), n):
		yield lst[i:i + n]


MAX_TEXT_SIZE = 10000000
CHUNK_SEPARATOR = "___@@@___@@@___"


@retry(Exception, tries=2, delay=5, backoff=2)
def encrypt_random_text(hotel_code, text):

	headers = {
		'Authentication': ENCRYPTION_HEADER,
		'Application': Config.PROJECT,
		'Client-Name': '',
		'Client-Ip': 'N/A',
		'Client-Headers': 'N/A',
		'Hotel-Code': hotel_code,
		'Random-Content': 'True',
		'Connection': 'keep-alive',
		'Content-Type': 'text/plain; charset=utf-8'
	}

	chunked_text = chunks(text, MAX_TEXT_SIZE)
	result = ''

	for text_chunk in chunked_text:
		response = requests.post(SECURITY_SERVER + '/generic_encrypt', data=text_chunk, headers=headers, timeout=60)

		if response.status_code != 200:
			logging.error("Problems encrypting random text for hotel: %s", hotel_code)
			raise Exception("Problem encrypting random text at %s", hotel_code)

		chunk_response = response.content.decode('utf-8')
		result += chunk_response + CHUNK_SEPARATOR

	return result


@retry(Exception, tries=2, delay=5, backoff=2)
def decrypt_random_text(hotel_code, text):

	headers = {
		'Authentication': get_secret(SECURITY_PROJECT, 'generic_decryptor_password'),
		'Application': Config.PROJECT,
		'Client-Name': '',
		'Client-Ip': 'N/A',
		'Client-Headers': 'N/A',
		'Hotel-Code': hotel_code,
		'Random-Content': 'True',
		'Connection': 'keep-alive',
		'Content-Type': 'application/json'
	}

	chunked_text = [x for x in text.split(CHUNK_SEPARATOR) if x]
	result = ''

	for text_chunk in chunked_text:
		response = requests.post(SECURITY_SERVER + '/generic_decrypt', data=text_chunk, headers=headers, timeout=60)

		if response.status_code != 200:
			logging.error("Problems decrypting random text for hotel: %s", hotel_code)
			raise Exception("Problem decrypting random text at %s", hotel_code)

		chunk_response = response.content.decode('utf-8')
		result += chunk_response

	return result


def hash_password(string_to_hash: str):
	result = hashlib.sha256(string_to_hash.encode('utf-8'))
	return result.hexdigest()


def md5(string_to_hash):
	result = hashlib.md5(string_to_hash)
	return result

def safe_hash(password):
    """
    Safely hashes a password using bcrypt with a randomly generated salt.
    
    Args:
        password (str): The password to hash
        
    Returns:
        str: The hashed password (including salt) as a string
        
    Example:
        # Hash a password
        hashed_password = safe_hash("my_secure_password")
        
        # To verify a password against a stored hash:
        # 1. Convert both to bytes
        password_bytes = "my_secure_password".encode('utf-8')
        stored_hash_bytes = hashed_password.encode('utf-8')
        # 2. Use bcrypt.checkpw to verify
        is_valid = bcrypt.checkpw(password_bytes, stored_hash_bytes)
    """

	
    import bcrypt

    """Hashes a password using bcrypt with a randomly generated salt."""
    # Encode the password as bytes (bcrypt works with bytes)
    password_bytes = password.encode('utf-8')

    # Generate a salt
    salt = bcrypt.gensalt()

    # Hash the password with the salt
    hashed_password_bytes = bcrypt.hashpw(password_bytes, salt)

    # Decode the hashed password back to a string for storage (including the salt)
    hashed_password_string = hashed_password_bytes.decode('utf-8')

    return hashed_password_string


def decrypt_reservations(hotel_code, reservations):

	headers = {
		'Authentication': AUTHENTICATION,
		'Application': APPLICATION,
		'Client-Ip': 'N/A',
		'Client-Name': '',
		'Client-Headers': 'N/A',
		'Hotel-Code': hotel_code,
		'Force-Decryption-Of-Old-Bookings': FORCE_OLD_BOOKINGS,
		'Connection': 'keep-alive',
		'Content-Type': 'application/json'
	}

	dict_reservations = [dict(reservation) for reservation in reservations]
	for current in dict_reservations:
		if current.get('encrypted') and not isinstance(current.get('encrypted'), str):
			encrypted_as_string = base64.b64encode(current.get('encrypted')).decode('utf-8')
			current['encrypted'] = encrypted_as_string


	payload = {reservation_dict['identifier']: reservation_dict for reservation_dict in dict_reservations}

	response = requests.post(SECURITY_SERVER + '/decrypt', data=json.dumps(payload), headers=headers, timeout=60)
	result = response.json()
	return result.values()


def encrypt_and_save_reservation(reservation, hotel, encryption_key):
	reservation['personal_details'], save = encrypt_data(reservation.get('personal_details', ''), encryption_key)
	reservation['reservation_data'], save = encrypt_data(reservation.get('reservation_data', ''), encryption_key)
	if save:
		datastore_communicator.save_to_datastore("PendingReservation", reservation.id, reservation, hotel_code=hotel)


def encrypt_data(data, encryption_key):
	try:
		if not_encrypted_yet(data):
			encryptor = AES.new(encryption_key, AES.MODE_CFB)
			encrypted_data = encryptor.encrypt(json.dumps(data).encode("utf-8"))
			return base64.urlsafe_b64encode(encryptor.iv + encrypted_data).decode("utf-8"), True
		logging.info(f"Already encrypted")
		return data, False
	except Exception as e:
		logging.error(f"Error encrypting: {e}")
		return data, False


def decrypt_data(encrypted_data, encryption_key):
	encrypted_data = base64.urlsafe_b64decode(encrypted_data.encode("utf-8"))
	iv = encrypted_data[:16]
	ciphertext = encrypted_data[16:]

	decryptor = AES.new(encryption_key, AES.MODE_CFB, iv=iv)
	decrypted_data = decryptor.decrypt(ciphertext)

	return json.loads(decrypted_data.decode("utf-8"))


def not_encrypted_yet(data):
	try:
		if not isinstance(data, str):
			return True
		if isinstance(data, list):
			return True
		json.loads(data)
		return True
	except:
		return False
