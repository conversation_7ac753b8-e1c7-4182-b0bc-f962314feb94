import copy
import json
# from typing_extensions import deprecated

import requests

from paraty_commons_3.audit_utils import make_traceback
from paraty_commons_3.content.web_content_utils import unescape, unescapeText, buildFriendlyUrl
from paraty_commons_3.decorators.cache.distributed_strong_cache import distributed_cache
from paraty_commons_3.decorators.cache.managers_cache.manager_cache import managers_cache
from paraty_commons_3.decorators.cache.requests_cache import local_thread_cache
from paraty_commons_3.decorators.timerdecorator import timeit
from paraty_commons_3.logging.my_gae_logging import logging

from paraty_commons_3.datastore import datastore_communicator, datastore_utils
from paraty_commons_3.datastore.datastore_utils import id_to_entity_key, alphanumeric_to_id
from paraty_commons_3.decorators.cache.timebased_cache import timed_cache
from paraty_commons_3.model.web_page_properties import get_related_web_properties
from paraty_commons_3.datastore.datastore_communicator import map_dictionaries_to_datastore_entities

CACHE_HOURS = 24

ISO_DATETIME_FORMAT = '%Y-%m-%d %H:%M:%S.%f'
ISO_DATE_FORMAT = '%Y-%m-%d'

def __set_name_and_desc_in_language(hotel_code, result, language, nameProperty, descProperty):
	if language and language != 'SPANISH':
		myNames = get_web_page_properties(hotel_code, language, nameProperty)
		myDescriptions = get_web_page_properties(hotel_code, language, descProperty)

		nameDictionary = dict([(x['entityKey'], x.get('value', '')) for x in myNames if 'entityKey' in x])
		descriptionDictionary = dict([(x['entityKey'], x.get('value', '')) for x in myDescriptions if 'entityKey' in x])

		for current in result:
			current['name'] = unescape(nameDictionary.get(current['key'], current.get('name')))
			current['description'] = unescape(descriptionDictionary.get(current['key'], current.get('description')))


# @deprecated("Deprecated: Use paraty_commons_3.common_data.data_management.reservation_utils.get_reservations_of_hotel instead")
def get_reservations_of_hotel(hotel, from_datetime, to_datetime, reservation_id=None, include_end_date=False, include_cancelled_reservations=False, discard_test_reservations=True, include_modified_reservations=True):

	if reservation_id:
		result_entities = datastore_communicator.get_using_entity_and_params('Reservation', search_params=[('identifier', '=', reservation_id)], hotel_code=hotel['applicationId'])
		return result_entities

	if include_end_date:
		to_datetime = to_datetime + " 23:59:59"

	reservations = datastore_communicator.get_using_entity_and_params('Reservation', search_params=[('timestamp', '>=', from_datetime), ('timestamp', '<=', to_datetime)], hotel_code=hotel['applicationId'])

	if include_cancelled_reservations:
		cancelled_entities = datastore_communicator.get_using_entity_and_params('Reservation', search_params=[('cancellationTimestamp', '>=', from_datetime), ('cancellationTimestamp', '<=', to_datetime)], hotel_code=hotel['applicationId'])
		if cancelled_entities:
			reservations.extend(cancelled_entities)

	if include_modified_reservations:
		cancelled_entities = datastore_communicator.get_using_entity_and_params('Reservation', search_params=[('modificationTimestamp', '>=', from_datetime), ('modificationTimestamp', '<=', to_datetime)], hotel_code=hotel['applicationId'])
		if cancelled_entities:
			reservations.extend(cancelled_entities)

	if discard_test_reservations:
		#Remove test reservations, we don't want these reservations to be sent to the channel manager
		reservations =filter(lambda x: not x.get('comments', '') or  not '@@@TEST@@@' in x.get('comments', '') , reservations)

	reservations = _filter_duplicates(reservations)

	return reservations


def _filter_duplicates(reservations):
	result = []
	identifiers_list = set()
	for booking in reservations:
		booking_id = booking['identifier']
		if booking_id not in identifiers_list:
			identifiers_list.add(booking_id)
			result.append(booking)

	return result


def get_boards_of_hotel(hotel: dict, language="SPANISH", include_removed=False) -> list[dict]:

	result = []
	try:
		result = entity_get_all('Regimen', hotel['applicationId'], "regimenName", "regimenDescription", language)
	except Exception as e:
		logging.warning("Something bad happened getting the boards!: %s. hotel_code: %s Returning empty array." % (e, hotel['applicationId']))

	if not include_removed:
		result = list(filter(lambda x: not x.get('removed', False), result))

	return result


@managers_cache(hotel_code_provider=lambda f, a, k: a[0], ttl_seconds=3600*CACHE_HOURS, entities='IntegrationConfiguration')
def get_all_integrations_configuration_of_hotel(hotel_code):
	result_entities = datastore_communicator.get_using_entity_and_params('IntegrationConfiguration', search_params=[], hotel_code=hotel_code)
	return list(result_entities)


# @distributed_cache(hotel_code_generator=lambda x: x[0]['applicationId'], entities=['IntegrationConfiguration'], memory_life_seconds=3600*CACHE_HOURS)
def get_integration_configuration_of_hotel(hotel: dict, name: str):

	all_configurations = get_all_integrations_configuration_of_hotel(hotel['applicationId'])
	# result_entities = datastore_communicator.get_using_entity_and_params('IntegrationConfiguration', search_params=[], hotel_code=hotel['applicationId'])
	return list(filter(lambda x: x.get('name', '').lower() == name.lower(), all_configurations))


@managers_cache(hotel_code_provider=lambda f,a,k: a[0]['applicationId'], entities='ConfigurationProperty', ttl_seconds=3600*CACHE_HOURS)
def get_hotel_all_advance_configs(hotel):
	result_entities = datastore_communicator.get_using_entity_and_params('ConfigurationProperty', hotel_code=hotel['applicationId'])
	return result_entities


@managers_cache(hotel_code_provider=lambda f, a, k: a[0]['applicationId'],  only_thread_local=True)
def _get_advanced_config_item_by_main_key(hotel: dict):
	return {x['mainKey']: x for x in get_hotel_all_advance_configs(hotel)}


# Backward compatibility
def get_hotel_advance_config_item(hotel: dict, main_key: str) -> list:
	result = _get_advanced_config_item_by_main_key(hotel).get(main_key, None)
	if result:
		return [result]
	return []
	#
	# result_entities = datastore_communicator.get_using_entity_and_params('ConfigurationProperty', search_params=[('mainKey', '=', item)], hotel_code=hotel['applicationId'])
	# return result_entities

@managers_cache(hotel_code_provider=lambda f,a,k: a[0]['applicationId'], entities='WebConfiguration', ttl_seconds=3600*CACHE_HOURS)
def get_hotel_all_web_configs(hotel):
	result_entities = datastore_communicator.get_using_entity_and_params('WebConfiguration', hotel_code=hotel['applicationId'])
	formatted_results = []
	for element in result_entities:
		configs_formatted = {}
		configs_list = element.get('configurations', [])
		for config_element in configs_list:
			splitted_config = config_element.split(" @@ ")
			if len(splitted_config) > 1:
				configs_formatted[splitted_config[0]] = splitted_config[1]

		formatted_results.append({
			'name': element['name'],
			'id': element.id,
			'configurations': configs_formatted
		})

	return formatted_results


def get_hotel_web_config_item(hotel_code, item):
	all_web_configs = get_hotel_all_web_configs({'applicationId': hotel_code})
	result_entities = list(filter(lambda x: x.get('name') == item, all_web_configs))

	if not result_entities:
		return {}

	return result_entities[0]['configurations']


@managers_cache(hotel_code_provider=lambda f,a,k: a[0]['applicationId'], only_thread_local=True)
# @local_thread_cache()
def _get_advanced_config_by_key(hotel_code):
	return {x['mainKey']: x['value'] for x in get_hotel_all_advance_configs({'applicationId': hotel_code})}


def get_hotel_advance_config_value(hotel_code: str, main_key: str) -> str|None:

	result = get_hotel_advance_config_item({'applicationId': hotel_code}, main_key)
	if result:
		return result[0].get('value')

	# target_config = _get_advanced_config_by_key(hotel_code).get(main_key, None)
	# if target_config and (target_config.lower() == 'false'):
	# 	return None
	#
	# return target_config


def _get_field_name(main_key):
	if main_key.endswith('Description'):
		return 'description'
	elif main_key.endswith("Subtitle"):
		return 'subtitle'
	elif main_key.endswith("Name"):
		return 'name'
	elif main_key.endswith("Title"):
		return 'name'
	elif main_key.endswith("pictureAlt"):
		return 'altText'
	else:
		return main_key


def _get_web_properties_sorted_by_entity_id(hotel_code, language, entity_related_properties):
	properties_at_datastore = []
	if entity_related_properties:
		for current_property in entity_related_properties:
			properties_at_datastore += get_web_page_properties(hotel_code, language, current_property['mainKey'])

	result = {}
	for current_property in properties_at_datastore:
		if not current_property['entityKey'] in result:
			result[current_property['entityKey']] = []

		result[current_property['entityKey']].append(current_property)

	return result


def _set_language_web_page_properties_for_all_entities(hotel_code, entities, language, related_properties):
	if related_properties:
		all_properties_at_datastore = _get_web_properties_sorted_by_entity_id(hotel_code, language, related_properties)
	else:
		all_properties_at_datastore = _get_web_page_properties_organized_by_entity_key(hotel_code, language)

	for entity in entities:
		entity_key = datastore_utils.id_to_entity_key(hotel_code, entity.key)
		properties_at_datastore = all_properties_at_datastore.get(entity_key, [])
		for current_property in properties_at_datastore:
			value = current_property['value']
			field_name = _get_field_name(current_property['mainKey'])
			entity[field_name] = unescape(value)


def _set_language_web_page_properties(hotel_code, entity, language, related_properties):

	entity_key = datastore_utils.id_to_entity_key(hotel_code, entity.key)

	if related_properties:
		properties_at_datastore = _get_web_properties_sorted_by_entity_id(hotel_code, language, related_properties).get(entity_key, [])
	else:
		properties_at_datastore = get_web_page_properties_by_entity_key(hotel_code, language, entity_key)

	for current_property in properties_at_datastore:

		value = current_property['value']
		field_name = _get_field_name(current_property['mainKey'])
		entity[field_name] = unescape(value)


@managers_cache(hotel_code_provider=lambda f,a,k: a[0], entities='WebSection', requires_compression=True)
# @distributed_cache(compressed=True, entities=['WebSection'])
def get_all_web_sections_by_language(hotel_code, language, set_languages=True, only_enabled=False, get_all_properties=False):
	result_entities = copy.deepcopy(get_all_websections(hotel_code))
	if result_entities and only_enabled:
		result_entities = list(filter(lambda section: section.get('enabled'), result_entities))

	if not result_entities:
		return []

	for section_element in result_entities:
		if not section_element.get('spanish_name'):
			section_element['spanish_name'] = section_element['name']

	if set_languages:
		related_properties = get_related_web_properties('WebSection') if not get_all_properties else None
		for section_element in result_entities:
			_set_language_web_page_properties(hotel_code, section_element, language, related_properties)

	return result_entities

def _get_web_section_key_builder(x, y):
	result = 'get_web_section_%s_%s_%s' % (x[0]['applicationId'], x[1], x[2])
	result += "_%s" % y.get('set_languages', True)
	return result


# @timed_cache(hours=CACHE_HOURS)
@managers_cache(hotel_code_provider=lambda f, a, k: a[0], ttl_seconds=3600*CACHE_HOURS, entities='PriceIncrease')
def get_all_price_increases(hotel_code, language, set_languages=True):
	result_entities = datastore_communicator.get_using_entity_and_params('PriceIncrease', hotel_code=hotel_code)
	if not result_entities:
		return []

	for section_element in result_entities:
		section_element['spanish_name'] = section_element['name']

	if set_languages:
		related_properties = get_related_web_properties('PriceIncrease')
		for section_element in result_entities:
			_set_language_web_page_properties(hotel_code, section_element, language, related_properties)

	return result_entities

@managers_cache(hotel_code_provider=lambda f, a, k: a[0], ttl_seconds=3600*CACHE_HOURS, entities='WebSection', requires_compression=True)
# @distributed_cache(compressed=True, entities=['WebSection'], memory_life_seconds=3600*CACHE_HOURS)
def get_all_websections(hotel_code):
	return datastore_communicator.get_using_entity_and_params('WebSection', hotel_code=hotel_code)

# @timed_cache(hours=CACHE_HOURS, key_builder_with_optional=_get_web_section_key_builder)
def get_web_section(hotel, section_name, language, set_languages=True, only_enabled=False):
	# result_entities = datastore_communicator.get_using_entity_and_params('WebSection', search_params=[('name', '=', section_name)], hotel_code=hotel['applicationId'])
	target_hotel = hotel['applicationId'] if isinstance(hotel, dict) else hotel
	all_web_sections = get_all_websections(target_hotel)
	result_entities = [x for x in all_web_sections if x['name'] == section_name]

	if only_enabled:
		result_entities = [x for x in result_entities if x.get('enabled')]

	if not result_entities:
		return None

	result_entity = copy.deepcopy(result_entities[0])
	result_entity['spanish_name'] = result_entity['name']
	if set_languages:
		_set_language_web_page_properties(target_hotel, result_entity, language, [])

	return result_entity


def get_web_section_from_key(hotel_code, section_key, language, set_languages=True):
	try:
		alphanumeric_key = alphanumeric_to_id(section_key)
	except Exception as e:
		logging.info(e)
		logging.info(section_key)
		return None

	all_sections = get_all_websections(hotel_code)
	result_entities = list(filter(lambda x: x.key.id == alphanumeric_key, all_sections))
	if not result_entities:
		return None
	result_entity = copy.deepcopy(result_entities[0])

	result_entity['spanish_name'] = result_entity['name']
	result_entity['key'] = section_key
	if set_languages:
		_set_language_web_page_properties(hotel_code, result_entity, language, [])

	return result_entity


@managers_cache(
	hotel_code_provider=lambda f,a,k: a[0],
	key_generator=lambda f,a,k: f"get_entity_from_key_{a[0]}_{a[1].kind}_{a[1].id}_{a[2]}",
	entities='WebSection',
	ttl_seconds=3600*CACHE_HOURS,
) # Kept entity WebSection for compatibility, but has no sense, it should be dynamic
def get_entity_from_key(hotel_code, entity_key, language, set_languages=True):
	result_entity = datastore_communicator.get_entity(entity_key.kind, entity_key.id, hotel_code=hotel_code)

	if not result_entity:
		return None

	result_entity['spanish_name'] = result_entity['name']
	if set_languages:
		_set_language_web_page_properties(hotel_code, result_entity, language, [])

	return result_entity


@managers_cache(hotel_code_provider=lambda f,a,k: a[0], entities='Picture', ttl_seconds=3600*CACHE_HOURS, requires_compression=True)
# @distributed_cache(compressed=True, entities=['Picture'], memory_life_seconds=3600*CACHE_HOURS)
def get_all_pictures(hotel_code):
	all_pictures = datastore_communicator.get_using_entity_and_params('Picture', [], hotel_code=hotel_code)

	for picture in all_pictures:
		picture.pop('blobKey', None)
		picture.pop('storage_url', None)
		picture.pop('legacy_url', None)

	return all_pictures


# @distributed_cache(compressed=True, entities=['Picture'], memory_life_seconds=3600*CACHE_HOURS)
@managers_cache(hotel_code_provider=lambda f,a,k: a[0], entities='Picture', ttl_seconds=3600, requires_compression=True, only_thread_local_and_memory=True)
def get_pictures_for_entity(hotel_code: str, entity_key: str, language: str, include_extra_props: bool = False):
	all_pictures = get_all_pictures(hotel_code)
	pictures = list(filter(lambda x: x.get('mainKey') == entity_key and x.get('enabled') and (not x.get('language') or language in x.get('language')), all_pictures))

	result = []

	if not pictures:
		return []

	_get_all_pictures_in_parallel(pictures, language, hotel_code, include_extra_props, result)
	result.sort(key=lambda p: p['priority'] if p['priority'] else 'zz9999')
	return result


def _process_pictures_for_entity(pictures, language, hotel_code, list_to_append):
	result = []
	pictures = list(filter(lambda x: not x.get('language') or language in x.get('language'), pictures))
	_get_all_pictures_in_parallel(pictures, language, hotel_code, True, result)

	result.sort(key=lambda p: p['priority'] if p['priority'] else 'zz9999')
	list_to_append += result


@timeit
def get_pictures_for_entities_list(hotel_code, entities_list, language):
	all_pictures = get_all_pictures(hotel_code)

	pictures_results = {}

	for entity_element in entities_list:
		pictures_results.setdefault(entity_element, [])
		pictures = list(filter(lambda x: x.get('enabled') and x.get('mainKey') == entity_element, all_pictures))
		if not pictures:
			continue

		_process_pictures_for_entity(pictures, language, hotel_code, pictures_results[entity_element])

	return pictures_results


def _get_all_pictures_in_parallel(pictures, language, hotel_code, include_extra_props, result):
	_set_language_web_page_properties_for_all_entities(hotel_code, pictures, language, get_related_web_properties('Picture'))

	for picture in pictures:
		picture_key = datastore_utils.id_to_entity_key(hotel_code, picture.key)
		picture_link = None
		built_link = None
		link_url = picture.get('linkingUrl')
		if link_url:
			if link_url.startswith("https://"):
				picture_link = link_url
				built_link = link_url
			else:
				# Imported here to prevent circular dependencies
				from paraty.utilities.data.data_builder.sections_data import get_section_from_spanish_name
				linked_section_info = get_section_from_spanish_name(link_url, hotel_code, language, avoid_pictures=True)
				picture_link = linked_section_info['friendlyUrlInternational'] if linked_section_info else ''
				built_link = linked_section_info['section_link'] if linked_section_info else ''

		picture_info = _create_picture_info(picture, hotel_code, language, picture_link, built_link, picture_key, include_extra_props)
		result.append(picture_info)


def _get_picture_in_parallel(picture, language, hotel_code, include_extra_props, result):
	if picture.get('language') and not language in picture.get('language'):
		return

	picture_key = datastore_utils.id_to_entity_key(hotel_code, picture.key)
	_set_language_web_page_properties(hotel_code, picture, language, get_related_web_properties('Picture'))

	picture_link = None
	built_link = None
	if picture.get('linkingUrl'):
		if picture.get('linkingUrl') and picture.get('linkingUrl').startswith("https://"):
			picture_link = picture.get('linkingUrl')
			built_link = picture.get('linkingUrl')
		else:
			# Imported here to prevent circular dependencies
			from paraty.utilities.data.data_builder.sections_data import get_section_from_spanish_name
			linked_section_info = get_section_from_spanish_name(picture['linkingUrl'], hotel_code, language)
			picture_link = linked_section_info['friendlyUrlInternational'] if linked_section_info else ''
			built_link = linked_section_info['section_link'] if linked_section_info else ''

	picture_info = _create_picture_info(picture, hotel_code, language, picture_link, built_link, picture_key, include_extra_props)
	result.append(picture_info)


def _create_picture_info(picture, hotel_code, language, picture_link, built_link, picture_key, include_extra_props):
	picture_info = \
		{
			'servingUrl': picture.get('servingUrl'),
			'title': unescapeText(picture.get('name')),
			'description': unescapeText(picture.get('description')),
			'enabled': picture.get('enabled'),
			'priority': picture.get('priorityInWeb'),
			'linkUrl': picture_link,
			'link': built_link,
			'onlyInMobile': False,
			'allowInMobile': True,
			'key': picture_key,
			'id': picture.key.id,
			'altText': unescapeText(picture.get('altText'))
		}

	if picture.get('servingUrl'):
		picture_info['servingUrl'] = picture['servingUrl'].replace("http:", "https:")

	if include_extra_props:
		advanced_props = get_web_page_properties_by_entity_key(hotel_code, language, picture_key)
		props_to_avoid = ['pictureDescription', 'pictureTitle', 'pictureAlt']

		# Add all available info from edit buttons
		extra_properties = {x['mainKey']: unescape(x['value']) for x in advanced_props if
							not x['mainKey'] in props_to_avoid}
		picture_info.update(extra_properties)

		if extra_properties.get("nomobile"):
			picture_info['allowInMobile'] = False

		if extra_properties.get("onlymobile") or extra_properties.get("onlyInMobile"):
			picture_info['onlyInMobile'] = True

		if extra_properties.get("onlytablet"):
			picture_info['onlytablet'] = True

	return picture_info

@managers_cache(hotel_code_provider=lambda f,a,k: a[0], entities='WebPageProperty', ttl_seconds=3600*CACHE_HOURS, requires_compression=True)
# @distributed_cache(compressed=True, entities=['WebPageProperty'], memory_life_seconds=3600*CACHE_HOURS)
def get_all_web_page_properties_by_language(hotel_code, language):
	try:
		target_url = f"https://wpp-utils-flask-399475283438.europe-west1.run.app/get-wpp-by-language?hotel_code={hotel_code}&language={language}"
		logging.info(f"Getting all WebPageProperties from cloud function: {target_url}")
		result = requests.get(target_url, timeout=60)
	except:
		return _get_all_web_page_properties_from_datastore(language, hotel_code)

	if result.status_code == 200:
		return map_dictionaries_to_datastore_entities(json.loads(result.content)["result"])
	else:
		return _get_all_web_page_properties_from_datastore(language, hotel_code)


def _get_all_web_page_properties_from_datastore(language, hotel_code, log_error=True):
	if log_error:
		logging.warning("Problems retrieving webPageProperties from cloudfunctions")

	return list(datastore_communicator.get_using_entity_and_params('WebPageProperty', [('languageKey', '=', language)],
														   hotel_code=hotel_code))


@managers_cache(hotel_code_provider=lambda f,a,k: a[0], only_thread_local=True)
def _get_web_page_properties_organized_by_main_key(hotel_code, language):
	web_properties = get_all_web_page_properties_by_language(hotel_code, language)
	web_properties_by_main_key = {}
	for web_property in web_properties:
		main_key = web_property.get('mainKey')
		if main_key not in web_properties_by_main_key:
			web_properties_by_main_key[main_key] = []

		web_properties_by_main_key[main_key].append(web_property)

	return web_properties_by_main_key


@managers_cache(hotel_code_provider=lambda f,a,k: a[0], only_thread_local=True)
def _get_web_page_properties_organized_by_entity_key(hotel_code, language):
	web_properties = get_all_web_page_properties_by_language(hotel_code, language)
	web_properties_by_main_key = {}
	for web_property in web_properties:
		entity_key = web_property.get('entityKey')
		if entity_key not in web_properties_by_main_key:
			web_properties_by_main_key[entity_key] = []

		web_properties_by_main_key[entity_key].append(web_property)

	return web_properties_by_main_key


def get_web_page_properties(hotel_code, language, main_key):
	return list(_get_web_page_properties_organized_by_main_key(hotel_code, language).get(main_key, []))



def get_web_page_properties_by_entity_key(hotel_code, language, entity_key):

	return list(_get_web_page_properties_organized_by_entity_key(hotel_code, language).get(entity_key, []))

	# filtered_properties = filter(lambda x: x.get('entityKey') == entity_key, get_all_web_page_properties_by_language(hotel_code, language))
	# # web_properties = list(datastore_communicator.get_using_entity_and_params('WebPageProperty', [('entityKey', '=', entity_key), ('languageKey', '=', language)], hotel_code=hotel_code))
	# return list(filtered_properties)


@managers_cache(hotel_code_provider=lambda f,a,k: a[0], entities='WebPageProperty')
# @distributed_cache(hotel_code_generator=lambda x: x[0], entities=['WebPageProperty'])
def get_all_webpage_properties_by_entity_key(hotel_code, entity_key):
	web_properties = list(datastore_communicator.get_using_entity_and_params('WebPageProperty', [('entityKey', '=', entity_key)], hotel_code=hotel_code))
	return web_properties


def get_rates_of_hotel(hotel: dict, language="SPANISH", only_enabled=False, include_removed=False):

	result = []
	try:
		result = entity_get_all('Rate', hotel['applicationId'], "rateName", "rateDescription", language)
	except Exception as e:
		logging.warning("Something bad happened getting the rates!: %s. hotel_code: %s Returning empty array." % (e, hotel['applicationId']))

	if only_enabled:
		result = list(filter(lambda x: x['enabled'], result))

	if not include_removed:
		result = list(filter(lambda x: not x.get('removed'), result))

	return result


def get_promotions_of_hotel(hotel, language="SPANISH", only_enabled=False, include_removed=False) -> list[dict]:

	result = []
	try:
		result = entity_get_all('Promotion', hotel['applicationId'], "promotionName", "promotionDescription", language)
	except Exception as e:
		logging.warning("Something bad happened getting the promotions!: %s. hotel_code: %s Returning empty array." % (e, hotel['applicationId']))

	if only_enabled:
		result = list(filter(lambda x: x['enabled'], result))

	if not include_removed:
		result = list(filter(lambda x: not x.get('removed'), result))

	return result


def get_multirates_of_hotel(hotel, language="SPANISH", only_enabled=False, include_removed=False):

	result = []
	try:
		result = entity_get_all('MultiRate', hotel['applicationId'], "rateName", "rateDescription", language)
	except Exception as e:
		logging.warning("Something bad happened getting the multirates!: %s. hotel_code: %s Returning empty array." % (e, hotel['applicationId']))

	if only_enabled:
		result = list(filter(lambda x: x['enabled'], result))

	if not include_removed:
		result = list(filter(lambda x: not x.get('removed'), result))

	return result


@managers_cache(hotel_code_provider=lambda f,a,k: a[1],
				   requires_compression=True,
				   entities='Supplement,Regimen,MultiRate,PriceIncrease,Promotion,Rate,RoomType',
				   ttl_seconds=3600*CACHE_HOURS)
def entity_get_all(entity_name, hotel_code, name_property, description_property, language):
	result_entities = datastore_communicator.get_using_entity_and_params(entity_name, search_params=[], hotel_code=hotel_code)

	result = []
	for current_entity in result_entities:
		new_entity_to_send = dict(current_entity)
		new_entity_to_send['id'] = current_entity.key.id
		new_entity_to_send['key'] = id_to_entity_key(hotel_code, current_entity.key)

		result.append(new_entity_to_send)

	__set_name_and_desc_in_language(hotel_code, result, language, name_property, description_property)

	return result


def get_room_of_hotel_by_room_id(hotel, room_id):
	for room in get_rooms_of_hotel(hotel, include_removed=True):
		if str(alphanumeric_to_id(room['key'])) == str(room_id):
			return room
	return None


def get_rooms_of_hotel(hotel: dict, language="SPANISH", include_removed=False) -> list[dict]:

	result = []
	try:
		result = entity_get_all('RoomType', hotel['applicationId'], "roomName", "roomDescription", language)
	except Exception as e:
		logging.warning("Something bad happened getting the rooms!: %s. hotel_code: %s Returning empty array." % (e, hotel['applicationId']))

	if not include_removed:
		result = list(filter(lambda x: not x.get('removed'), result))

	for room_element in result:
		room_element['description'] = unescape(room_element['description'])

	return result


def get_additional_services_of_hotel(hotel, language="SPANISH", include_disabled=False):

	result = []
	try:
		result = entity_get_all('Supplement', hotel['applicationId'], "supplementName", "supplementDescription", language)
	except Exception as e:
		logging.warning("Something bad happened getting the additional services!: %s. hotel_code: %s Returning empty array." % (e, hotel['applicationId']))

	if not include_disabled:
		result = list(filter(lambda x: x['enabled'], result))

	return result


@managers_cache(hotel_code_provider=lambda f,a,k: a[0]['applicationId'], entities='RateCondition', ttl_seconds=3600*CACHE_HOURS, only_thread_local_and_memory=True)
# @timed_cache(hours=CACHE_HOURS)
def get_conditions_of_hotel(hotel, language="SPANISH", only_enabled=False, include_removed=False) -> list[dict]:

	result = None

	if only_enabled:
		result = datastore_communicator.get_using_entity_and_params('RateCondition', search_params=[('enabled', '=', True)], hotel_code=hotel['applicationId'])
	else:
		result = datastore_communicator.get_using_entity_and_params('RateCondition', search_params=[], hotel_code=hotel['applicationId'])

	if not include_removed:
		result_not_removed_virtually = filter(lambda x: x.get('removed', 'false') == 'false' or not x.get("removed"), result)
		result = result_not_removed_virtually

	final_result = []
	for current_entity in result:
		new_entity_to_send = dict(current_entity)
		new_entity_to_send['id'] = current_entity.key.id
		new_entity_to_send['key'] = id_to_entity_key(hotel['applicationId'], current_entity.key)

		final_result.append(new_entity_to_send)
	__set_name_and_desc_in_language(hotel['applicationId'], final_result, language, "rateConditionName", "rateConditionDescription")

	return final_result


@managers_cache(hotel_code_provider=lambda f,a,k: a[0]['applicationId'], entities='WebPageProperty', ttl_seconds=3600*24, only_thread_local_and_memory=True)
# @timed_cache(key_builder=lambda x: "get_web_page_property_%s_%s" % (x[1], x[2]), hours=24)
def get_web_page_property(hotel, key_rate, show_names):

	result = datastore_communicator.get_using_entity_and_params('WebPageProperty',
	                                                            search_params=[('entityKey', '=', key_rate), ('languageKey','=',show_names)],
	                                                            hotel_code=hotel['applicationId'])

	return result


def get_price_increase_of_hotel(hotel, language="SPANISH", include_removed=False):

	result = []
	try:
		result = entity_get_all('PriceIncrease', hotel['applicationId'], "priceIncreaseName", "promotionDescription", language)
	except Exception as e:
		logging.warning("Something bad happened getting the supplements!: %s. hotel_code: %s Returning empty array." % (e, hotel['applicationId']))

	if not include_removed:
		result = list(filter(lambda x: not x.get('removed'), result))

	for price_increase in result:
		price_increase['description'] = unescape(price_increase.get('description'))

	return result

