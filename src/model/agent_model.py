# -*- coding: utf-8 -*-
import datetime
from google.cloud import ndb


class Agents(ndb.Model):

    Description = ndb.StringProperty()
    Extension = ndb.IntegerProperty()
    Name = ndb.StringProperty()
    code = ndb.StringProperty()

    def to_json(self):

        agent = {}
        agent['Description'] = self.Description
        agent['Extension'] = self.Extension
        agent['Name'] = self.Name
        agent['code'] = self.code

        return agent
