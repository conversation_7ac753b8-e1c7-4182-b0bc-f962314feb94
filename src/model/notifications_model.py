# -*- coding: utf-8 -*-
import datetime
from google.cloud import ndb



class Notification(ndb.Model):
	creation_datetime = ndb.DateTimeProperty(auto_now=True)
	already_viewed = ndb.BooleanProperty(default=False)
	view_time = ndb.DateTimeProperty()
	user = ndb.StringProperty()
	type = ndb.StringProperty()
	logo = ndb.StringProperty()
	text = ndb.StringProperty()
	link = ndb.StringProperty(default='#')

	def is_viewed(self):
		return self.already_viewed

	def is_expired(self):
		if not self.is_viewed():
			return False
		return True

	def set_viewed(self):
		self.already_viewed = True
		self.view_time = datetime.datetime.now()
		self.put()


	def to_json(self):
		notification = {}
		notification['text'] = self.text
		notification['logo'] = self.logo
		notification['type'] = self.type
		notification['link'] = self.link
		notification['isSiteIcon'] = False
		return notification