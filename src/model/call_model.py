# -*- coding: utf-8 -*-
import datetime
from google.cloud import ndb


class Call(ndb.Model):

    src = ndb.StringProperty()
    start = ndb.DateTimeProperty()
    duration = ndb.IntegerProperty()
    billsec = ndb.IntegerProperty()
    status = ndb.StringProperty()
    client = ndb.StringProperty()
    dst = ndb.StringProperty()
    agent = ndb.StringProperty()
    agent_name = ndb.StringProperty()
    name = ndb.StringProperty()
    fails = ndb.TextProperty()

    def to_json(self):

        call = {}
        call['src'] = self.src
        call['start'] = self.start
        call['duration'] = self.duration
        call['billsec'] = self.billsec
        call['status'] = self.status
        call['client'] = self.client
        call['dst'] = self.dst
        call['agent'] = self.agent
        call['agent_name'] = self.agent_name
        call['name'] = self.name
        call['fails'] = self.fails

        return call
