<div id="vacation_club_toggle_template" class="toggle_vacation_club_search" style="display:none;">
    <span class="vacation_club_search_label">{{ T_VACATION_CLUB_SEARCH|safe }}</span>
    <div class="vacation_club_switcher">
        <input type="checkbox" id="vacation_club_search" class="vacation_club_search">
        <div class="knobs"></div>
        <div class="layer"></div>
    </div>
</div>

<div id="vacation_club_container_template" class="vacation_club_container">
    <form class="vacation_club_form">
        <div class="inputs_wrapper">
            <div class="input_wrapper">
                <input class="user_field" type="text" name="member_id">
                <label for="member_id">{{ T_VACATION_CLUB_MEMBER_ID|safe }}</label>
            </div>
            <div class="input_wrapper">
                <input class="user_field" type="text" name="name">
                <label for="name">{{ T_NAME|safe }}</label>
            </div>
            <div class="input_wrapper">
                <input class="user_field" type="text" name="email">
                <label for="email">{{ T_EMAIL|safe }}</label>
            </div>
        </div>

        <div class="buttons_block">
            <div class="search_loader">
                <img src="/static/images/loading_spinner.gif" alt="Loading">
            </div>
            <div class="find_user_btn">
                <button type="button">{{ T_SEARCH_USER|safe }}</button>
            </div>
            <div class="clear_search_btn">
                <button type="reset">{{ T_CLEAR_SEARCH|safe }}</button>
            </div>
        </div>
    </form>
    <div class="vacation_club_results">
        <div class="user_element vacation_club_user_template">
            <div class="user_info">
                <div class="user_member_id">
                    <input class="user_field" type="text" name="member_id">
                </div>
                <div class="user_name">
                    <input class="user_field" type="text" name="name">
                </div>
                <div class="user_email">
                    <input class="user_field" type="text" name="email">
                </div>
                <div class="user_contract_type">
                    <input class="user_field" type="text" name="contract_type">
                </div>
            </div>

            <div class="buttons_wrapper">
                <div class="select_user_btn">
                    {{ T_SELECT|safe }}
                </div>
                <div class="cancel_user_btn">
                    {{ T_CANCEL_SELECTION|safe }}
                </div>
            </div>
        </div>
        <div class="pagination">
            <i class="prev-page fal fa-angle-left" aria-hidden="true"></i>
            <span class="pages"></span>
            <i class="next-page fal fa-angle-right" aria-hidden="true"></i>
        </div>
    </div>
</div>