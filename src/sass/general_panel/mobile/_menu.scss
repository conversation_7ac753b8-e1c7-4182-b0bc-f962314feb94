.page-sidebar-wrapper {
  .page-sidebar {
    width: 100%;
    margin: auto;
    position: fixed;
    top: 0;
    bottom: 0;
    background: #43425D !important;

    &.in, &.collapsing {
      height: auto !important;
      width: 100% !important;
    }

    .menu_logo {
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        height: auto;
        width: 54%;
      }
    }

    .logo_small, .menu_block {
      display: none;
    }

    .close_menu_button {
      display: block!important;
      position: absolute;
      top: 16px;
      z-index: 1000;
      left: 19px;
      background: white;
      border-radius: 60px !important;
      width: 40px;
      height: 40px;

      i {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }

    .menu_item a {
      border: 0 !important;
    }
  }

  .content_wrapper {
    .menu_item .menu_item_text {
      display: inline-block;
    }
  }
}