.tickets_wrapper {
    margin-bottom: 50px;
    border-bottom: 1px solid #DDD;
    padding-bottom: 30px;

    .create_ticket_btn, .modify_ticket_btn, .send_ticket_btn {
        margin: 15px 0;

        &.create_ticket_btn, &.modify_ticket_btn {
            display: inline-flex;
            margin-bottom: 5px;
        }

        &.modify_ticket_btn, &.send_ticket_btn {
            margin-left: 20px;
        }

        &.hide {
            display: none;
        }
    }

    .send_loader {
        width: 215px;
        margin-top: 10px;
        text-align: center;
        display: none;

        img {
            width: 50px;
        }
    }

    .inputs_wrapper {
        width: 650px;
        display: none;

        .whatsapp_switch_wrapper {
            position: relative;
            box-sizing: border-box;
            margin-top: 10px;
            display: none;

            .switch {
                position: relative;
                width: 105px;
                height: 32px;
                overflow: hidden;
                border-radius: 100px;

                .checkbox {
                    position: relative;
                    width: 100%;
                    height: 100%;
                    padding: 0;
                    margin: 0;
                    opacity: 0;
                    cursor: pointer;
                    z-index: 3;

                    &:active + .knobs:before {
                        width: 46px;
                        border-radius: 100px;
                    }

                    &:checked:active + .knobs:before {
                        margin-left: -26px;
                    }

                    &:checked + .knobs:before {
                        content: "Whatsapp";
                        left: 42px;
                        background-color: #F28E2A;
                    }

                    &:checked ~ .layer {
                        background-color: rgba(#F28E2A, 0.2);
                    }
                }

                .knobs, .layer {
                    position: absolute;
                    top: 0;
                    right: 0;
                    bottom: 0;
                    left: 0;
                }

                .knobs {
                    z-index: 2;

                    &:before {
                        content: "Email";
                        position: absolute;
                        top: 4px;
                        left: 4px;
                        width: 50px;
                        height: 7px;
                        color: #fff;
                        font-size: 11px;
                        font-weight: bold;
                        text-align: center;
                        line-height: 0.7;
                        padding: 9px 4px;
                        background-color: #F28E2A;
                        border-radius: 30px;
                        transition: 0.3s ease all, left 0.3s cubic-bezier(0.18, 0.89, 0.35, 1.15);
                    }
                }

                .layer {
                    width: 100%;
                    background-color: rgba(#F28E2A, 0.2);
                    transition: 0.3s ease all;
                    z-index: 1;
                    border-radius: 100px;
                }
            }
        }

        .input_wrapper {
            position: relative;
            border-radius: 4px !important;
            border: 1px solid #D7DAE2;
            background-color: white;
            box-shadow: 0 2px 3px rgba(0,0,0,0.05);
            margin: 15px 0;
            width: calc((100% - 40px) / 3);
            min-height: 40px;

            input {
                height: 40px !important;
            }

            label {
                display: inline-block;
                position: absolute;
                top: 50%;
                left: 0;
                transform: translateY(-50%);
                margin-left: 16px;
                padding: 0 4px;
                background-color: white;
                font-size: 16px;
                white-space: nowrap;
                pointer-events: none;
                transition: all 0.3s;
            }

            input, select {
                width: 100%;
                height: 100%;
                border: none;
                background: none;
                outline: none;
                padding: 0 20px;
                font-family: $font-1;
                font-size: 16px;
            }

            select {
                width: 95%;
            }

            &.active label:not(.error) {
                top: 0;
                font-size: 12px;
            }

            &:not(:first-child) {
                margin-left: 20px;
            }

            &.error {
                border-color: red;
            }
        }

        &.creating {
            display: block;

            .bottom_flex {
                display: flex;
            }

            .input_wrapper.id_ticket {
                display: none;
            }

            .input_wrapper.email {
                margin-left: 0;
            }
        }

        &.modifyng {
            display: block;

            .whatsapp_switch_wrapper {
                display: block;
            }

            .bottom_flex {
                display: flex;
            }

            .input_wrapper.email {
                display: none;
            }
        }
    }

    .response_wrapper {
        font-size: 22px;
        line-height: 0.6px;
        margin-top: 20px;

        .label {
            font-weight: 600;
        }

        .ticket_id {
            font-size: 24px;
            line-height: 1.5px;
        }

        &.hide {
            display: none;
        }
    }
}