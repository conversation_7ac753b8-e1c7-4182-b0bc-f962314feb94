.fideltour_container {
    display: none;
    padding: 20px 30px 0 30px;

    &.active {
        display: block;
    }

    .top_wrapper {
        margin-bottom: 5px;

        .title {
            font-family: Source Sans Pro, sans-serif;
            font-weight: 600;
            font-size: 20px;
            color: #4D4F5C;
        }

        .new_client_wrapper {
            display: inline-flex;
            align-items: center;
            margin-left: 20px;

            .new_client_btn {
                font-size: 14px;
                padding: 1px 18px;
                border: 0.5px solid #707070;
                cursor: pointer;

                &:first-child {
                    border-top-left-radius: 6px !important;
                    border-bottom-left-radius: 6px !important;
                    border-right: none;

                    &.active {
                        border-right: 0.5px solid #F28E2A;
                    }
                }

                &:last-child {
                    border-top-right-radius: 6px !important;
                    border-bottom-right-radius: 6px !important;
                    border-left: none;

                    &.active {
                        border-left: 0.5px solid #F28E2A;
                    }
                }

                &.active {
                    background-color: #FCEEE2;
                    border-color: #F28E2A;
                }
            }
        }
    }

    .fideltour_form {
        display: flex;
        align-items: center;

        .inputs_wrapper {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            width: 800px;

            .input_wrapper {
                position: relative;
                border-radius: 4px !important;
                border: 1px solid #D7DAE2;
                background-color: white;
                box-shadow: 0 2px 3px rgba(0,0,0,0.05);
                margin: 10px 0;
                width: calc((100% - 40px) / 3);
                min-height: 40px;

                label {
                    display: inline-block;
                    position: absolute;
                    top: 50%;
                    left: 0;
                    transform: translateY(-50%);
                    margin-left: 16px;
                    padding: 0 4px;
                    background-color: white;
                    font-size: 16px;
                    white-space: nowrap;
                    pointer-events: none;
                    transition: all 0.3s;

                    &.error {
                        transform: none;
                        background: transparent;
                        margin-left: 0;
                        margin-top: 10px;
                        top: initial !important;
                        font-size: 16px !important;
                        bottom: -30px;
                        font-weight: 300;
                        color: red;
                    }
                }

                input, select {
                    width: 100%;
                    height: 100%;
                    border: none;
                    background: none;
                    outline: none;
                    padding: 0 20px;
                    font-family: 'Source Sans Pro', sans-serif;
                    font-size: 16px;
                }

                select {
                    width: 95%;
                }

                &.active label:not(.error) {
                    top: 0;
                    font-size: 12px;
                }
            }
        }

        .buttons_block {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            align-items: stretch;
            width: 180px;
            margin-left: 100px;

            .search_loader {
                width: 215px;
                text-align: center;
                display: none;

                img {
                    width: 50px;
                }
            }

            div:not(.search_loader) {
                width: 100%;
                height: 40px;
                border: 1px solid #F28E2A;
                border-radius: 4px !important;
                background-color: white;
                margin: 10px 0;
                font-size: 18px;
                letter-spacing: 0.45px;
                color: #F28E2A;
                transition: opacity 0.3s;

                &.find_user_btn, &.create_user_btn {
                    background-color: #F28E2A;
                    color: white;
                }

                &:hover {
                    opacity: 0.7;
                }

                button {
                    appearance: none;
                    width: 100%;
                    height: 100%;
                    border: none;
                    background: none;
                    font: inherit;
                    color: inherit;
                    cursor: pointer;
                }

                &.hide {
                    display: none;
                }
            }

            .create_user_btn {
                display: none;
            }
        }
    }

    .fideltour_results {
        margin-top: 10px;
        border-bottom: 1px solid #D7DAE2;
        padding: 10px 0;

        .user_element {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border: 1px solid #D7DAE2;
            border-radius: 12px !important;
            margin-bottom: 10px;

            .modify_form {
                width: calc(100% - 400px);
                display: flex;
                flex-wrap: wrap;
                padding: 10px 30px;

                .field_element {
                    width: calc(100%/3);
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding-right: 30px;
                    margin: 7px 0;

                    input, select {
                        width: calc(100% - 40px);
                        border: none;
                        background: none !important;
                        outline: none;
                        appearance: none;
                        pointer-events: none;
                    }

                    label {
                        display: none !important;
                    }
                }
            }

            .buttons_wrapper {
                width: 400px;
                padding: 0 30px;
                display: flex;
                align-items: center;
                justify-content: space-between;

                div {
                    width: calc(50% - 5px);
                    height: 40px;
                    border: 1px solid #43425D;
                    border-radius: 4px !important;
                    background-color: white;
                    margin: 15px 0;
                    font-size: 18px;
                    letter-spacing: 0.45px;
                    color: #43425D;
                    transition: opacity 0.3s;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    cursor: pointer;

                    &.select_user_btn, &.cancel_user_btn {
                        background-color: #43425D;
                        color: white;
                    }

                    &:hover {
                        opacity: 0.7;
                    }
                }

                .save_user_btn {
                    display: none;
                }

                .cancel_user_btn {
                    display: none;
                }
            }

            &.fideltour_user_template {
                display: none;
            }

            &.modifying {
                .modify_form {
                    .field_element:not(.user_language) {
                        input, select {
                            pointer-events: all;
                            border: initial;
                            outline: 1px solid #D7DAE2;
                            appearance: auto;

                            &.error {
                                outline: 1px solid red;
                            }
                        }
                    }
                }

                .buttons_wrapper {
                    .save_user_btn {
                        display: flex;
                    }

                    .modify_user_btn {
                        display: none;
                    }
                }
            }

            &.selected {
                .buttons_wrapper {
                    .cancel_user_btn {
                        display: flex;
                    }

                    .select_user_btn {
                        display: none;
                    }
                }
            }
        }

        .pagination {
            display: none;
            justify-content: center;
            align-items: center;
            font-size: 20px;
            margin-top: 20px;

            .pages {
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 0 10px;

                .page_button {
                    margin: 0 10px;
                    cursor: pointer;

                    &.active {
                        color: #F28E2A;
                    }
                }
            }

            i {
                font-size: 24px;
                margin-top: 3px;
                cursor: pointer;
            }

            &.active {
                display: flex;
            }
        }
    }

    &.new_client {
        .fideltour_form {
            .buttons_block {
                .find_user_btn {
                    display: none;
                }

                .create_user_btn {
                    display: block;
                }
            }
        }
    }
}