# -*- coding: utf-8 -*-
import logging
from model.notifications_model import Notification
from paraty.tools import emailUtils

INTERMITENT_FAILURES = 'Fallos intermitentes en %s.'
SITE_DOWN = 'Site %s temporalmente fuera de servicio.'
ADMIN = '<EMAIL>'

def get_notifications_by_user(user):
	notifications_custom = __get_custom_notifications_by_user(user)
	notifications_automatic = __get_automatic_notifications_by_user(user)

	return notifications_custom + notifications_automatic


def notify_error_to_admin(error_text, error_subject):
	try:
		emailUtils.sendEmail(ADMIN, error_subject, '', error_text)
	except:
		logging.error("Error notifying to admin")
		pass

def __get_custom_notifications_by_user(user):
	if not user:
		return []

	notification_entities = Notification.query(Notification.user == user.get("name"),
					   						   Notification.already_viewed == False)

	return [notification_entity.to_json() for notification_entity in notification_entities]


def __get_automatic_notifications_by_user(user):
	notifications = []

	#TODO

	return notifications