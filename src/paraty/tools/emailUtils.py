import fix_path  # @UnusedImport
import logging
from google.appengine.api import mail
from google.appengine.api.mail import EmailMessage

from paraty_commons_3.libs.postmark import PMMail

SENDER_DEFAULT = '<EMAIL>'

def sendEmail(address, title, contentText, contentHtml):

    logging.info("Sending email to user: %s, email title: %s" % (address, title))

    try:
        for myAddress in address.split(";"):
            mail.send_mail(sender="Price Seeker" + " <<EMAIL>>", to=myAddress, subject=title,
                           body=contentText, html=contentHtml)
            mail.send_mail(sender="Price Seeker" + " <<EMAIL>>", to="<EMAIL>", subject=title,
                           body=contentText, html=contentHtml)
            mail.send_mail(sender="Price Seeker" + " <<EMAIL>>", to="<EMAIL>", subject=title,
                           body=contentText, html=contentHtml)
    except Exception as e:
        logging.warning("Error sending email to " + str(address))
        logging.warning(e)
        sendEmail_backup(address, title, contentText, contentHtml)


def notifyExceptionByEmail(myException, myRequest=''):
    try:
        logging.warning("Unexpected Exception" + str(myException))
        emailContent = "<html><body><B>Request: %s</B><br/><br/>%s</body><html/>" % (str(myRequest), str(myException))
        sendEmail("<EMAIL>", '[Paraty] Problem at comparator', "", emailContent)
    except:
        pass


def notifyMappingSeekerMissed(hotel, city, site):
    try :
        logging.warning("[Mapping Seeker] Mapping not found in inventory for hotel %s in %s and site %s " % (hotel, city, site))
        emailContent = "<html><body>Mapping not found in inventory for hotel <b>%s</b> in <b>%s</b> and site <b>%s</b> <br/></body><html/>" % (hotel, city, site)
        sendEmail("<EMAIL>", '[Mapping Seeker] Missing hotel in inventory', "", emailContent)
    except:
        pass


def sendEmail_backup(address, title, contentText, contentHtml, replyTo=None, sender=None):
    try:
        logging.warning("Trying to send it using PMM....")
        if not sender:
            sender = "Price Seeker" + " <<EMAIL>>"
        to = ""
        for myAddress in address.split(";"):
            to = to + myAddress + ","
        message = PMMail(api_key='70f0042b-fc70-43cf-a01c-86b783a31327',
                         sender=sender,
                         subject=title,
                         html_body=contentHtml,
                         text_body=contentText,
                         to=to,
                         bcc='<EMAIL>, <EMAIL>',
                         reply_to=replyTo)

        message.send()
        logging.warning("Sent!")
    except Exception as e:
        logging.warning("Error sending email using PMM to " + str(address))
        logging.warning(e)


def sendEmailPostMark(address, title, contentText, contentHtml, replyTo=None, sender=SENDER_DEFAULT, bcc=None):
    try:
        logging.warning("Trying to send it using PMM....")

        to = ""
        for myAddress in address:
            to = to + myAddress + ","
        message = PMMail(api_key='70f0042b-fc70-43cf-a01c-86b783a31327',
                         sender=sender,
                         subject=title,
                         html_body=contentHtml,
                         text_body=contentText,
                         to=to,
                         bcc=bcc,
                         reply_to=replyTo)

        message.send()
        logging.warning("Sent!")
    except Exception as e:
        logging.warning("Error sending email using PMM to " + str(address))
        logging.warning(e)


def send_email_with_google(to, subject, body, reply_to=None, sender='<EMAIL>'):
    try:

        logging.info("Sending email with Google to: %s with subject %s and SENT BY %s", to, subject, sender)

        message = EmailMessage(sender=sender, to=to, subject=subject, html=body, reply_to=reply_to)
        message.send()
        pass

    except Exception as e:
        logging.exception(e)


