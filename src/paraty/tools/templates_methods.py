# -*- coding: utf-8 -*-
import logging, os
import datetime
import jinja2

from datetime import datetime

#
# def _jinja2_filter_date(date):
#     return datetime.strftime(datetime.strptime(date, "%Y-%m-%d"), "%d/%m/%Y")
#
# def _jinja2_filter_month(date):
#     return datetime.strftime(datetime.strptime(date, "%Y-%m-%d"), "%B")
#
# def _jinja2_filter_year(date, fmt=None):
#     return datetime.strftime(datetime.strptime(date, "%Y-%m-%d"), "%Y")

def build_template_jinja(template_path, template_name, context, additional_path=''):

    template_loader = [template_path]

    JINJA_ENVIRONMENT = jinja2.Environment(
        loader=jinja2.FileSystemLoader(template_loader),
        auto_reload=False,
        extensions=[],
        autoescape=True)
    #
    # JINJA_ENVIRONMENT.filters['date'] = _jinja2_filter_date
    # JINJA_ENVIRONMENT.filters['month'] = _jinja2_filter_month
    # JINJA_ENVIRONMENT.filters['year'] = _jinja2_filter_year

    my_template = JINJA_ENVIRONMENT.get_template(template_name)

    result = my_template.render(context)

    return result