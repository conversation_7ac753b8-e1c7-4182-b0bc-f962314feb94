import json
import logging

import requests
from paraty.tools.retryDecorator import retry


__author__ = 'fmatheis'

'''

Rest client to be used in conjuntion with https://code.google.com/p/appengine-rest-server/

Note that we use exactly the same syntaxis for queries


'''


def get(url, entity, query="", authorization="Basic cGFibDA6bWF0aGUxcw=="):

	result = []

	headers = {
		"Accept": "application/json",
		'Authorization': authorization
	}

	if url.endswith('/'):
		rest_url = url + entity + query
	else:
		rest_url = url + '/' + entity + query

	offset = _getMoreEntities(rest_url, entity, "", headers, result)

	while offset:
		offset = _getMoreEntities(rest_url, entity, offset, headers, result)

	return result


def _getMoreEntities(url, entity, offset, headers, entities):

	currentUrl = url
	if offset and '?' in url:
		currentUrl += "&offset=%s" % offset
	elif offset:
		currentUrl += "?offset=%s" % offset

	logging.info(currentUrl)
	result = do_urlfetch_call(currentUrl, headers)
	if not result:
		return

	responseString = result.content
	jsonResult = json.loads(responseString)

	offset = jsonResult.get("list").get("@offset")

	if jsonResult.get("list") and jsonResult["list"].get(entity):
		entities.extend(jsonResult["list"][entity])

	return offset


@retry(Exception, tries=3, delay=10, backoff=20)
def do_urlfetch_call(url, headers):
	return requests.get(url=url, headers=headers, timeout=60)

if __name__ == "__main__":
	result = get("https://rest-dot-admin-hotel.appspot.com/rest/", "UsageLogEntry", "?feq_applicationId=5851669761884160&fgt_timestamp=2014-08-29 09:00&feq_action=UPDATE")
	print(result)