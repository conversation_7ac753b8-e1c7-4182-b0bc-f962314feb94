
from paraty.utils.languages.language_utils import get_available_languages, get_web_dictionary, \
	get_session_language
from paraty.utils.session import session_manager
from paraty.web_builder.common_builder import build_blank_page, build_404
from paraty.web_builder.hotels_info_builder import build_hotels_info
from paraty_commons_3.language_utils import get_language_code, get_language_title
from paraty_commons_3.datastore.datastore_communicator import get_using_entity_and_params
from paraty.utils.session import session_utils

AVAILABLE_PAGES = {
	# Hotel & Flight search
	'index': build_blank_page,
	'hotelsInfo': build_hotels_info
}


def _get_permissions_by_user():
	session = session_utils.get_session_user()
	permissions_list = []
	permissions_dict = {}
	for item in session.get("configurationMap").get("item"):
		item_value = item.split(" @@ ")
		if "call_seeker_permission" in item_value[0]:
			permissions_list = item_value[1].split(";")

	if permissions_list:
		permissions_dict = {perm.strip(): True for perm in permissions_list if perm.strip()}

	return permissions_dict


def _get_specific_permission(permission, hotel_code, hotel_codes, permissions):
	"""
	Checks if a specific permission is granted for a hotel.

	Args:
	permission (str): The permission to check.
	hotel_code (str): The code of the hotel to check.
	hotel_codes (list): List of hotel codes with specific permissions.
	permissions (dict): Dictionary of permissions.

	Returns:
	bool: True if the permission is granted, False otherwise.
	"""
	if not hotel_codes: # Only users with hotelCodes have restrictions
		return True

	return hotel_code in hotel_codes and permissions.get(permission, False)


def buildPage(page_id):
	language = get_session_language()
	translations = get_web_dictionary(language)

	context = {
		'session': session_manager.get_all(),
		'available_languages': _get_language_options(language),
		'permissions': _get_permissions_by_user()
	}
	context.update(translations)

	builder = AVAILABLE_PAGES.get(page_id)
	if not builder:
		return build_404(context)
	else:
		return AVAILABLE_PAGES.get(page_id)(context)


def _get_language_options(selected_language):
	languages_list = get_available_languages()
	language_options = []

	for language in languages_list:
		language_option = {
			'language_full': language,
			'language_code': get_language_code(language),
			'title': get_language_title(language)
		}
		if language == selected_language:
			language_option['selected'] = True

		language_options.append(language_option)

	return language_options
