def colored(msg, color):
    """Set a color to the text"""
    colors = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m'
    }
    return colors.get(color, '\033[0m') + msg + '\033[0m'


def ok(msg):
    """Print green colored text and wait for input"""
    return input(colored(msg, 'green'))

def warning(msg):
    """Print yellow colored text and wait for input"""
    return input(colored(msg, 'yellow'))


def error(msg):
    """Print red colored text and wait for input"""
    return input(colored(msg, 'red'))


def pause(msg=''):
    """Print normal text and wait for input"""
    input('\n\n' + msg + " Press any key to continue...")


def required(msg=''):
    """Requires an input from user"""
    value = None
    while not value:
        value = input(msg)
    return value


def menu_input():
    while True:
        print(
            "\n" * 5,
            "Choose the action:",
            "1 - Add new translation",
            "2 - Remove existent translation",
            "3 - Find and print a translation by key",
            "4 - Check translation languages by key",
            "5 - Check all translations languages",
            "6 - Fix missing translation by key",
            "7 - Fix all missing translations",
            "8 - Reset unsaved changes",
            "9 - Save changes",
            "0 - Exit",
            sep='\n'
        )

        input_action = required("Action: ")

        try:
            action = int(input_action)
            if action in range(0, 11):
                break
            else:
                error("Wrong action...")
        except:
            error("Input error...")

    return action
