import json
import logging
import os

from paraty.utils.session import session_manager
from paraty.utils.languages.translations import webDictionary
from paraty_commons_3.language_utils import SPANISH, ENGLISH, PORTUGUESE, ITALIAN


def get_web_dictionary(language=None):
    """
    Returns the general dictionary for the given language
    """

    if not language:
        language = get_session_language()

    # If not defined we set default
    if not language or language == 'default':
        language = get_default_language()

    try:
        dict_name = 'translations_' + language.upper()
        result = getattr(webDictionary, dict_name)

    except:
        dict_name = 'translations_' + SPANISH
        result = getattr(webDictionary, dict_name)

    return result


def get_language_list():
    try:
        with open(os.path.dirname(os.path.abspath(__file__)) + '/language_list.json') as languages:
            country_list = json.load(languages)
            country_list.sort(key=lambda x: x.get('name'))

            return country_list

    except Exception as e:
        logging.warning("Something failed trying to load language list", e)


def get_available_languages():
    return [SPANISH, ENGLISH, PORTUGUESE, ITALIAN]


def get_default_language():
    return SPANISH


def get_session_language():
    return session_manager.get('selected_language') or get_default_language()
