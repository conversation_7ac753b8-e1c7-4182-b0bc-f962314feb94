import base64
import re

from paraty.constants.advanced_configs import DOMINIO_ASOCIADO, MY_BOOKINGS_CALLCENTER, CALLSEKER_BOOKING_PROCESS
from paraty.tools import rest_client
from paraty.tools.timebased_cache import timed_cache
from paraty.utils.constants import EMAIL_SENDER
from paraty_commons_3.common_data.common_data_provider import ISO_DATE_FORMAT, get_hotel_all_web_configs, \
	get_hotel_advance_config_item, get_hotel_advance_config_value, get_rates_of_hotel, get_promotions_of_hotel
from paraty_commons_3.datastore.datastore_utils import alphanumeric_to_id, entity_id_to_alphanumeric_key
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_all_hotels
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_hotel_metadata_by_application_id

hotelsMap = {}

SEEKER_REST = "https://call-seeker.appspot.com/rest/"
ADMIN_PROD_REST = "https://rest-dot-admin-hotel.appspot.com/rest/"
HOTEL_ENDPOINT = "/admin/local/HotelApplication"
ADMIN_BASE64_PASSWORD = base64.encodebytes(bytes(f'%s:%s' % ("paco", "paco"), encoding="utf-8")).decode("utf-8").replace('\n', '')


def to_string(data, encoding='utf8'):
	if isinstance(data, str):
		return data.encode(encoding)
	elif isinstance(data, list):
		return to_string(str(data), encoding)

	return str(data)


@timed_cache(days=3)
def getHotelMetadataEntity(hotel_id):
	my_metadata = get_hotel_metadata_by_application_id(hotel_id)

	if my_metadata:
		config_values = {}
		for item_config in my_metadata.get('configurations', []):
			if item_config:
				key, value = item_config.split('@@')
				config_values[key.strip()] = value.strip()

		my_metadata['configurations'] = config_values

	return my_metadata

	# response = rest_client.get(ADMIN_PROD_REST, "HotelMetadata", "?feq_applicationId=%s" % hotel_id, authorization="Basic cGFibDA6bWF0aGUxcw==")
	#
	# data = {}
	# for hotel_metadata in response:
	# 	data['applicationId'] = hotel_metadata.get('applicationId', '')
	# 	data['accountManager'] = hotel_metadata.get('accountManager', '')
	# 	data['goals'] = hotel_metadata.get('goals', '')
	# 	data['configurations'] = []
	# 	if hotel_metadata.get('configurations', ''):
	# 		data['configurations'] = hotel_metadata.get('configurations', '').get('item', [])
	#
	# if data:
	# 	config_values = {}
	# 	for item_config in data['configurations']:
	# 		if item_config:
	# 			key, value = item_config.split('@@')
	# 			config_values[key.strip()] = value.strip()
	#
	# 	data['configurations'] = config_values
	#
	# return data


@timed_cache(days=3)
def getUrlReservation(hotel):
	hotel_metadata = getHotelMetadataEntity(hotel['applicationId'])

	url = ''
	if hotel_metadata and hotel_metadata.get('configurations', ''):
		hotel_config = hotel_metadata.get('configurations')
		url = hotel_config.get('my_reservations', '')

	if not url:
		# This should take the value of associated domain by default.
		# In case of injection it should be configured manually in manager. Don't use 'Widget Only' config to check it
		url = get_config_property_value(hotel, DOMINIO_ASOCIADO)

		misreservas = get_config_property_value(hotel, MY_BOOKINGS_CALLCENTER)
		if misreservas:
			url += "/" + misreservas + ".html"
		else:
			url += "/mis-reservas.html"

	#return always the param to avoid security headers
	if "?" in url:
		url = url + "&ignoreSecurityHeaders=True"
	else:
		url = url + "?ignoreSecurityHeaders=True"

	return url


@timed_cache(days=3)
def getUrlOffers(hotel):
	hotel_metadata = getHotelMetadataEntity(hotel['applicationId'])

	url = ''
	if hotel_metadata and hotel_metadata.get('configurations', ''):
		hotel_config = hotel_metadata.get('configurations')
		url = hotel_config.get('offers', '')

	if not url:
		# This should take the value of associated domain by default.
		# In case of injection it should be configured manually in manager. Don't use 'Widget Only' config to check it
		# https://parkroyal-grancancun-dot-park-royal-hotels.appspot.com
		url = get_config_property_value(hotel, DOMINIO_ASOCIADO)
		url += "/ofertas.html"

	#return always the param to avoid security headers
	if "?" in url:
		url = url + "&ignoreSecurityHeaders=True"
	else:
		url = url + "?ignoreSecurityHeaders=True"

	return url


def get_all_hotels_call_seeker():
	entries = get_all_hotels()
	filtered_hotels = []

	for entry in entries.values():
		hotel = entry
		hotel['key'] = entity_id_to_alphanumeric_key(hotel['id'], 'HotelApplication', 'admin-hotel')

		if hotel['applicationId'] == 'test-backend':
			hotel['inProduction'] = True
			hotel['enabled'] = True

		if hotel['enabled'] and hotel['inProduction']:
			filtered_hotels.append(hotel)

	return filtered_hotels


def get_hotel_id(hotelName):
	hotels = get_all_hotels_call_seeker()
	for hotel in hotels:
		if hotelName.lower() in hotel.get('name','').lower():
			return hotel['key']


def get_hotel_name(hotel_id):
	hotels = get_all_hotels_call_seeker()
	for hotel in hotels:
		if to_string(hotel['key']) == to_string(hotel_id):
			return hotel.get('name', '').title()


def get_hotel_name_from_email_sender(hotel_code):
	hotel_name = get_hotel_advance_config_item({'applicationId': hotel_code}, EMAIL_SENDER)
	if not hotel_name:
		return None

	hotel_name = hotel_name[0].get("value")
	splitted_hotel_name = hotel_name.split("-")
	return splitted_hotel_name[0]


def get_hotel_by_id(hotel_key):
	hotels = get_all_hotels_call_seeker()
	hotel_id = alphanumeric_to_id(hotel_key)
	for hotel in hotels:
		if int(hotel['id']) == hotel_id:
			return hotel


def get_hotel_by_name(hotel_name):
	if hotel_name == 'Todos':
		return {'key': "ALL", "name": "Todos"}

	hotels = get_all_hotels_call_seeker()
	for hotel in hotels:
		if hotel_name and hotel.get('name'):
			if hotel_name.lower() in hotel.get('name', '').lower():
				return hotel


def getRealDomain(hotel):
	special_booking_process = get_config_property_value(hotel, CALLSEKER_BOOKING_PROCESS)
	if special_booking_process:
		return special_booking_process

	return get_config_property_value(hotel, DOMINIO_ASOCIADO)


#@timed_cache(days=3)
def get_config_property_value(hotel, config_name):
	hotel_code = hotel['applicationId']
	target_value = get_hotel_advance_config_value(hotel_code, config_name)

	if '_CALLCENTER_ ' not in config_name:
		call_specific_value = get_hotel_advance_config_value(hotel_code, '_CALLCENTER_ ' + config_name)
		if call_specific_value:
			target_value = call_specific_value

	return target_value


#@timed_cache(days=3)
def getWebConfiguration(hotel, config_name):
	all_web_configs = get_hotel_all_web_configs(hotel)
	result_entities = list(filter(lambda x: x.get('name') == config_name, all_web_configs))

	if not result_entities:
		return {}

	return result_entities[0]['configurations']


def getPromocodes(hotel):
	promocode_list = []

	#distributed_strong_cache.USE_PERSISTENCE = False
	rates = get_rates_of_hotel(hotel)
	#distributed_strong_cache.USE_PERSISTENCE = True

	for rate in rates:
		if rate['enabled'] and rate['promoCode']:
			promocodes = re.split(';| ', rate['promoCode'])
			promocode_list.extend(promocodes)

	promotions = get_promotions_of_hotel(hotel, only_enabled=True)
	for promotion in promotions:
		if promotion['enabled'] and promotion['promocode']:
			promocodes = re.split(';| ', promotion['promocode'])
			promocode_list.extend(promocodes)

	return promocode_list


EXCEPTIONS = {
	'albaytcorporativa': 'http://www.albaythotels.com'
}
def build_url(name, url):
	if EXCEPTIONS.get(name):
		url_to_use = EXCEPTIONS.get(name)
	elif url and not 'multi' in url:
		url_to_use = "https://%s.appspot.com" % name
	elif url:
		url_to_use = url.replace("rest-", "%s-" % name).replace("/multi/%s" % name, "")
	else:
		return

	return url_to_use


# TODO, FMATHEIS, REST IS TO BE REMOVED
def get_reservations_of_hotel(hotel, from_date, to_date):

	from_date_str = from_date.strftime(ISO_DATE_FORMAT)
	to_date_str = to_date.strftime(ISO_DATE_FORMAT)

	return rest_client.get(hotel['url'] + '/rest/', 'Reservation', '?fge_timestamp=%s&fge_timestamp=%s' % (from_date_str, to_date_str))


def get_reservations_of_hotel_by_agent(hotel, agent, from_date, to_date):

	# from_date_str = from_date.strftime(ISO_DATE_FORMAT)
	# to_date_str = to_date.strftime(ISO_DATE_FORMAT)

	return rest_client.get(hotel['url'] + '/rest/', 'Reservation', '?feq_agent=%s&fge_timestamp=%s&fle_timestamp=%s' % (agent, from_date, to_date))


def get_calls_by_date(from_date, to_date):

	# from_date_str = from_date.strftime(ISO_DATE_FORMAT)
	# to_date_str = to_date.strftime(ISO_DATE_FORMAT)

	return rest_client.get(SEEKER_REST, "Call", '?fge_start=%s&fle_start=%s' % (from_date, to_date))
