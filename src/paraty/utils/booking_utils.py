import json
import re
from datetime import datetime
from functools import reduce

import requests

from paraty.utils.constants import SELECTED_ADDITIONAL_SERVICES, PRICE_OPTION_KEY_PREFIX, \
	SELECTED_OPTION_KEY, SEARCH_KEY_PREFIX, LANG<PERSON>AG<PERSON>, CURRENC<PERSON>
from paraty_commons_3.common_data.common_data_provider import get_rooms_of_hotel, get_boards_of_hotel, \
	get_additional_services_of_hotel, get_rates_of_hotel
from paraty_commons_3.hotel_manager.hotel_manager_utils import get_internal_url
from paraty_commons_3.session.session_utils import get_session_from_hotel


def get_selected_options(hotel_code, session_id):
	session_content = get_session_from_hotel(hotel_code, session_id)

	language = session_content.get(LANGUAGE)
	currency = session_content.get(CURRENCY)
	search = session_content.get(SEARCH_KEY_PREFIX)

	selectedPrice = []
	for partialSelected in session_content.get(SELECTED_OPTION_KEY).split(";"):
		selectedPrice.append(session_content.get(PRICE_OPTION_KEY_PREFIX + partialSelected))

	selected_services = session_content.get(SELECTED_ADDITIONAL_SERVICES)
	merged_prices_supplements = session_content.get('mergedReadyToRenderSupplements')
	supplements = []
	if selected_services:
		supplements = _get_supplements(hotel_code, selected_services, merged_prices_supplements, language)

	start_date = _format_date(search.get('startDate'))
	end_date = _format_date(search.get('endDate'))

	rooms = _get_rooms_price_summary_data(hotel_code, selectedPrice, language, search)

	total_price = reduce(lambda x, y: x + float(y[3]), selectedPrice, 0)

	selected_options = {
		'rooms': rooms,
		'rate': _get_rate_name(hotel_code, selectedPrice[0][0], language),
		'board': _get_board_name(hotel_code, selectedPrice[0][2], language),
		'total_price': total_price,
		'additional_services': supplements,
		'start_date': start_date,
		'end_date': end_date,
		'currency': currency if currency else 'EUR',
		'language': language
	}

	if len(selectedPrice[0]) > 4:
		selected_options['promotions'] = selectedPrice[0][4]

	return selected_options


def _format_date(date_str):
	date = datetime.strptime(date_str, '%Y-%m-%d')
	return date.strftime('%d/%m/%Y')


def _get_rooms_price_summary_data(hotel_code, selected_price, language, search):
	rooms_summary = []
	hotel_rooms = get_rooms_of_hotel({'applicationId': hotel_code}, language=language)

	for room_element in selected_price:
		filtered_rooms = filter(lambda room: room['key'] == room_element[1], hotel_rooms)
		room_found = next(filtered_rooms, None)

		room_name = ""
		if room_found.get("name"):
			room_name = room_found['name']

		room_info = {
			'room_name': room_name,
			'price': room_element[3]
		}

		rooms_summary.append(room_info)

	for i, my_room in enumerate(search.get('rooms', [])):
		rooms_summary[i]['pax'] = '-'.join([str(my_room['numAdults']), str(my_room['numKids']), str(my_room['numBabies'])])

	return rooms_summary


def _get_rate_name(hotel_code, rate_key, language):
	hotel_rates = get_rates_of_hotel({'applicationId': hotel_code}, language=language)
	filtered_rates = filter(lambda room: room['key'] == rate_key, hotel_rates)
	rate_found = next(filtered_rates, None)

	return rate_found.get('name')


def _get_board_name(hotel_code, board_key, language):
	hotel_boards = get_boards_of_hotel({'applicationId': hotel_code}, language=language)
	filtered_boards = filter(lambda room: room['key'] == board_key, hotel_boards)
	board_found = next(filtered_boards, None)

	return board_found.get('name')


def _get_supplements(hotel_code, selected_services, merged_prices_supplements, language):
	additional_services = get_additional_services_of_hotel({'applicationId': hotel_code}, language=language)

	result = []
	for supplement in additional_services:
		key = supplement['key']
		amount = selected_services.get('amount_' + key, 0)
		days = selected_services.get('days_' + key, 0)

		if int(amount) > 0 and int(days) > 0:
			name = supplement['name']
			description = supplement['description']
			if description:
				description = description.replace("&lt;", "<").replace("&gt;", ">").replace("&nbsp;", " ").replace("&amp;nbsp;", " ").replace("&quot;", '"')
				description = re.sub('<.*?>', '', description.replace('<br>', '\n'))

			name_of_supplement_to_get = supplement.get('name').lower().strip()
			merged_price_dict = merged_prices_supplements.get(name_of_supplement_to_get, {})

			if merged_price_dict:
				supPrice = merged_price_dict.get("price", supplement.get('price'))
			else:
				supPrice = supplement.get('price')

			#supPrice = supplement.price
			price = float(supPrice) * int(amount) * int(days)

			result_dict = {
				'key': key,
				'name': name,
				'description': description,
				'amount': amount,
				'days': days,
				'price': price,
			}

			result.append(result_dict)

	return result
