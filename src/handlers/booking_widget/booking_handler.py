# -*- coding: utf-8 -*-
import logging
from copy import deepcopy

from flask import make_response
from flask.views import MethodView

from handlers.flight_hotel.flight_hotel import get_airports_data, is_flight_hotel_active
from handlers.users_list.users_list_handler import get_mastered_users
from handlers.vacation_club.vacation_club import is_vacation_club_enabled, get_vacation_club_config
from paraty.constants.advanced_configs import AGE_SELECTION_CONFIG_PROPERTY, AGE_SELECTION_WEB_ONLY_CONFIG_PROPERTY, \
	LANGUAGE_SELECTION, CALLCENTER_COUNTRY_LIST, RANGO_EDADES_BABYS, RANGO_EDADES_KIDS, ONLY_ADULTS, \
	CALLCENTER_PROMOCODE_SELECTOR, VALID_ROOMS_OPTIONS, CALLCENTER_NUM_ROOMS_SELECTOR, HIDE_COMMENTS_CALLSEEKER, \
	CALLCENTER_IGNORE_MIN_STAY, CALLCENTER_IGNORE_DISPO, NOT_SHOW_CHECKBOX_MOBILE_OFFERS, CALLSEEKER_PRERESERVATION, \
	MEMBERS_CLUB, MEMBERCLUB_NAMESPACE, HIDE_PAYMENT_FOR_CALLSEKER, ALLOW_PETS, INCLUDE_BABIES, \
	SHOW_BABIES_AGES_SELECTOR, DOMINIO_BOOKING, CUSTOM_PATH_BOOKING, BOOK_YESTERDAY, BREADCRUMBS_CALLSEEKER, \
	BOOKING_SPA, DOMINIO_ASOCIADO, NO_AVAILABILITY_REDIRECT, CALLCENTER_COUNTRY_INDEX, DEFAULT_PROMOCODE, \
	DISABLE_BOOKING_SCROLL, CALL_CENTER_LITE_VERSION, CALL_CENTER_RELOAD_BUTTON
from paraty.constants.section_types import INDIVIDUAL_HOTEL, INDIVIDUAL_ROOM
from paraty.constants.web_configs import PREBOOKING, ZENDESK, CALLCENTER_HOTEL_GROUPS, PUSHTECH, FIDELTOUR
from paraty.tools.templates_methods import build_template_jinja
from paraty.utils import hotel_manager_utils
from paraty.utils.constants import COUNTRIES
from paraty.utils.session import session_utils, session_manager

__author__ = 'fmatheis'

from paraty.utils.hotel_manager_utils import get_config_property_value, getWebConfiguration, getPromocodes
from paraty.utils.languages.language_utils import get_web_dictionary
from paraty.utils.session.session_utils import USER_MASTERED_USERS
from paraty.utils.users_utils import is_ring2travel_user, is_master_agent_user
from paraty.web_builder.page_factory import _get_permissions_by_user
from paraty_commons_3.common_data.common_data_provider import get_all_websections, _set_language_web_page_properties
from paraty_commons_3.common_data.data_management.integrations_utils import get_integration_configuration
from paraty_commons_3.language_utils import SPANISH

PARAM_ID = "id"


LANGUAGES_TRANSLATE = {"SPANISH": u'Español', "FRENCH": u'Francés', "ENGLISH": u'English', "DUTCH": u'Holandés', "POLISH": u'Polaco', "FINNISH": u'Finlandés', "SWEDISH": u'Sueco', "PORTUGUESE": u'Português', "GERMAN": u'Alemán', 'ITALIAN' : u'Italiano', 'RUSSIAN' : u'Ruso', 'SUOMI' : u'Suomi', 'CATALAN' : u'Catalán'}
AIRPORTS_DATA = get_airports_data()

class BookingWidgetHandler(MethodView):

	def _ageSelector(self, hotel):
		return get_config_property_value(hotel, AGE_SELECTION_CONFIG_PROPERTY) or get_config_property_value(hotel, AGE_SELECTION_WEB_ONLY_CONFIG_PROPERTY)

	def _languageSelector(self, hotel):

		selector = []

		languages = get_config_property_value(hotel, LANGUAGE_SELECTION).split('-')
		languages = list(filter(None, languages))

		for language in languages:
			option = {}
			option['code'] = language
			option['value'] = LANGUAGES_TRANSLATE.get(language)
			option['selected'] = 'selected' if (language=='SPANISH') else ''
			selector.append(option)

		return selector

	def _countrySelector(self, hotel):
		selector = []
		countries = get_config_property_value(hotel, CALLCENTER_COUNTRY_INDEX)

		if countries:
			countries = countries.split(";")
			for country_option in countries:
				option = {}
				if country_option in COUNTRIES:
					option['code'] = country_option
					option['value'] = COUNTRIES[country_option]
					selector.append(option)
		else:
			countries = get_config_property_value(hotel, CALLCENTER_COUNTRY_LIST)
			if countries:
				countries = countries.split(";")
				for country_option in countries:
					option = {}
					country_info = country_option.split(",")
					if len(country_info) == 2:
						option['code'] = country_info[0]
						option['value'] = country_info[1]
						selector.append(option)

		return selector

	def _ignore_min_stay(self, hotel, user_session):
		return self._has_configuration(hotel, CALLCENTER_IGNORE_MIN_STAY, user_session)

	def _get_rooms_selector_options(self, hotel):
		room_options = get_config_property_value(hotel, "_CALLCENTER_ " + VALID_ROOMS_OPTIONS)
		if room_options:
			try:
				return list(map(lambda x: int(x), room_options.split(';')))
			except Exception as e:
				logging.error('Failed to process valid room options config')
				logging.error(e)

		num_rooms = get_config_property_value(hotel, CALLCENTER_NUM_ROOMS_SELECTOR)
		if num_rooms:
			try:
				num_rooms = int(num_rooms)
				return list(range(1, num_rooms + 1))
			except Exception as e:
				logging.error('Failed to process num rooms config')
				logging.error(e)

		return [1, 2, 3]

	def _ignore_room_availability(self, hotel, user_session):
		return self._has_configuration(hotel, CALLCENTER_IGNORE_DISPO, user_session)

	def _show_mobile_offers(self, hotel):
		return not bool(get_config_property_value(hotel, NOT_SHOW_CHECKBOX_MOBILE_OFFERS))

	def _show_limit_user_reservation(self, hotel):
		club_config = getWebConfiguration(hotel, 'Club config')
		return bool(club_config and 'limit_user_reservation' in club_config)

	def _show_quote_email(self, hotel):
		return get_config_property_value(hotel, CALLSEEKER_PRERESERVATION)

	def _show_users_club(self, hotel):
		return get_config_property_value(hotel, MEMBERS_CLUB) or get_config_property_value(hotel, MEMBERCLUB_NAMESPACE)

	def _show_chk_hide_payment(self, hotel, user_session):
		return self._has_configuration(hotel, HIDE_PAYMENT_FOR_CALLSEKER, user_session)

	def _has_configuration(self, hotel, config, user_session):
		config = get_config_property_value(hotel, config)
		if config == 'False':
			return False
		elif config == 'True':
			return True
		elif config:
			if 'users' in config.split("=") and config.split("=")[1]:
				users = config.split("=")[1].lower().split(";")
				return user_session.get('name').lower() in users
			else:
				return True
		else:
			return False

	def _show_babies(self, hotel):
		return get_config_property_value(hotel, INCLUDE_BABIES)

	def _show_pets(self, hotel):
		return get_config_property_value(hotel, ALLOW_PETS)

	def _show_babies_age_selector(self, hotel):
		return get_config_property_value(hotel, SHOW_BABIES_AGES_SELECTOR)

	def _get_kids_age_range(self, hotel):
		min_age = 2
		max_age = 12

		age_range_conf = get_config_property_value(hotel, RANGO_EDADES_KIDS)
		if age_range_conf:
			min_age, max_age = age_range_conf.split(';')[0].split(':')

		return {
			'start': int(min_age),
			'end': int(max_age)
		}

	def _get_babies_age_range(self, hotel):
		min_age = 0
		max_age = 2

		age_range_conf = get_config_property_value(hotel, RANGO_EDADES_BABYS)
		if age_range_conf:
			min_age, max_age = age_range_conf.split(';')[0].split(':')

		return {
			'start': int(min_age),
			'end': int(max_age)
		}

	def _get_email(self, user_session):
		if user_session.get('configurationMap'):
			user_configurations = {x.split("@@")[0].strip():x.split("@@")[1].strip() for x in user_session.get('configurationMap').get('item', [])}
			if user_configurations.get('email'):
				return user_configurations.get('email')
		return ""

	def _get_users_call(self, user_session):
		if user_session.get('configurationMap'):
			user_configurations = {x.split("@@")[0].strip():x.split("@@")[1].strip() for x in user_session.get('configurationMap').get('item', [])}
			if user_configurations.get('common_users'):
				return user_configurations.get('common_users').split(";")
		return ""

	def _get_write_comments(self, user_session):
		if user_session.get('configurationMap'):
			user_configurations = {x.split("@@")[0].strip():x.split("@@")[1].strip() for x in user_session.get('configurationMap').get('item', [])}
			if user_configurations.get('write_comments'):
				return True
		return ""

	def _is_fuerte_group_hotel(self, hotel_code):

		return 'fuerte' in hotel_code or 'amare' in hotel_code or 'olee' in hotel_code

	def _hide_comments(self, hotel):
		return get_config_property_value(hotel, HIDE_COMMENTS_CALLSEEKER)

	def _get_prebooking_config(self, hotel):
		prebooking_config = getWebConfiguration(hotel, PREBOOKING)

		params = {}
		if prebooking_config and prebooking_config.get('enable_multiprebooking'):
			user_session = session_utils.get_session_user()
			params['prebooking_enabled'] = True
			if prebooking_config.get('hide_multiprebooking'):
				params['hide_multiprebooking'] = True
			else:
				params['hide_multiprebooking'] = False

			params['hide_chk_block_price'] = False
			if prebooking_config.get('user_allow_to_block_price'):
				users = prebooking_config.get('user_allow_to_block_price')
				if user_session.get('name') in users.split(";"):
					params['hide_chk_block_price'] = False

			params['hide_chk_block_availability'] = False
			if prebooking_config.get('hide_chk_block_availability'):
				params['hide_chk_block_availability'] = True

		return params

	def _get_zendesk_config(self, hotel):
		zendesk_config = getWebConfiguration(hotel, ZENDESK)

		if zendesk_config:
			return True

		return False


	def _get_puschtech_config(self, hotel):
		puschtech_config = getWebConfiguration(hotel, PUSHTECH)

		if puschtech_config:
			return True

		return False

	def _get_fideltour_config(self, hotel):
		fideltour_config = getWebConfiguration(hotel, FIDELTOUR)

		if fideltour_config:
			return True

		return False

	def _get_only_adults_config(self, hotel):
		return get_config_property_value(hotel, ONLY_ADULTS)


	def get(self, hotel_id):

		hotel = hotel_manager_utils.get_hotel_by_id(hotel_id)
		user_session = session_utils.get_session_user()
		translations = get_web_dictionary()

		internal_url = hotel_manager_utils.build_url(hotel['applicationId'], hotel['url'])

		hotel_url = get_config_property_value(hotel, DOMINIO_BOOKING)

		if not hotel_url or get_config_property_value(hotel, CUSTOM_PATH_BOOKING):
			hotel_url = internal_url

		#if Config.DEV:
			#hotel_url = 'http://localhost:9090'

		room_index_list = self._get_rooms_selector_options(hotel)
		user_permissions = _get_permissions_by_user()

		show_hide_payment_gateway = self._show_chk_hide_payment(hotel, user_session)
		show_ignore_min_stay = self._ignore_min_stay(hotel, user_session)

		context = {
			'booking_url': hotel_url + "/booking1",
			'agent_id': user_session['name'],
			'agent_email': self._get_email(user_session),
			'ageSelection': self._ageSelector(hotel),
			'kids_age_range': self._get_kids_age_range(hotel),
			'babies_age_range': self._get_babies_age_range(hotel),
			'show_babies_age_selector': self._show_babies_age_selector(hotel),
			'showBabies': self._show_babies(hotel),
			'showPets': self._show_pets(hotel),
			'show_mobile_offers': self._show_mobile_offers(hotel),
			'show_limit_user_reservation': self._show_limit_user_reservation(hotel),
			'show_quote_email': self._show_quote_email(hotel),
			'show_users_club': self._show_users_club(hotel),
			'vacation_club': get_vacation_club_config(hotel['applicationId']),
			'ignore_room_availability': self._ignore_room_availability(hotel, user_session),
			'hotel_url': hotel_url,
			'internal_url': internal_url,
			'namespace': hotel['applicationId'],
			'countries_client': self._countrySelector(hotel),
			'languages_client': self._languageSelector(hotel),
			'users': self._get_users_call(user_session),
			'show_perfil_club_amigos': self._is_fuerte_group_hotel(hotel['applicationId']),
			'write_comments': self._get_write_comments(user_session),
			'hide_comments': self._hide_comments(hotel),
			'room_index_list': room_index_list,
			'prebooking_config': self._get_prebooking_config(hotel),
			'booking_spa': get_config_property_value(hotel, BOOKING_SPA),
			'zendesk_enabled': self._get_zendesk_config(hotel),
			'puschtech_enabled': self._get_puschtech_config(hotel),
			'fideltour_enabled': self._get_fideltour_config(hotel),
			'promocode_list': self._get_promocode_list(hotel),
			'only_adults': self._get_only_adults_config(hotel),
			'breadcrumbs_enabled': get_config_property_value(hotel, BREADCRUMBS_CALLSEEKER),
			'book_yesterday': get_config_property_value(hotel, BOOK_YESTERDAY),
			'related_hotels': get_config_property_value(hotel, NO_AVAILABILITY_REDIRECT),
			'hotel_groups': self.get_hotel_groups(hotel, SPANISH),
			'is_ring2travel_user': is_ring2travel_user(),
			'permissions': user_permissions,
			'disable_booking_scroll': get_config_property_value(hotel, DISABLE_BOOKING_SCROLL),
			'show_ignore_min_stay' : show_ignore_min_stay,
			'show_hide_payment_gateway' :  show_hide_payment_gateway,
			'lite_version': get_config_property_value(hotel, CALL_CENTER_LITE_VERSION),
			'reload_button': get_config_property_value(hotel, CALL_CENTER_RELOAD_BUTTON)
		}

		if is_flight_hotel_active(hotel):
			context['flight_hotel_data'] = AIRPORTS_DATA

		logging.info(f"Contexto para hotel {hotel['applicationId']} - show_limit_user_reservation: {context['show_limit_user_reservation']}")

		if is_master_agent_user():
			# Use session to avoid not necessary calls to datastore
			available_users = session_manager.get(USER_MASTERED_USERS)
			if not available_users:
				# If not info available on session, we need to get them again
				available_users = get_mastered_users(user_session)
			context['master_agents_selector'] = available_users

		default_promocode = get_config_property_value(hotel, DEFAULT_PROMOCODE)
		context['default_promocode'] = default_promocode if default_promocode else ""

		if context.get("booking_spa"):
			context['show_mobile_offers'] = True
			context['hide_comments'] = True

			hotel_url = get_config_property_value(hotel, DOMINIO_BOOKING)

			if not hotel_url:
				hotel_url = get_config_property_value(hotel, DOMINIO_ASOCIADO)

			context['booking_url'] = f"{hotel_url}/es/"

		context.update(translations)
		content = build_template_jinja('templates/booking_widget', 'booking_widget.html', context)

		response = make_response(content, 200)
		response.headers['Content-Type'] = 'text/html'

		return response

	def _get_promocode_list(self, hotel):
		if not get_config_property_value(hotel, CALLCENTER_PROMOCODE_SELECTOR):
			return []

		return getPromocodes(hotel)

	def get_hotels(self, hotel, language):
		hotel_code = hotel['applicationId']
		all_sections = deepcopy(get_all_websections(hotel_code))
		hotels = [x for x in all_sections if x.get('sectionType') == INDIVIDUAL_HOTEL and x.get('enabled')]

		if not hotels:
			hotels = [x for x in all_sections if x.get('sectionType') == INDIVIDUAL_ROOM and x.get('enabled')]

		for hotel_section in hotels:
			_set_language_web_page_properties(hotel_code, hotel_section, language, [])

		hotels = [x for x in hotels if x.get('namespace')]

		return hotels

	def get_hotel_groups(self, hotel, language):
		hotels = self.get_hotels(hotel, language)
		groups = {}

		for hotel_section in hotels:
			hotel_namespace = hotel_section.get('namespace')
			if hotel_namespace:
				groups.setdefault('all', []).append(hotel_namespace)

				if hotel_section.get('destiny'):
					groups.setdefault('destinations', {}).setdefault(hotel_section.get('destiny'), []).append(hotel_namespace)

				if hotel_section.get('country'):
					groups.setdefault('countries', {}).setdefault(hotel_section.get('country'), []).append(hotel_namespace)

		groups_config = getWebConfiguration(hotel, CALLCENTER_HOTEL_GROUPS)
		if groups_config:
			for group_name, group_hotels in groups_config.items():
				groups.setdefault('custom', {})[group_name] = group_hotels.split(';')

		# Sort by name
		for key in ['destinations', 'countries', 'custom']:
			if groups.get(key):
				groups[key] = dict(sorted(groups[key].items()))

		return groups
