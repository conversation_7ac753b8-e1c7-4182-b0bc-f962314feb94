class VacationClubConfig(object):
    """
    Model that represents the configurations of the Vacation Club functionality

    This model is used to set default values and specify types for the configuration values.
    """

    active: bool = False
    adapter_url: str  # URL of the adapter for the vacation club. This is a mandatory field.
    api_key: str | None = None  # API key for the external service.
