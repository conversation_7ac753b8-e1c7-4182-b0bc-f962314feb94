from google.appengine.ext.remote_api import remote_api_stub
from google.appengine.ext import testbed
TEST_USER = ""
TEST_PASSWORD = ""


def auth_func():
	return TEST_USER, TEST_PASSWORD

def connect_to_remote_datastore(appUrl):

	remote_api_stub.ConfigureRemoteApi(None, '/_ah/remote_api', auth_func, appUrl)

	# First, create an instance of the Testbed class.
	myTestBed = testbed.Testbed()

	# Then activate the testbed, which prepares the service stubs for use.
	myTestBed.activate()

	# Next, declare which service stubs you want to use.
	myTestBed.init_blobstore_stub()
	myTestBed.init_logservice_stub()

