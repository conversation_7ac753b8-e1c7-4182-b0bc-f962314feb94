
from google.appengine.ext import testbed
import unittest
import webapp2
import webtest
import jinja2
import django
from bunch import bunchify
from datetime import datetime

import os

from paraty.tools.callUtils import _post_reservations, _initialize_reservations_results, _initialize_calls_results

xml = """<CallsXml xmlns="https://call-seeker.appspot.com" Version="1.0">
    
        <Call>
            <src>961347474</src>
            <start>2019-08-19 10:19:09</start>
            <duration>116</duration>
            <billsec>89</billsec>
            <status>ANSWERED</status>
            <client>Central Reservas CASUAL</client>
            <dst>204</dst>
            
            <fails>
                
                <fail>
                    <dst>205</dst>
                    <status>BUSY</status>
                </fail>
                
                <fail>
                    <dst>202</dst>
                    <status>NO ANSWER</status>
                </fail>
                
            </fails>
            
            <agent>Agent4</agent>
            <agent_name>Ezequiel</agent_name>
        </Call>
    
        <Call>
            <src>961347474</src>
            <start>2019-08-19 10:21:15</start>
            <duration>189</duration>
            <billsec>93</billsec>
            <status>ANSWERED</status>
            <client></client>
            <dst>202</dst>
            
            <fails>
                
                <fail>
                    <dst>205</dst>
                    <status>BUSY</status>
                </fail>
                
                <fail>
                    <dst>204</dst>
                    <status>NO ANSWER</status>
                </fail>
                
            </fails>
            
            <agent>Agent2</agent>
            <agent_name>Vero</agent_name>
        </Call>
    
        <Call>
            <src>963286644</src>
            <start>2019-08-19 11:00:16</start>
            <duration>133</duration>
            <billsec>115</billsec>
            <status>ANSWERED</status>
            <client>Onhotel Oceanfront Matalascanas - Premiu</client>
            <dst>204</dst>
            
            <fails>
                
                <fail>
                    <dst>202</dst>
                    <status>NO ANSWER</status>
                </fail>
                
            </fails>
            
            <agent>Agent4</agent>
            <agent_name>Ezequiel</agent_name>
        </Call>
    
        <Call>
            <src>963449400</src>
            <start>2019-08-19 13:36:17</start>
            <duration>115</duration>
            <billsec>55</billsec>
            <status>ANSWERED</status>
            <client>Casual Teatro</client>
            <dst>202</dst>
            
            <fails>
                
                <fail>
                    <dst>205</dst>
                    <status>BUSY</status>
                </fail>
                
                <fail>
                    <dst>204</dst>
                    <status>NO ANSWER</status>
                </fail>
                
            </fails>
            
            <agent>Agent2</agent>
            <agent_name>Vero</agent_name>
        </Call>
    
        <Call>
            <src>967603545</src>
            <start>2019-08-19 22:07:27</start>
            <duration>376</duration>
            <billsec>230</billsec>
            <status>ANSWERED</status>
            <client>Casual Teatro</client>
            <dst>204</dst>
            
            <fails>
                
                <fail>
                    <dst>205</dst>
                    <status>BUSY</status>
                </fail>
                
            </fails>
            
            <agent>Agent4</agent>
            <agent_name>Ezequiel</agent_name>
        </Call>
    
        <Call>
            <src>972458231</src>
            <start>2019-08-19 17:26:28</start>
            <duration>474</duration>
            <billsec>456</billsec>
            <status>ANSWERED</status>
            <client>Yoy hoteles - Premium</client>
            <dst>202</dst>
            
            <fails>
                
                <fail>
                    <dst>204</dst>
                    <status>NO ANSWER</status>
                </fail>
                
            </fails>
            
            <agent>Agent2</agent>
            <agent_name>Vero</agent_name>
        </Call>
    
        <Call>
            <src>976301523</src>
            <start>2019-08-19 09:46:22</start>
            <duration>156</duration>
            <billsec>90</billsec>
            <status>ANSWERED</status>
            <client>Casual Vintage </client>
            <dst>202</dst>
            
            <fails>
                
                <fail>
                    <dst>205</dst>
                    <status>BUSY</status>
                </fail>
                
                <fail>
                    <dst>204</dst>
                    <status>NO ANSWER</status>
                </fail>
                
            </fails>
            
            <agent>Agent2</agent>
            <agent_name>Vero</agent_name>
        </Call>
    
        <Call>
            <src>977326116</src>
            <start>2019-08-19 15:52:59</start>
            <duration>85</duration>
            <billsec>64</billsec>
            <status>ANSWERED</status>
            <client>SNO Hoteles</client>
            <dst>204</dst>
            
            <fails>
                
                <fail>
                    <dst>202</dst>
                    <status>NO ANSWER</status>
                </fail>
                
            </fails>
            
            <agent>Agent4</agent>
            <agent_name>Ezequiel</agent_name>
        </Call>
    
        <Call>
            <src>977353033</src>
            <start>2019-08-19 09:25:02</start>
            <duration>720</duration>
            <billsec>697</billsec>
            <status>ANSWERED</status>
            <client>Cadena Ohtels - Premium</client>
            <dst>202</dst>
            
            <fails>
                
                <fail>
                    <dst>204</dst>
                    <status>NO ANSWER</status>
                </fail>
                
            </fails>
            
            <agent>Agent2</agent>
            <agent_name>Vero</agent_name>
        </Call>
    
        <Call>
            <src>977353033</src>
            <start>2019-08-19 09:31:09</start>
            <duration>135</duration>
            <billsec>91</billsec>
            <status>ANSWERED</status>
            <client>Cadena Ohtels - Premium</client>
            <dst>202</dst>
            
            <fails>
                
                <fail>
                    <dst>204</dst>
                    <status>NO ANSWER</status>
                </fail>
                
            </fails>
            
            <agent>Agent2</agent>
            <agent_name>Vero</agent_name>
        </Call>
    
        <Call>
            <src>977353033</src>
            <start>2019-08-19 15:42:49</start>
            <duration>243</duration>
            <billsec>223</billsec>
            <status>ANSWERED</status>
            <client>Cadena Ohtels - Premium</client>
            <dst>204</dst>
            <fails>
                <fail>
                    <dst>202</dst>
                    <status>NO ANSWER</status>
                </fail>
            </fails>
            <agent>Agent4</agent>
            <agent_name>Ezequiel</agent_name>
        </Call>
    
        <Call>
            <src>977353033</src>
            <start>2019-08-19 17:40:45</start>
            <duration>339</duration>
            <billsec>321</billsec>
            <status>ANSWERED</status>
            <client>Cadena Ohtels - Premium</client>
            <dst>204</dst>
            
            <fails>
                <fail>
                    <dst>202</dst>
                    <status>NO ANSWER</status>
                </fail>
            </fails>
            
            <agent>Agent4</agent>
            <agent_name>Ezequiel</agent_name>
        </Call>
    
        <Call>
            <src>983409732</src>
            <start>2019-08-19 19:51:22</start>
            <duration>384</duration>
            <billsec>224</billsec>
            <status>ANSWERED</status>
            <client>Casual Teatro</client>
            <dst>202</dst>
            <fails>
                <fail>
                    <dst>205</dst>
                    <status>BUSY</status>
                </fail>
                <fail>
                    <dst>202</dst>
                    <status>NO ANSWER</status>
                </fail>
            </fails>
            
            <agent>Agent2</agent>
            <agent_name>Vero</agent_name>
        </Call>
    
</CallsXml>"""

AGENTS_TEST = [{'code':'agente1','Name':'Ezequiel','Description':'Agente 1','Extension':201},
               {'code':'agente2','Name':'Reme','Description':'Agente 2','Extension':202},
               {'code':'agente3','Name':'Vero','Description':'Agente 3','Extension':203},
               {'code':'agente4','Name':'Maria','Description':'Agente 4','Extension':204},
               {'code':'agente5','Name':'Fran','Description':'Agente 5','Extension':205}]


class TestReportCalls(unittest.TestCase):

    testbed = None
    testapp = None

    def setUp(self):

        os.environ["APPENGINE_RUNTIME"] = "python27"
        os.environ['SERVER_SOFTWARE'] = "Development/"

        # from django.conf import settings
        # os.environ.setdefault("DJANGO_SETTINGS_MODULE", "test_report_calls_handler.settings")
        # django.setup()

        from routes import route_list
        from config import app_config

        # create a WSGI application.
        self.app = webapp2.WSGIApplication(route_list, config=app_config)
        self.testapp = webtest.TestApp(self.app, extra_environ={'REMOTE_ADDR': '127.0.0.1'})
        return

    def testPushCalls(self):

        self.setUp()

        self.testbed = testbed.Testbed()

        self.testbed.activate()
        self.testbed.init_datastore_v3_stub()
        self.testbed.init_memcache_stub()
        # self.testbed.init_urlfetch_stub()
        # self.testbed.init_taskqueue_stub()
        self.testbed.init_mail_stub()
        # self.testbed.init_user_stub()

        response = self.testapp.post('/reports_call/push_call', params=xml)

        # activate GAE stubs

    def testRetrieveCalls(self):

        self.setUp()

        self.testbed = testbed.Testbed()

        self.testbed.activate()
        self.testbed.init_datastore_v3_stub()
        self.testbed.init_memcache_stub()
        self.testbed.init_urlfetch_stub()
        self.testbed.init_taskqueue_stub()
        self.testbed.init_mail_stub()
        # self.testbed.init_user_stub()

        response = self.testapp.get('/reports_call/daily?date=2019-08-26')


    def testPostReservations(self):

        self.setUp()

        self.testbed = testbed.Testbed()

        self.testbed.activate()
        self.testbed.init_datastore_v3_stub()
        self.testbed.init_memcache_stub()
        self.testbed.init_urlfetch_stub()
        self.testbed.init_taskqueue_stub()
        self.testbed.init_mail_stub()
        # self.testbed.init_user_stub()

        agents = bunchify(AGENTS_TEST)

        calls_agent =_initialize_calls_results(agents)

        reservations_agent = _initialize_reservations_results(agents)

        _post_reservations(agents, reservations_agent, (datetime.strptime('2019-01-01', '%Y-%m-%d')))

        for reservations in reservations_agent:

            reservations = filter(lambda x: datetime.strptime(x.get('timestamp'), "%Y-%m-%d %H:%M:%S").month == 8 and
                                            datetime.strptime(x.get('timestamp'), "%Y-%m-%d %H:%M:%S").day == 26, reservations)

            pass





        response = self.testapp.get('/reports_call/daily?date=2019-08-26')

        # activate GAE stubs







