const MAP_FIDELTOUR = {"name":"name",
    "country":"country",
    "email":"email",
    "phone": "phone1"}

const FideltourController = function () {
    return {
        config: {
            client_active_class: '.tab_client_content.active',
            hotel_active_class: '.tab_client_content.active .hoteltabs-content .hoteltab.active',
            fideltour_class: '.fideltour_container',
            selected_clients: {},
            users: {},
            users_per_page: 10,
            dev_client_id: '16276060951444',
            dev: false,

        },

        init: function () {
            const fideltourContainer = $(this.config.fideltour_class);

            $('body').on('shown.bs.tab', '.hoteltabs-container.nav.nav-tabs', this.requestFideltourInfoToActiveHotel);

            const formInputs = fideltourContainer.find('.inputs_wrapper input');
            formInputs.on('focus', function () {
                $(this).closest('.input_wrapper').addClass('active');
            });
            formInputs.on('change', FideltourController.checkInputs);
            formInputs.on('blur', FideltourController.checkInputs);

            window.addEventListener('message', function (event) {
                const actions = {
                    'fideltour_info': FideltourController.toggleFideltourActivation
                };

                actions[event.data.message] && actions[event.data.message](event);
            }, false);

            fideltourContainer.find('.new_client_btn').click(function () {
                const fideltourWrapper = $(this).closest(FideltourController.config.fideltour_class);
                fideltourWrapper.find('.new_client_btn').removeClass('active');
                $(this).addClass('active');

                const isNewClient = $(this).data('new_client') === true;
                fideltourWrapper.toggleClass('new_client', isNewClient);
            });

            $('.fideltour_form input[name="email"]').on('input', function() {
                const clientTab = $(this).closest('.tab_client_content');
                const value = $(this).val();
                const iframeContent = clientTab.find('.booking_engine_iframe_wrapper iframe').contents();

                iframeContent.find('.user_email_selection').addClass('active');
                iframeContent.find('#user_email').val(value);
            });
            window.parent.document.getElementById('infoPadre');

            fideltourContainer.find('#div_select_register').click(this.handleSearchSelectUser);
            fideltourContainer.find('.close_popup').click(this.handleClosePopup);

            fideltourContainer.find('.clear_search_btn_fideltour[name=fideltour]').click(this.handleClearSearch);
            fideltourContainer.find('.cancel_user_btn').click(() => this.handleCancelSelectedUser(FideltourController.getClientTabActive()));
            fideltourContainer.find('.pagination .prev-page').click(() => this.handlePageNavigation('prev'));
            fideltourContainer.find('.pagination .next-page').click(() => this.handlePageNavigation('next'));
            //}
        },

        getClientTabActive: function () {
            return $(this.config.client_active_class);
        },

        getActiveHotelTab: function () {
            return $(this.config.hotel_active_class);
        },

        requestFideltourInfoToActiveHotel: function () {
            const hotelTab = FideltourController.getActiveHotelTab();
            const message = {
                message: "request_Fideltour_info"
            };

            hotelTab.find('.booking_engine_iframe_wrapper iframe')[0].contentWindow.postMessage(message, '*');
        },

        sendActiveClientIdHotelIframe: function (hotelIframe) {
            const clientTab = hotelIframe.closest('.tab_client_content');
            if (!FideltourController.config.selected_clients[clientTab.attr('id')]) {
                return;
            }
            const message = {
                message: "fideltour_client_selected",
                client_id: FideltourController.config.selected_clients[clientTab.attr('id')]
            };

            hotelIframe[0].contentWindow.postMessage(message, '*');
        },

        toggleFideltourActivation: function (event) {
            const iframeSender = $(event.source.frameElement);
            const clientTab = iframeSender.closest('.tab_client_content');
            event.data.fideltour_enabled
                ? clientTab.find(FideltourController.config.fideltour_class).addClass('active')
                : clientTab.find(FideltourController.config.fideltour_class).removeClass('active');
            if (event.data.fideltour_enabled && event.data.first_load) {
                FideltourController.sendActiveClientIdHotelIframe(iframeSender);
            }

            const fideltourEmail = clientTab.find('.fideltour_form input[name="email"]').val();
            if (fideltourEmail) {
                const iframeContent = iframeSender.contents();
                iframeContent.find('.user_email_selection').addClass('active');
                iframeContent.find('#user_email').val(fideltourEmail);
            }
        },

        checkInputs: function () {
            const target_wrapper = $(this).closest('.input_wrapper');
            if ($(this).val()) {
                target_wrapper.addClass('active');
            } else {
                target_wrapper.removeClass('active');
            }
        },

        toggleLoader: function (btn, show) {
            if (show) {
                btn.addClass('hide');
                $('.search_loader').show();
            } else {
                btn.removeClass('hide');
                $('.search_loader').hide();
            }
        },
        handleSearchSelectUser: function () {
            $(".page-container").find("#popup_register")[0].showModal();
        },
        handleClosePopup: function (){
            $(".page-container").find("#popup_register")[0].close();
        },

        handleClearSearch: function (e) {
            if(confirm("Se reiniciara la busqueda ¿Esta seguro de que NO quiere terminar la reserva?")){
                $(this).closest('.fideltour_container').find('.input_wrapper').removeClass('active');
                $(this).closest('.fideltour_container').find('.user_element').not('.fideltour_user_template').remove();
                $(this).closest('.fideltour_container').find('.pagination').removeClass('active');

                const clientTab = FideltourController.getClientTabActive();
                FideltourController.handleCancelSelectedUser(clientTab);
            }else{
                e.preventDefault();
            }
        },

        handleSelectUser: function () {
            const questionMessage = 'Se asociará la pestaña de cliente al usuario y se reiniciarán las búsquedas realizadas \n\n¿Desea continuar?';
            if (!window.confirm(questionMessage)) {
                return;
            }

            const client_id = $(this).closest('.user_element').data('client_id');
            $(this).closest('.fideltour_results').find('.user_element').removeClass('selected');
            $(this).closest('.user_element').addClass('selected');

            const clientTab = FideltourController.getClientTabActive();
            const message = {
                message: "fideltour_client_selected",
                client_id: client_id
            };

            clientTab.find('.booking_engine_iframe_wrapper iframe').each(function () {
                this.contentWindow.postMessage(message, '*');
            });

            FideltourController.config.selected_clients[clientTab.attr('id')] = client_id;
        },

        handleCancelSelectedUser: function (clientTab) {
            clientTab.find('.fideltour_container .user_element.selected').removeClass('selected');

            const message = {
                message: "fideltour_client_cancel"
            };

            clientTab.find('.booking_engine_iframe_wrapper iframe').each(function () {
                this.contentWindow.postMessage(message, '*');
            });

            FideltourController.config.selected_clients[clientTab.attr('id')] = null;
        },

        getUserInfo: function (form) {
            return form.find('.user_field').toArray().reduce((userInfo, field) => {
                if (field.value) {
                    userInfo[field.name] = field.value;
                }

                return userInfo;
            }, {});
        },

        handlePageNavigation: function (action) {
            const clientTab = FideltourController.getClientTabActive();
            const paginationContainer = clientTab.find(`.fideltour_container .pagination`);
            const activePage = paginationContainer.find('.page_button.active');
            const pageNumber = +activePage.data('page');
            const totalPages = paginationContainer.find('.page_button').length;

            if (action === 'prev' && pageNumber > 1) {
                FideltourController.changePage(clientTab, pageNumber - 1);
            } else if (action === 'next' && pageNumber < totalPages) {
                FideltourController.changePage(clientTab, pageNumber + 1);
            }
        },

        changePage: function (clientTab, pageNumber) {
            const paginationContainer = clientTab.find(`.fideltour_container .pagination`);
            paginationContainer.find('.page_button').removeClass('active');
            paginationContainer.find(`.page_button[data-page="${pageNumber}"]`).addClass('active');

            const users = FideltourController.config.users[clientTab.attr('id')];
            const usersToCreate = users.slice((pageNumber - 1) * FideltourController.config.users_per_page, pageNumber * FideltourController.config.users_per_page);
            FideltourController.createUserElements(clientTab, usersToCreate);
        },

        createPagination: function (clientTab, users) {
            const totalPages = Math.ceil(users.length / FideltourController.config.users_per_page);
            const paginationContainer = clientTab.find(`.fideltour_container .pagination`);
            paginationContainer.find('.pages').empty();

            if (totalPages > 1) {
                for (let i = 1; i <= totalPages; i++) {
                    const pageButton = $(`<span class="page_button ${i === 1 ? 'active' : ''}" data-page="${i}">${i}</span>`);
                    pageButton.click(() => FideltourController.changePage(clientTab, i));
                    paginationContainer.find('.pages').append(pageButton);
                }

                paginationContainer.addClass('active');
            } else {
                paginationContainer.removeClass('active');
            }
        },

        createUserElements: function (clientTab, users) {
            const userTemplate = clientTab.find('.fideltour_user_template');
            const userContainer = clientTab.find('.fideltour_results');

            const userElements = users.map(user => {
                const userElement = userTemplate.clone(true, true);
                userElement.removeClass('fideltour_user_template');
                userElement.attr('data-client_id', user.client_id);

                Object.entries(user).forEach(([key, value]) => {
                    userElement.find(`.user_${key} .user_field`).val(value);
                });

                return userElement;
            });

            userContainer.find('.user_element').not('.fideltour_user_template').remove();
            userContainer.prepend(userElements);
        },

        validForm: function (form) {
            const validator = form.validate({
                rules: {
                    name: {
                        required: true
                    },
                    email: {
                        required: true,
                        email: true
                    }
                },
                messages: {
                    name: {
                        required: "El nombre es obligatorio"
                    },
                    email: {
                        required: "El correo electrónico es obligatorio",
                        email: "Por favor, ingresa un correo electrónico válido"
                    }
                },
            });

            return validator.form();
        },

        sendRequest: function (endpoint_url, data) {
            const params = {
                sessionKey: $('#session_id').val(),
                ...data
            };

            const options = {
                method: 'POST',
                mode: 'cors',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(params)
            };

            return fetch(endpoint_url, options).then(response => {
                try {
                    if (response.headers.get("Content-Type") === 'application/json') {
                        return response.json();
                    }

                    return response;
                } catch (error) {
                    return response;
                }
            });
        }
    };
}();
$(document).ready(function(){
        function pullValue() {
            let hotel_code = $(".booking_engine_iframe_wrapper iframe").contents().find('[name="namespace"]').val()
            let url = `/fideltour/get_contact?hotel_code=${hotel_code}&email=${this.value}`;
            $.ajax({
                url: url,
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({}),
                success: function (response) {
                    console.log('Respuesta exitosa:', response);
                    if (Object.keys(response.user).length != 0) {
                        $('.fideltour_form .inputs_wrapper .input_wrapper input, select.user_field[name="country"]').each(function (i, e) {
                            if (MAP_FIDELTOUR[e.name]) {
                                e.value = response.user[MAP_FIDELTOUR[e.name]];
                                $(e).closest('div.input_wrapper').addClass('active');
                            }
                        });
                    }
                },
                error: function (xhr, status, error) {
                    console.error('Error:', error);
                }
            });
        }

        $(".fideltour_form .inputs_wrapper .input_wrapper input").each(function (i, e) {
            if (e.name.includes("email")) {
                $(e).change(pullValue);
            }
        });
});