var headers = {
    identifier: 'Identifier',
    start_date: 'start_date',
    end_date: 'end_date',
    price: 'price',
    cancellationTimestamp: 'cancellationTimestamp',
    timestamp: 'timestamp',
};

var fileTitle = 'bookings';

$(function () {
    const booking_form = $('#searchForm');

    // Datepicker to use when picker fails on mobile
    if (!booking_form.find('#datepickerCallcenter').length) {
        $("<input>", {
            type: "date",
            id: "datepickerCallcenter",
            css: {
                position: "absolute",
                top: "50%",
                left: "50%",
                transform: "translate(-50%, -50%)",
                opacity: 0,
                pointerEvents: "none"
            }
        }).appendTo(booking_form);
    }

    booking_form.find('.occupancy_selector').on('click', '.button_number', function (e) {
        e.preventDefault();
        let input_counter = $(this).siblings('.occupancy_counter').first();

        changeCounter(input_counter, $(this).hasClass('plus'));
    });

    $('#clear_search').on('click', function (e) {
        e.preventDefault();

        let booking_form = $('#searchForm');

        booking_form.find('#datepicker1').val('').trigger('change');
        booking_form.find('#datepicker2').val('').trigger('change');
        booking_form.find('.occupancy_counter.rooms').val('1').trigger('change');
        booking_form.find('#promocodeInput').val('').trigger('change');
        booking_form.find('#user_dni').val('');
        booking_form.find('#user_email').val('');
        booking_form.find('#fromCountry option:first').prop('selected', true);
        booking_form.find('#language option:first').prop('selected', true);
        booking_form.find('.occupancy_counter:not(.adults):not(.rooms)').val('0').trigger('change');
        booking_form.find('.occupancy_counter.adults').val('2').trigger('change');
        booking_form.find('.child_age, .baby_age').remove();
        booking_form.find('input[type="checkbox"]').prop('checked', false).trigger('change');

        $('.booking_results_wrapper iframe').attr('src', '');
        $('.booking_results_wrapper').addClass('hidden');
        $('.breadcrumbs_wrapper .step').removeClass('active').data('url', '');
    });

    booking_form.find('.occupancy_selector .occupancy_counter.children').on('change', function () {
        updateAges($(this), true);
    });

    booking_form.find('.occupancy_selector .occupancy_counter.babies').on('change', function () {
        updateAges($(this), false);
    });

    booking_form.find('.occupancy_counter.rooms').on('change', function () {
        showRooms($(this).val());
    });

    booking_form.find('.occupancy_counter').on('change', function () {
        updateOccupancyDisplay();
    });

    booking_form.find('.occupancy_display').on('click', function () {
        booking_form.find('.occupancy_selector').toggleClass('active');
    });

    booking_form.find('.group_search .yes_no_checkbox .yes_no_checkbox_option').on('click', function () {
        let value = $(this).attr('data-value'),
            target_input = $(this).closest('.group_search_checkbox_wrapper').find('#group_search');

        target_input.prop('checked', value === 'yes');
    });

    $('#show_comments').on('click', function () {
        var agentId = $("#agentId").val();
        var comments = $("#comments").val();
        var namespace = $("#namespace").val();
        var data = {'agent_id': agentId, 'comments': comments, 'namespace': namespace};
        $.ajax({
            url: '/comments/save',
            data: JSON.stringify(data),
            type: "POST",
            contentType: "application/json",
            crossDomain: true,
            success: function (data) {
                if (data.length > 0) {
                    alert("Datos guardados correctamente");
                    $('.submit_button.comments').removeClass('active');
                }
            },
            async: false
        });
    });

    var namespace = $("#namespace").val();
    $.ajax({
        url: '/comments/get?namespace=' + namespace,
        type: "GET",
        crossDomain: true,
        success: function (data) {
            if (data.length > 0) {
                $("#comments").val(data);
            }
        },
        async: false
    });

    $("[name='booking_results']").on('load', function() {
            setTimeout(() => {
                $("[name='booking_results']").show();
            }, 1000);
    });

    $.datepicker.setDefaults($.datepicker.regional.es);

    let inputs = $('.booking_widget_wrapper input, .booking_widget_wrapper select');
    inputs.each(checkInputLabel);
    inputs.on('change', checkInputLabel);
    inputs.on('focus', function () {
        $(this).closest('.input_wrapper, .start_date, .end_date').addClass('active');
    });
    inputs.on('blur', checkInputLabel);

    var startParse = "";
    var endParse = "";

    $("#datepicker1").val(startParse);
    $("#datepicker2").val(endParse);

    let min_date = new Date();

    if ($('#book_yesterday').length) {
        min_date.setDate(min_date.getDate() - 1);
    }

    $("#datepicker1").datepicker({
        minDate: min_date,
        onClose: function (selectedDate) {
            if (selectedDate) {
                var theDate = $.datepicker.parseDate("dd/mm/yy", selectedDate);
                theDate.setDate(theDate.getDate() + 1);
                $("#datepicker2").datepicker("option", "minDate", theDate);

                setTimeout(function () {
                    $("#datepicker2").trigger('focus');
                    $("#datepicker2").datepicker('show');
                }, 100);
            }
        }
    });

    $("#datepicker2").datepicker({
        minDate: new Date()
    });

    $("#datepicker3").datepicker({
        onClose: function (selectedDate) {
            if (selectedDate) {
                var theDate = $.datepicker.parseDate("dd/mm/yy", selectedDate);
                theDate.setDate(theDate.getDate() + 1);
                $("#datepicker4").datepicker("option", "minDate", theDate);
            }
        }
    });

    $("#datepicker4").datepicker({
        minDate: new Date()
    });

    $("#datepicker1").on('change', function () {
        var date2 = $('#datepicker1').datepicker('getDate', '+3d');
        if (date2) {
            date2.setDate(date2.getDate() + 1);
            $('#datepicker2').datepicker('setDate', date2);
            $('#datepicker2').trigger('change');
            checkDates();
        }

    });

    $("#datepicker2").on('change', checkDates);

    $("#datepicker1").removeAttr('disabled');
    $("#datepicker2").removeAttr('disabled');

    $('.booking_widget_wrapper .comments_wrapper textarea').on('input', function () {
        $('.booking_widget_wrapper .submit_button.comments').addClass('active');
    });

    $('.multi_prebooking_block button').click(function () {
        let is_preview = $(this).hasClass('preview');

        if (!$('.multi_prebooking_block input#email').val() && !is_preview) {
            $('.multi_prebooking_block .email_selection').addClass('error');
            return false;
        }

        $('.multi_prebooking_block .email_selection').removeClass('error');
        $('.multi_prebooking_block .button_wrapper').hide();
        $('#multi_prebooking_loading').show();

        let parentMessage = {};
        if (is_preview) {
            parentMessage = {
                message: "multi_prebooking_preview_clicked"
            };
        } else {
            parentMessage = {
                message: "multi_prebooking_clicked",
                email: $('.multi_prebooking_block input#email').val(),
                block_availability: $('.multi_prebooking_block input#block_availability').is(':checked'),
                block_price: $('.multi_prebooking_block input#block_price').is(':checked')
            };
        }

        window.parent.postMessage(parentMessage, '*');
    });

    window.iframeAlreadyResized = false;
    window.addEventListener('message', function (event) {
        const actions = {
            'booking_info': resendBookingInfo,
            'resize_iframe': resizeIframes,
            'get_current_position': getCurrentPosition,
            'set_popup_position': setPopupPosition,
            'request_booking_info': requestBookingInfo,
            'request_zendesk_info': sendZendeskInfo,
            'request_pushtech_info': sendPushtechInfo,
            'request_fideltour_info': sendFideltourInfo,
            'request_vacation_club_info': sendVacationClubInfo,
            'zendesk_client_selected': handleZendeskClientSelected,
            'pushtech_client_selected': handlePushtechClientSelected,
            'fideltour_client_selected': handleFideltourClientSelected,
            'vacation_club_client_selected': handleVacationClubClientSelected,
            'zendesk_client_cancel': handleZendeskClientCancel,
            'pushtech_client_cancel': handlePushtechClientCancel,
            'fideltour_client_cancel': handleFideltourClientCancel,
            'vacation_club_client_cancel': handleVacationClubClientCancel,
            'reactivate_prebooking_btn': activatePrebookingBtn,
            'booking0_search_click': () => window.parent.postMessage(event.data, '*'),
            'open_datepicker': openDatepicker
        };
        if (actions[event.data.message]) {
            actions[event.data.message](event);
        }
    }, false);

    const breadcrumbElement = $('.breadcrumbs_wrapper');
    if (breadcrumbElement.length) {
        breadcrumbElement.addClass('active');

        breadcrumbElement.find('.step').on('click', function () {
            const url = $(this).data('url');
            if (url) {
                $('.booking_results_wrapper iframe').attr('src', url);
            }
        });
    }

    $('.booking_results_wrapper iframe').on('load', function () {
        setTimeout(function () {
            if (window.iframeAlreadyResized) {
                return;
            }

            resizeIframes({data: {height: 1600}});
            window.iframeAlreadyResized = true;
        }, 1000);

        if (breadcrumbElement.length) {
            window.addEventListener('message', function (event) {
                if (event.data.message !== 'booking_info') {
                    return;
                }

                const step = event.data.step;
                const completeUrl = event.data.complete_url;
                if (completeUrl) {
                    const bookingUrl = new URL(completeUrl);

                    if (step === 'booking3') {
                        bookingUrl.searchParams.set('keepCookieData', 'true');
                    }

                    const target_step = breadcrumbElement.find(`.step.${step}`);
                    target_step.data('url', bookingUrl.href);
                    target_step.addClass('active');
                    target_step.nextAll().removeClass('active').data('url', '');
                }
            }, false);

            requestBookingInfo();
        }
    });

    sendZendeskInfo(null, true);
    sendPushtechInfo(null, true);
    sendFideltourInfo(null, true);

    if ($('#booking_spa').length) {
        $("select[name=fromCountry]").on("change", function () {
            var original_action = $(".search_form").data("original-action");

            original_action += "?force_geo=" + $(this).find("option:selected").val().toUpperCase();
            $(".search_form").attr("action", original_action);
        });
    }

    if ($('#limitUser').length) {
        $('#limitUser').on('change', function() {
            $('#limit_user_reservation').val($(this).is(':checked'));
        });
    }

    listenFormChanges();
});

function changeCounter(counter, increment = false) {
    let current_value = parseInt(counter.val()) || 0,
        min_value = parseInt(counter.attr('min')) || 0,
        max_value = parseInt(counter.attr('max')) || 99;

    if (increment && current_value < max_value) {
        counter.val(++current_value);
    } else if (!increment && current_value > min_value) {
        counter.val(--current_value);
    }

    counter.trigger('change');
}

function updateAges(counter, children) {
    let current_value = parseInt(counter.val()) || 0,
        ages_min = parseInt(counter.attr('data-ages-min')) || 0,
        ages_max = parseInt(counter.attr('data-ages-max')) || 99,
        ages_wrapper = counter.closest('.room_selection').find(`.${(children) ? 'children' : 'babies'}_age_wrapper`),
        age_selectors = ages_wrapper.find((children) ? '.child_age' : '.baby_age'),
        current_age_selectors_count = age_selectors.length;

    if (ages_wrapper.length) {
        if (current_value < current_age_selectors_count) {
            age_selectors.slice(current_value - current_age_selectors_count).remove();
        } else if (current_value > current_age_selectors_count) {
            for (let i = 0; i < current_value - current_age_selectors_count; i++) {
                ages_wrapper.append(buildAgeSelector(children, current_age_selectors_count + i + 1, ages_min, ages_max));
            }
        }
    }
}

function buildAgeSelector(children, index, ages_min, ages_max) {
    let age_selector_wrapper = $('<div></div>', {
            class: (children) ? 'child_age' : 'baby_age'
        }),
        age_label = $('<div></div>', {
            class: 'label_title',
            text: `Edad ${(children) ? 'niño' : 'bebe'} ${index}:`
        }),
        input_wrapper = $('<div></div>', {
            class: `${(children) ? 'child' : 'baby'}_button_age`
        }),
        btn_minus = $('<button></button>', {
            class: 'button_number minus',
            html: '<i class="fal fa-minus"></i>'
        }),
        btn_plus = $('<button></button>', {
            class: 'button_number plus',
            html: '<i class="fal fa-plus"></i>'
        }),
        input_counter = $('<input>', {
            class: `occupancy_counter ${(children) ? 'child' : 'baby'}_input_age`,
            type: 'number',
            value: ages_min,
            min: ages_min,
            max: ages_max
        });

    input_counter.on('input', function () {
        const minValue = $(this).attr('min');
        const maxValue = $(this).attr('max');
        const enteredValue = +$(this).val();

        if (isNaN(enteredValue)) {
            $(this).val(minValue);
        } else if (enteredValue < minValue) {
            $(this).val(minValue);
        } else if (enteredValue > maxValue) {
            $(this).val(maxValue);
        }
    });

    input_wrapper.append(btn_minus).append(input_counter).append(btn_plus);
    age_selector_wrapper.append(age_label).append(input_wrapper);

    return age_selector_wrapper;
}

function showRooms(numRooms) {
    const max_rooms = $('.occupancy_selector .room_selection').length;

    for (let i = 1; i <= max_rooms; i++) {
        if (i <= numRooms) {
            $('.occupancy_selector .room_selection.room_' + i).show();
        } else {
            $('.occupancy_selector .room_selection.room_' + i).hide();
            $(`.occupancy_selector .room_selection.room_${i} .occupancy_counter.children`).val('0');
            $(`.occupancy_selector .room_selection.room_${i} .occupancy_counter.babies`).val('0');
            $(`.occupancy_selector .room_selection.room_${i} .children_age_wrapper`).html('');
        }
    }

    resizeParentDocument();
}

function updateOccupancyDisplay() {
    const numRooms = +$('.occupancy_counter.rooms').val();
    let numAdults = 0;
    let numChildren = 0;
    let numBabies = 0;

    for (let i = 1; i <= numRooms; i++) {
        numAdults = numAdults + parseInt($(`.room_selection.room_${i} .occupancy_counter.adults`).val());
        numChildren = numChildren + parseInt($(`.room_selection.room_${i} .occupancy_counter.children`).val());
        numBabies = numBabies + parseInt($(`.room_selection.room_${i} .occupancy_counter.babies`).val());
    }

    if ($(`.room_selection.room_1 .occupancy_counter.children`).val()) {
        $('.occupancy_selection .occupancy_display').html(`${numAdults}/${numChildren}`);

    } else if ($(`.room_selection.room_1 .occupancy_counter.babies`).val()) {
        $('.occupancy_selection .occupancy_display').html(`${numAdults}/${numChildren}/${numBabies}`);

    } else {
        $('.occupancy_selection .occupancy_display').html(`${numAdults}`);
    }
}

function checkPersonClub() {
    var nivel = $("#perfilClubAmigos").val().split("/")[0];
    var idpersona = $("#perfilClubAmigos").val().split("/")[1];
    $.ajax({
        url: 'https://fuerte-adapter.appspot.com/get_persona_club?idpersona=' + idpersona,
        method: 'GET',
        crossDomain: true,
        success: function (data) {
            if (data.length > 0) {
                $.each(data, function (i, datainfo) {

                    var nivel_real = datainfo["nivel/idCliente"].split("/")[0];
                    if (nivel !== nivel_real) {
                        alert("¡Atención, el nivel de club de este cliente es: " + nivel_real + "\nNo puede introducir un nivel distinto. Se utilizará el nivel correspondiente");
                        $("#perfilClubAmigos").val(nivel_real + "/" + idpersona);
                    }

                });
            }

        },
        async: false
    });
}

function requestEmail() {
    let hotelUrl = '';
    const internalUrl = $('#internal_url');
    if(internalUrl.length){
        hotelUrl = internalUrl.val();
    }
    $.get(`${hotelUrl}/send-confirmation/`, {
        id: $("#emailIdentifier").val(),
        email: $("#emailEmail").val(),
        type: 'customer',
        from_manager2: true
    }).done(function () {
            alert("Email enviado");
        }
    );
}

var checkDates = function () {

    var date1_str = $("#datepicker1").val();
    var date2_str = $("#datepicker2").val();

    return !(date1_str === "" || date2_str === "");
};

function checkInputLabel() {
    let target_wrapper = $(this).closest('.input_wrapper, .start_date, .end_date');
    if ($(this).val()) {
        target_wrapper.addClass('active');
    } else {
        target_wrapper.removeClass('active');
    }
}

function getCurrentPosition(event) {
    if (event.data.window !== 'booking_widget') return getParentCurrentPosition();

    const bookingResultsIframe = $('.booking_results_wrapper iframe')[0];
    const scrollTop = window.scrollY || document.documentElement.scrollTop;

    const childrenMessage = {
        message: 'set_popup_position',
        top: scrollTop,
    };

    bookingResultsIframe.contentWindow.postMessage(childrenMessage, '*');
}

function getParentCurrentPosition(event) {
    const parentMessage = {
        message: 'get_current_position'
    };

    window.parent.postMessage(parentMessage, '*');
}

function setPopupPosition(event) {
    const top = event.data.top;
    if (!top) return;

    const bookingResultsIframe = $('.booking_results_wrapper iframe')[0];
    const searchFormHeight = $('#searchForm .search_form_block').outerHeight(true);

    const childrenMessage = {
        message: 'set_popup_position',
        top: top - searchFormHeight,
    };

    bookingResultsIframe.contentWindow.postMessage(childrenMessage, '*');
}

function resizeIframes(event) {
    let height = event.data.height;
    if (!height) return;

    // Needed because on react apps initial height is too small. We set as default a Notebook height
    if(height < 1000) height = 1600;

    window.iframeAlreadyResized = true;
    const bookingResultsIframe = $('.booking_results_wrapper iframe');
    const heightToAdd = (typeof height !== 'string') ? `${height + 50}px` : height;
    bookingResultsIframe.css('height', heightToAdd);

    //bookingResultsIframe.attr('scrolling', 'no');

    resizeParentDocument();
}

function openDatepicker(event) {
    const $input = $('#datepickerCallcenter');

    if (!$input.length) return;

    $input.click();
    $input[0].showPicker();

    const bookingResultsIframe = $('.booking_results_wrapper iframe')[0];

    $input.off("change").on("change", function () {
        const selectedDate = $(this).val();
        const childrenMessage = {
            message: 'selected_date',
            date: selectedDate,
        };
        bookingResultsIframe.contentWindow.postMessage(childrenMessage, "*");
    });
}

function reloadBookingIframe() {
    const bookingResultsIframe = $('.booking_results_wrapper iframe')[0];
    const childrenMessage = {
        message: 'reload_window'
    };

    bookingResultsIframe.contentWindow.postMessage(childrenMessage, '*');
}

function sendZendeskInfo(event, first_load = false) {
    const parentMessage = {
        message: "zendesk_info",
        zendesk_enabled: $('#zendesk_enabled').length,
        first_load: first_load
    };

    window.parent.postMessage(parentMessage, '*');
}

function sendPushtechInfo(event, first_load = false) {
    const parentMessage = {
        message: "pushtech_info",
        puschtech_enabled: $(".puschtech_enabled").length,
        first_load: first_load
    };

    window.parent.postMessage(parentMessage, '*');
}

function sendPushtechImport(event, first_load = false) {
    const parentMessage = {
        message: "send_data",
        puschtech_enabled: $(".puschtech_enabled").length,
        first_load: first_load
    };

    window.parent.postMessage(parentMessage, '*');
}

function sendFideltourInfo(event, first_load = false) {
    const parentMessage = {
        message: "fideltour_info",
        fideltour_enabled: $(".fideltour_enabled").length,
        first_load: first_load
    };

    window.parent.postMessage(parentMessage, '*');
}

function sendVacationClubInfo(event, first_load = false) {
    debugger;
    const parentMessage = {
        message: "vacation_club_info",
        vacation_club_enabled: $("#vacation_club_enabled").length,
        adapter_url: $('#vacation_club_adapter_url').val(),
        first_load: first_load
    };

    window.parent.postMessage(parentMessage, '*');
}

function handleZendeskClientSelected(event) {
    if (!$('#zendesk_enabled').length) {
        return;
    }

    const bookingForm = $('.search_form .search_form_block');
    const extraData = {
        zendesk_id: event.data.client_id
    };

    const extraInfoInput = '<input type="hidden" name="extra_info_reservation" value=' + JSON.stringify(extraData) + '>';
    bookingForm.find('input[name="extra_info_reservation"]').remove();
    bookingForm.prepend(extraInfoInput);

    if (window.searchLaunched) {
        $('.search_form').trigger('submit');
    }
}

function handlePushtechClientSelected(){
    if (!$('#pushtech_enabled').length) {
        return;
    }

    const bookingForm = $('.search_form .search_form_block');
    const extraData = {
        zendesk_id: event.data.client_id
    };

    const extraInfoInput = '<input type="hidden" name="extra_info_reservation" value=' + JSON.stringify(extraData) + '>';
    bookingForm.find('input[name="extra_info_reservation"]').remove();
    bookingForm.prepend(extraInfoInput);

    if (window.searchLaunched) {
        $('.search_form').trigger('submit');
    }
}

function handleFideltourClientSelected(){
    if (!$('#fideltour_enabled').length) {
        return;
    }

    const bookingForm = $('.search_form .search_form_block');
    const extraData = {
        zendesk_id: event.data.client_id
    };

    const extraInfoInput = '<input type="hidden" name="extra_info_reservation" value=' + JSON.stringify(extraData) + '>';
    bookingForm.find('input[name="extra_info_reservation"]').remove();
    bookingForm.prepend(extraInfoInput);

    if (window.searchLaunched) {
        $('.search_form').trigger('submit');
    }
}

function handleVacationClubClientSelected(event) {
    if (!$('#vacation_club_enabled').length) return;

    const bookingForm = $('.search_form .search_form_block');
    const extraData = {
        member_id: event.data.id,
        member_email: event.data.email
    };

    for (const [param, value] of Object.entries(extraData)) {
        bookingForm.prepend(`<input type="hidden" name="vacation_club_${param}" value="${value}">`);
    }

    if (window.searchLaunched) {
        $('.search_form').trigger('submit');
    }
}

function handlePushtechClientCancel() {
    if (!$('#pushtech_enabled').length) {
        return;
    }
        const bookingForm = $('.search_form .search_form_block');
    bookingForm.find('input[name="extra_info_reservation"]').remove();

    if (window.searchLaunched) {
        $('.search_form').trigger('submit');
    }
}

function handleFideltourClientCancel() {
    if (!$('#fideltour_enabled').length) {
        return;
    }
        const bookingForm = $('.search_form .search_form_block');
    bookingForm.find('input[name="extra_info_reservation"]').remove();

    if (window.searchLaunched) {
        $('.search_form').trigger('submit');
    }
}

function handleZendeskClientCancel() {
    if (!$('#zendesk_enabled').length) {
        return;
    }

    const bookingForm = $('.search_form .search_form_block');
    bookingForm.find('input[name="extra_info_reservation"]').remove();

    if (window.searchLaunched) {
        $('.search_form').trigger('submit');
    }
}

function handleVacationClubClientCancel() {
    if (!$('#vacation_club_enabled').length) {
        return;
    }

    const bookingForm = $('.search_form .search_form_block');
    const vacationClubParams = ['member_id', 'member_email'];

    for (const param of vacationClubParams) {
        bookingForm.find(`input[name="vacation_club_${param}"]`).remove();
    }

    if (window.searchLaunched) {
        $('.search_form').trigger('submit');
    }
}

function resizeParentDocument() {
    const parentMessage = {
        message: 'resize_iframe'
    };
    window.parent.postMessage(parentMessage, '*');
}

function requestBookingInfo() {
    const bookingResultsIframe = $('.booking_results_wrapper iframe')[0];
    const message = {
        message: 'request_booking_info'
    };

    bookingResultsIframe.contentWindow.postMessage(message, '*');
}

function resendBookingInfo(event) {
    if (ZendeskTicketsController.config.booking_info_requested) {
        return;
    }

    const isPrebookingEnabled = $('#prebooking_enabled').length === 1;

    const parentMessage = {
        message: 'booking_info',
        hotel_code: $('#namespace').val(),
        prebooking_enabled: isPrebookingEnabled,
        session_id: event.data.sid,
        step: event.data.step,
        personal_details: event.data.personal_details
    };

    window.parent.postMessage(parentMessage, '*');
}

function activatePrebookingBtn() {
    $('#multi_prebooking_loading').hide();
    $('.multi_prebooking_block .button_wrapper').show();
}

function handleLiteVersionSwitch(switchContainer) {
    const active = switchContainer.find('.checkbox').is(':checked');
    const targetInput = $('#active_lite_version');
    const bookingForm = $(`.booking_widget_wrapper #searchForm`);
    if(active){
        if(targetInput.length) return;
        bookingForm.append($(`<input type="hidden" name="active_lite_version" id="active_lite_version" value="call_seeker_lite" />`));
    } else if(targetInput.length){
        bookingForm.find(targetInput).remove();
    }
}

function listenFormChanges() {
    $("#searchForm").find("select, input, textarea").change(function() {
       $("#changes-pending").removeClass('hidden');
    });
}

function handleMobileVersion() {
    let is_mobile = $('input#forceMobile').is(":checked");
    if (is_mobile) {
        $('.booking_results_wrapper').addClass('mobile');
    } else {
        $('.booking_results_wrapper').removeClass('mobile');
    }
}


function bookingSearch(button) {
    const booking_form = $(button).closest('#searchForm');
    const group_search = booking_form.find('#group_search').is(':checked');
    const bookingResultsIframe = $('.booking_results_wrapper iframe');

    $('.booking_results_wrapper').removeClass('hidden');

    /*if (!checkDates()) {
        return;
    }*/

    if (bookingResultsIframe.data('disable-scroll')) {
        document.cookie = 'disable_booking_scroll=true; path=/';
    } else {
        document.cookie = 'disable_booking_scroll=; expires=Thu, 01-Jan-70 00:00:01 GMT; path=/';
    }
    initProgressBar();

    let related_hotels = $(button).attr('data-related-hotels');
    if (related_hotels) {
        booking_form.attr('action', booking_form.attr('action').replace('booking1', 'booking0'));
        booking_form.find('#applicationIds').val(related_hotels);
    } else if (group_search) {
        let selected_hotels_group = booking_form.find('#group_select').val();
        booking_form.attr('action', booking_form.attr('action').replace('booking1', 'booking0'));
        booking_form.find('#applicationIds').val(selected_hotels_group);
    } else {
        booking_form.attr('action', booking_form.attr('data-original-action'));
        booking_form.find('#applicationIds').val(booking_form.find('#applicationIds').attr('data-original-value'));
    }

    let num_rooms_selected = $(".occupancy_counter.rooms").val();
    num_rooms_selected = parseInt(num_rooms_selected) || 1;

    for (let i = 1; i <= num_rooms_selected; i++) {
        let ages_kid = [12, 12, 12, 12];
        $(`#room_${i} .child_input_age`).each(function (index) {
            ages_kid[index] = parseInt($(this).val());
        });

        let target_input = $(`#agesKid${i}`);
        if (!target_input.length) {
            target_input = $(`<input type="hidden" name="agesKid${i}" id="agesKid${i}" />`);
            $(`.booking_widget_wrapper form`).append(target_input);
        }
        $(`#agesKid${i}`).val(ages_kid.join(";"));
    }

    for (let i = 1; i <= num_rooms_selected; i++) {
        let ages_baby = [];
        $(`#room_${i} .baby_input_age`).each(function (index) {
            ages_baby[index] = parseInt($(this).val());
        });

        let target_input = $(`#agesBaby${i}`);
        if (!target_input.length) {
            target_input = $(`<input type="hidden" name="agesBaby${i}" id="agesBaby${i}" />`);
            $(`.booking_widget_wrapper form`).append(target_input);
        }
        $(`#agesBaby${i}`).val(ages_baby.join(";"));
    }

    const lite_version_switch = $('.lite_switch_wrapper');
    if(lite_version_switch.length){
        handleLiteVersionSwitch(lite_version_switch)
    }


    if ($('#perfilClubAmigos').length) {
        checkPersonClub();
    }

    $(button).addClass('disabled');
    setTimeout(function () {
        $(button).removeClass('disabled');
    }, 5000);

    $('.occupancy_selector').removeClass('active');

    handleMobileVersion();
    $("#changes-pending").addClass('hidden');

    window.searchLaunched = true;
    try {
        $("iframe[name='booking_results']").load(import_data);
    }catch (e){
        console.log("Error importando datos: ", e);
    }
}

function import_data(){
    sendPushtechImport();
}

function initProgressBar() {
    const progressBar = $(`.progress.hotel_search`);
    const progressBarStatus = $(`.progress.hotel_search .progress-bar`);
    progressBar.show();

    let width = 0;
    const interval = setInterval(function () {
        width = width + 1;
        if (width > 90) {
            clearInterval(interval);
            return;
        }

        progressBarStatus.css('width', width + '%');
    }, 40);

    $('.booking_results_wrapper iframe').on('load', function () {
        hideProgressBar(interval);
    });
}

function hideProgressBar(intervalProgressBar) {
    clearInterval(intervalProgressBar);
    const progressBar = $(`.progress.hotel_search`);
    const progressBarStatus = $(`.progress.hotel_search .progress-bar`);

    setTimeout(function () {
        progressBarStatus.css('width', '100%');
    }, 500);

    setTimeout(function () {
        progressBar.hide();
        progressBarStatus.css('width', '0');
    }, 1000);
}

function isString(variable) {
    return typeof (variable) === 'string';
}

function lastReplace(input, find, replaceWith) {
    if (!isString(input) || !isString(find) || !isString(replaceWith)) {
        // returns input on invalid arguments
        return input;
    }

    const lastIndex = input.lastIndexOf(find);
    if (lastIndex < 0) {
        return input;
    }

    return input.substr(0, lastIndex) + replaceWith + input.substr(lastIndex + find.length);
}

function convertToCSV(objArray) {
    var array = typeof objArray !== 'object' ? JSON.parse(objArray) : objArray;
    var str = '';

    for (var i = 0; i < array.length; i++) {
        var line = '';
        for (var index in array[i]) {
            if (line !== '') {
                line += ',';
            }

            line += array[i][index];
        }

        str += line + '\r\n';
    }

    return str;
}

function exportCSVFile(headers, items, fileTitle) {
    if (headers) {
        items.unshift(headers);
    }

    // Convert Object to JSON
    var jsonObject = JSON.stringify(items);

    var csv = this.convertToCSV(jsonObject);

    var exportedFilenmae = fileTitle + '.csv' || 'export.csv';

    var blob = new Blob([csv], {type: 'text/csv;charset=utf-8;'});
    if (navigator.msSaveBlob) { // IE 10+
        navigator.msSaveBlob(blob, exportedFilenmae);
    } else {
        var link = document.createElement("a");
        if (link.download !== undefined) { // feature detection
            // Browsers that support HTML5 download attribute
            var url = URL.createObjectURL(blob);
            link.setAttribute("href", url);
            link.setAttribute("download", exportedFilenmae);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
    }
}

function getListReservationsByUser() {
    var user_id = $("[name=user_call]").val();
    var user_start_list = $("[name=startDateList]").val();
    var user_end_list = $("[name=endDateList]").val();
    var namespace = $("[name=namespace]").val();

    var startDay = user_start_list.substring(0, 2);
    var startMonth = user_start_list.substring(3, 5);
    var startYear = user_start_list.substring(6, 10);
    var startDate = startYear + "-" + startMonth + "-" + startDay;

    var endDay = user_end_list.substring(0, 2);
    var endMonth = user_end_list.substring(3, 5);
    var endYear = user_end_list.substring(6, 10);
    var endDate = endYear + "-" + endMonth + "-" + endDay;

    var action = lastReplace($('#searchForm').prop('action'), "booking1", "");

    $.ajax({
        url: action + 'utils/?action=get_reservations&user_id=' + user_id + '&user_start_list=' + startDate + '&user_end_list=' + endDate + '&namespace=' + namespace,
        method: 'GET',
        crossDomain: true,
        success: function (data) {
            if (data.length > 0) {
                var result = JSON.parse(data);
                var itemsFormatted = [];
                result.forEach((item) => {
                    itemsFormatted.push({
                        booking_id: item.identifier,
                        start_date: item.start_date,
                        end_date: item.end_date,
                        price: item.price,
                        cancellationTimestamp: item.cancellationTimestamp,
                        timestamp: item.timestamp
                    });
                });

                exportCSVFile(headers, itemsFormatted, fileTitle);

            }

        },
        async: false
    });
}