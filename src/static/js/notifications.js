var Notifications = function () {

    return {

        addNotification: function(notificationObj) {
            var template =      '<li>' +
                                    '<a href="%LINK%">' +
                                        '<span class="label label-sm label-icon label-%TYPE%">' +
                                            '%LOGO%' +
                                        '</span>' +
                                        '%TEXT%' +
                                    '</a>' +
                                '</li>';


            var logo = notificationObj.logo;
            if (!notificationObj.isSiteIcon) {
                logo = '<i class="fa fa-' + notificationObj.logo + '"></i>'
            } else {
                logo = '<img class="flag" width="16px" height="16px" src="/static/images/logos/' + notificationObj.logo + '.ico">&nbsp;';
            }

            var notificationText = template.replace(/%LINK%/, notificationObj.link)
                            .replace(/%TYPE%/, notificationObj.type)
                            .replace(/%TEXT%/, '&nbsp;&nbsp;' + notificationObj.text)
                            .replace(/%LOGO%/, logo);

            var current_text = $('#notificationsContainer').html();
            $('#notificationsContainer').html(notificationText + current_text);

            var current_num = $('#numNotificationsContainer').html();
            var num_notifications = 1;
            if (current_num.length != 0) {
                current_num = parseInt(current_num);
                num_notifications = num_notifications + current_num;
            }
            $('#numNotificationsContainer').html("" + num_notifications);
            $('#textGeneralNotificationsContainer').html("Tiene " + num_notifications + " notificaciones.");
            $('#iconNotificationsContainer').removeClass('fa-bell');
            $('#iconNotificationsContainer').addClass('fa-warning');
        },

        reset: function() {
            $('#notificationsContainer').html('');
            $('#numNotificationsContainer').html('');
            $('#textGeneralNotificationsContainer').html($('#textGeneralNotificationsContainer').attr('data-default-text'));
            $('#iconNotificationsContainer').addClass('fa-bell');
        },

        getNotifications: function() {
            $.get('/api/notifications',
                function(results) {
                    Notifications.reset();
                    $.each(results, function(index, notificationObj) {
                       console.log(notificationObj);
                       Notifications.addNotification(notificationObj);
                    });

//                    window.setTimeout(function () {
//                        Notifications.getNotifications();
//                    }, 300000);

                }
            ).fail(function() {
                console.log('Error getting notifications');
            });
        }
    };
}();

