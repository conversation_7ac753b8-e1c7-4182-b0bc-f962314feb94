var HotelsInfo = function () {
    return {
        init: function() {
            tabCounter = 0;
            App.updateMenu('menuCallCenter', 'hotels-info');
            HotelsInfo.createTemplateClientTab(1);
            HotelsInfo.initClientTabs();
            HotelsInfo.initMenuListeners();
            HotelsInfo.initBookingMessageEvent();
            //HotelsInfo.initKeepParamsToggle();
        },

        configs: {
            automaticSearch: false
        },

        initEditor: function(id) {
            $('#' + id).wysihtml5({
                "stylesheets": ["/static/template/assets/plugins/bootstrap-wysihtml5/wysiwyg-color.css"]
            });

            var ed = $('#editor0').data("wysihtml5").editor;
            ed.observe("load", function () {
                var $iframe = $(this.composer.iframe);
                var $body = $(this.composer.element);

                $body
                  .css({
                    'min-height': 0,
                    'line-height': '20px',
                    'overflow': 'hidden'
                  })
                  .bind('keypress keyup keydown paste change focus blur', function(e) {
                    var height = Math.min($body[0].scrollHeight, $body.height());
                    // a little extra height to prevent spazzing out when creating newlines
                    var extra = e.type == "blur" ? 0 : 20 ;
                    var total = height + extra;
                    if (total < 600) total = 1000;
                    $iframe.height(total);
                });
            });
        },

        initHotelsSelector: function(clientTabId) {
             $(`#client_${clientTabId} .hotels_selector`).select2({
                allowClear: true,
                closeOnSelect: false,
                escapeMarkup: function (m) {
                    return m;
                }
            });
        },

        getLastClientIdUsed: function() {
            return Math.max.apply(null, $('.client_tabs_wrapper .tab_client').map(function() {
                return parseInt($(this).data('tab'));
            }).get());
        },

        initClientTabs: function() {
            $('.client_tabs_wrapper .add_client_tab').click(function() {
                if ($(this).hasClass('disabled')) {
                    return null;
                }

                const tabClientCounter = HotelsInfo.getLastClientIdUsed() + 1;
                const defaultText = `${$('#client_default_text').val()} ${tabClientCounter}`;
                const tabTemplate = `<div id="tab_client_${tabClientCounter}" class="tab_client" data-tab="${tabClientCounter}" onclick="HotelsInfo.activateClientTab(event, this)"><input type="text" class="tab" placeholder="${defaultText}" value="${defaultText}"/> <i class="fal fa-times remove_tab clickable" onclick="HotelsInfo.removeClientTab(this)"></i></div>`;
                //$('.client_tabs_wrapper .tabs').append(tabTemplate);
                $('.client_tabs_wrapper .tabs').children().not(".tab_client_arrow").last().after(tabTemplate);
                $('.client_tabs_wrapper .tab_client').removeClass('active');
                $('#clienttabs-content .tab_client_content').removeClass('active');
                $(`#client_${tabClientCounter}`).addClass('active');
                $(`.client_tabs_wrapper #tab_client_${tabClientCounter}`).addClass('active');

                if ($('.client_tabs_wrapper .tab_client').length === 5) {
                    //$(this).addClass('disabled');
                    let all_taps = $("div[id^='tab_client_'].tab_client");
                    let taps_visible = $("div[id^='tab_client_'].tab_client:not(.no_visible)");
                    taps_visible.first().addClass("no_visible");
                    taps_visible.first().hide();
                    let arrows_left = `
                        <div id="tab_client_first" class="tab_client_arrow active" onclick="moveTabs(this,'first')" style="    width: 42px;
                        display: flex;
                        justify-content: center;
                        align-items: center;">
                        <img src="/static/images/pushtech/angles-left-solid.svg" style="width: 58%"/></div>
                        
                        <div id="tab_client_left" class="tab_client_arrow active" onclick="moveTabs(this,-1)" style="    width: 42px;
                        display: flex;
                        justify-content: center;
                        align-items: center;">
                        <img src="/static/images/pushtech/angle-left-solid.svg" style="width: 58%"/></div>`;
                    let arrows_right = `
                        <div id="tab_client_right" class="tab_client_arrow active" onclick="moveTabs(this,1)" style="    width: 42px;
                        display: flex;
                        justify-content: center;
                        align-items: center;">
                        <img src="/static/images/pushtech/chevron-right-solid.svg" style="width: 58%"/></div>
                    
                        <div id="tab_client_last" class="tab_client_arrow active" onclick="moveTabs(this,'last')" style="    width: 42px;
                        display: flex;
                        justify-content: center;
                        align-items: center;">
                        <img src="/static/images/pushtech/angles-right-solid.svg" style="width: 58%"/></div>`;

                    all_taps.first().before(arrows_left);
                    all_taps.last().after(arrows_right);
                } else if ($('.client_tabs_wrapper .tab_client').length < 5){
                    $(".tab_client_arrow").remove();
                    let all_taps = $("div[id^='tab_client_'].tab_client");
                    all_taps.first().removeClass("no_visible");
                    all_taps.first().show();
                }else if ($('.client_tabs_wrapper .tab_client').length > 5 ){
                    moveTabs("","last")
                }

                /*if ($('.client_tabs_wrapper .tab_client').length > 4) {
                    const tabsWrapper = $(".tabs.owl-carousel");

                    tabsWrapper.addClass('carousel_active');
                    tabsWrapper.owlCarousel({
                        navigation : true,
                        items : 4,
                        nav: false,
                    });
                }*/
                HotelsInfo.createTemplateClientTab(tabClientCounter);
                HotelsInfo.updateMenuItems();
            });

            $('.client_tabs_wrapper .tab_client').click(function(event) {
                HotelsInfo.activateClientTab(event, $(this));
                HotelsInfo.updateMenuItems();
            });

            $('.client_tabs_wrapper .tab_client .remove_tab').click(function() {
                HotelsInfo.removeClientTab(this);
                HotelsInfo.updateMenuItems();
            });
        },

        createTemplateClientTab: function(tabClientCounter) {
            const newTabTemplate = $('#template_client_tab .tab_client_content').clone();
            const newFideltourTemplate = $('#fideltour_container_template').clone(true, true);
            newFideltourTemplate.removeAttr('id');
            newTabTemplate.find('.fideltour_wrapper').append(newFideltourTemplate);
            const newPushtechTemplate = $('#pushtech_container_template').clone(true, true);
            newPushtechTemplate.removeAttr('id');
            newTabTemplate.find('.pushtech_wrapper').append(newPushtechTemplate);
            const newZendeskTemplate = $('#zendesk_container_template').clone(true, true);
            newZendeskTemplate.removeAttr('id');
            newTabTemplate.find('.zendesk_wrapper').append(newZendeskTemplate);

            newTabTemplate.attr('id', `client_${tabClientCounter}`);
            newTabTemplate.attr('data-tab', tabClientCounter);
            newTabTemplate.addClass('active');
            $('#clienttabs-content').append(newTabTemplate);
            HotelsInfo.initHotelsSelector(tabClientCounter);
            HotelsInfo.initScrollBtnListeners();
        },

        activateClientTab: function(event, tab) {
            if ($(event.target).hasClass('remove_tab')) {
                return null;
            }

            $('.client_tabs_wrapper .tab_client').removeClass('active');
            $(tab).addClass('active');
            $('#clienttabs-content .tab_client_content').removeClass('active');
            $(`#client_${$(tab).data('tab')}`).addClass('active');
            HotelsInfo.updateMenuItems();
        },

        removeClientTab: function(button) {
            if ($('.client_tabs_wrapper .tab_client').length === 1) {
                return null;
            }

            if ($('.client_tabs_wrapper .tab_client').length < 6){
                    $(".tab_client_arrow").remove();
                    let all_taps = $("div[id^='tab_client_'].tab_client");
                    all_taps.first().removeClass("no_visible");
                    all_taps.first().show();
            }

            const tab = $(button).parent();
            const tabId = tab.data('tab');
            if (tab.hasClass('active')) {
                if (tab.prev().length) {
                    tab.prev().addClass('active');
                    $(`#client_${tabId - 1}`).addClass('active');
                } else {
                    tab.next().addClass('active');
                    $(`#client_${tabId + 1}`).addClass('active');
                }
            }

            tab.remove();
            $(`#client_${tabId}`).remove();
            $('.client_tabs_wrapper .add_client_tab').removeClass('disabled');
            refreshVision();

        },

        updateMenuItems: function() {
            App.updateMenuItems();
            App.updateActiveMenuItem();
        },

        loadNewHotel: function(selected, namespace=null) {
            const hotelId = namespace || $(selected).val();
            const clientTabId = $('.tab_client.active').data('tab');

            const activeHotelTab = $('.tab_client_content.active .hoteltabs-content .hoteltab.active');
            const bookingEngineOrigin = activeHotelTab.find(`.booking_engine_iframe_wrapper iframe`).contents().find('.booking_widget_wrapper');

            if (hotelId === '') {
                return;
            }


            const intervalProgressBar = HotelsInfo.initProgressBar(clientTabId);

            $(`#client_${clientTabId} .noHotelsMsg`).hide();
            //$('#hoteltabs_mode_toggle').show();
            var hotelName = $(`#client_${clientTabId} .hotels_selector option:selected`).text();
            HotelsInfo.addHotelInfoTab(hotelName, tabCounter, clientTabId);

            const counterTarget = tabCounter;

            $.ajax({
                url:'/api/hotel/info/' + hotelId,
                success: function(data) {
                    if (data.sessionError){
                        $(".session_alert").css("display", "block");
                        $(".response_ko").text(data.sessionError);
                        setTimeout(function(){
                            window.location.href = "/logout";
                        }, 5000);
                    }
                    //Check if the session isn't expired
                    if (typeof data !== 'object') {
                       // data.redirect contains the string URL to redirect to
                        location.reload();
                    }

                    var ficha = data.xlsUrl;
                    if (typeof ficha === 'undefined') {
                        ficha = 'javascript:HotelsInfo.addFichaToHotel("' + hotelId + '", ' + counterTarget + ');';
                    }

                     let hotel_info = {
                        tabId: counterTarget,
                        iframeSrc: data.iframeSrc,
                        ficha: ficha,
                        hotelId: hotelId,
                        webIframe: data.webIframe,
                        webIframe_offer: data.webIframeOffers,
                        webIframeReservation: data.webIframeReservation,
                        hotelCode: data.hotelCode,
                        hotelName: hotelName,
                    };

                    if (hotelName.indexOf('Fuerte') >= 0) {
                        hotel_info.webIframe_offer = "";
                    }

                    HotelsInfo.get_utils_hotel_data(hotel_info, clientTabId, counterTarget, intervalProgressBar, bookingEngineOrigin);

                    hotelData = hotel_info;

                    console.log('HotelData:');
                    console.log(hotelData);
                },
                async: true
            });

            //HotelsInfo.initEditor('editor' + tabCounter);
            tabCounter += 1;

            $(window).resize();
        },

        get_utils_hotel_data: function(hotel_info, clientTabId, counterTarget, intervalProgressBar, bookingEngineOrigin) {
            $.ajax({
                url: '/utils',
                data: {
                    action: 'get_template',
                    template: 'hotel_info'
                },
                type: 'POST',
                success: function(template) {
                    $.tmpl(template, hotel_info).appendTo(`#client${clientTabId}_${counterTarget}`);
                    HotelsInfo.updateMenuItems();
                    HotelsInfo.initBookingIframesResizeEvent(counterTarget, clientTabId);
                    setTimeout(function() {
                        $(`#client_${clientTabId} .hotels_selector`).val('');
                        $(`#client_${clientTabId} .hotels_selector`).trigger('change');
                    }, 500);

                    const newHotelTab = $(`#client${clientTabId}_${counterTarget}`);
                    newHotelTab.find(`.booking_engine_iframe_wrapper iframe`).on('load', () => HotelsInfo.onNewHotelIframeLoad(newHotelTab, clientTabId, intervalProgressBar, bookingEngineOrigin));
                },
                async: true
            });
        },

        onNewHotelIframeLoad: function (newHotelTab, clientTabId, intervalProgressBar, bookingEngineOrigin) {
            HotelsInfo.hideProgressBar(clientTabId, intervalProgressBar);

            const bookingEngineDestiny = newHotelTab.find(`.booking_engine_iframe_wrapper iframe`).contents();
            const contentWindowDestiny = newHotelTab.find(`.booking_engine_iframe_wrapper iframe`)[0].contentWindow;

            if (bookingEngineOrigin.length && bookingEngineDestiny.length) {
                bookingEngineOrigin.find('input, select').each(function () {
                    const name = $(this).attr('name');

                    if (name === 'namespace' || name === 'applicationIds') {
                        return;
                    }

                    const value = $(this).val();
                    const field = bookingEngineDestiny.find(`input[name="${name}"], select[name="${name}"]`);
                    const contentWindowField = contentWindowDestiny.$(`input[name="${name}"], select[name="${name}"]`);

                    if (name && value) {
                        if (field.hasClass('children') || field.hasClass('babies')) {
                            const room_id = field.closest('.room_selection').attr('id');

                            for (let i = 0; i < +value; i++) {
                                field.siblings('.plus').trigger('click');

                                const className = field.hasClass('children') ? 'child_age' : 'baby_age';
                                const age = bookingEngineOrigin.find(`#${room_id} .${className}:eq(${i}) input`).val();
                                bookingEngineDestiny.find(`#${room_id} .${className}:eq(${i}) input`).val(age);
                            }
                        } else {
                            field.val(value);

                            // We need to trigger the change event on the contentWindow cause jQuery trigger doesn't work on iframes
                            contentWindowField.trigger('change');
                            field.closest('.input_wrapper').addClass('active');
                        }
                    }

                    if ($(this).prop("checked")) {
                        field.prop("checked", true);
                    }
                });
            }

            bookingEngineDestiny.find('.booking_results_wrapper iframe').on('load', function() {
                $('.scroll_option[data-target="bookingProcess"]').click();
            });

            if (HotelsInfo.configs.automaticSearch) {
                bookingEngineDestiny.find('.submit_button button.btn_booking').click();
                HotelsInfo.configs.automaticSearch = false;
            }
        },

        handleBooking0SearchClicked: function (event) {
            const namespace = event.data.namespace;

            HotelsInfo.configs.automaticSearch = true;
            $(`.tab_client_content.active select.hotels_selector option[data-namespace="${namespace}"]`).prop("selected", true);
            $('.tab_client_content.active select.hotels_selector').trigger("change");
        },

        addHotelInfoTab: function(hotelName, tabId, clientTabId) {
            var tabTemplate = '<li class="hoteltab active" id="client' + clientTabId + '_tab' + tabId + '"><a id="client' + clientTabId + '_tabLink' + tabId + '" href="#client' + clientTabId + "_" + tabId + '" data-toggle="tab"><i class="fal fa-times remove_tab clickable" onclick="HotelsInfo.closeTab(' +"'"+ tabId +"'"+ ', ' + clientTabId + ')"></i>' + hotelName + '</a></li>';
            var tabTemplateContent = `<div class="hoteltab tab-pane active" id="client${clientTabId}_${tabId}">`;
            $(`#client_${clientTabId} .hoteltab`).removeClass('active').removeClass('in');
            $(`#client_${clientTabId} .hoteltabs-container`).append(tabTemplate);
            $(`#client_${clientTabId} .hoteltabs-content`).append(tabTemplateContent);
        },

        closeTab: function(tabId, clientTabId) {
            $(`#client${clientTabId}_${tabId}`).remove();
            $(`#client${clientTabId}_tab${tabId}`).remove();

            var counter = 0;
            while (counter < 20) {
                if ($(`#client${clientTabId}_tabLink${counter}`).length > 0) {
                    $(`#client${clientTabId}_tabLink${counter}`)[0].click();
                    return;
                }
                counter = counter + 1;
            }

            if (!$(`#client_${clientTabId} .hoteltabs-container .hoteltab`).length) {
                $(`#client_${clientTabId} .noHotelsMsg`).show();
                //$('#hoteltabs_mode_toggle').hide();
            }
        },

        saveHotelInfo: function(hotelId, contentId) {
              var oContent = {};
              oContentcontent = $('#' + contentId).data("wysihtml5").editor.getValue();
              $.ajax({
                    url: '/api/hotel/info/' + hotelId,
                    type: 'POST',
                    contentType : 'application/json',
                    data: JSON.stringify(oContent)
                }).done(function(data) {
                    console.log('Save successfully');
                    bootbox.alert("La información del hotel se ha guardado con éxito.");
                }).error(function() {
                    console.log('Error saving content!');
                    bootbox.alert("Se ha producido un error al guardar la ficha del hotel. Por favor, inténtalo de nuevo y si el error persiste ponte en contacto con el equipo informático.");
                });
        },

        addFichaToHotel: function(hotelId, tabCounter, clientTabId) {
            bootbox.prompt("El hotel aun no tiene ficha asignada. Por favor, introduce la url de la nueva ficha.", function(result) {
                if (result === null) {
                    return;
                } else {
                     $.ajax({
                        url:'/api/hotel/addExcel/' + hotelId + '?xls=' + result,
                        success: function(data) {
                            $(`#client_${clientTabId} .fichaLink${tabCounter}`).attr('href', result);
                        },
                        async: false}
                     );
                }
            });
        },

        initMenuListeners: function() {
            HotelsInfo.initScrollBtnListeners();

            $(window).scroll(function() {
              const scrollPosition = $(window).scrollTop();

              if (scrollPosition >= 50) {
                $('body').addClass('submenu_fixed');
                $('.scroll_menu_fixed').addClass('visible');
              } else {
                 $('body').removeClass('submenu_fixed');
                $('.scroll_menu_fixed').removeClass('visible');
              }

              HotelsInfo.checkScrollElementActive(scrollPosition);
            });

            $('.page-sidebar-wrapper .menu_block').click(function() {
                $('.page-sidebar-wrapper').toggleClass('expand');
                $('body').toggleClass('expanded_menu');
            });
        },

        initScrollBtnListeners: function() {
            $('.scroll_menu_fixed .scroll_option').unbind('click');
            $('.scroll_menu_fixed .scroll_option').click(function() {
                const disableBookingScroll = $.cookie('disable_booking_scroll');
                const iframeContent = $('.tab_client_content.active .hoteltab.active .booking_engine_iframe_wrapper iframe').contents();
                const bookingResults = iframeContent.find('.booking_results_wrapper');
                const bottomWrapper = iframeContent.find('.bottom_wrapper');

                const selectorPositions = {
                    bookingConfig: 0,
                    bookingProcess: bookingResults.length && !disableBookingScroll ? bookingResults.offset().top + 250 : null,
                    advanceOptions: bottomWrapper.length ? bottomWrapper.offset().top : null
                };

                const target = $(this).data('target');
                const targetPosition = selectorPositions[target];

                if (!targetPosition && targetPosition !== 0) return;

                $('html, body').animate({
                    scrollTop: targetPosition
                }, 'slow');
            });

            $('.scroll_menu_fixed .reset_search_btn').unbind('click');
            $('.scroll_menu_fixed .reset_search_btn').click(function() {
                const iframeContent = $('.tab_client_content.active .hoteltab.active .booking_engine_iframe_wrapper iframe').contents();
                const bookingResults = iframeContent.find('.booking_results_wrapper');

                if (!bookingResults.length) {
                    return;
                }

                $('html, body').animate({
                    scrollTop: 0
                }, 'slow', function() {
                    iframeContent.find('#clear_search').click();
                    bookingResults.find('iframe').attr('src', '');
                    bookingResults.addClass('hidden');
                });
            });
        },

        checkScrollElementActive: function(scrollPosition) {
            const iframeContent = $('.tab_client_content.active .hoteltab.active .booking_engine_iframe_wrapper iframe').contents();
            const bookingResults = iframeContent.find('.booking_results_wrapper');
            const bottomWrapper = iframeContent.find('.bottom_wrapper');

            if (!bookingResults.length || !bottomWrapper.length) {
                return;
            }

            $('.scroll_menu_fixed .scroll_option').removeClass('active');
            if (scrollPosition < bookingResults.offset().top) {
                $('.scroll_menu_fixed .scroll_option[data-target="bookingConfig"]').addClass('active');
            } else if (scrollPosition >= bookingResults.offset().top && scrollPosition < bottomWrapper.offset().top - 300) {
                $('.scroll_menu_fixed .scroll_option[data-target="bookingProcess"]').addClass('active');
            } else if (scrollPosition >= bottomWrapper.offset().top - 300) {
                $('.scroll_menu_fixed .scroll_option[data-target="advanceOptions"]').addClass('active');
            }
        },

        initProgressBar: function(clientTabId) {
            const progressBar = $(`#client_${clientTabId} .progress.hotel_selection`);
            const progressBarStatus = $(`#client_${clientTabId} .progress.hotel_selection .progress-bar`);
            progressBar.show();

            let width = 0;
            const interval = setInterval(function() {
                width = width + 1;
                if (width > 90) {
                    clearInterval(interval);
                    return;
                }

                progressBarStatus.css('width', width + '%');
            }, 20);

            return interval;
        },

        hideProgressBar: function(clientTabId, intervalProgressBar) {
            clearInterval(intervalProgressBar);
            const progressBar = $(`#client_${clientTabId} .progress.hotel_selection`);
            const progressBarStatus = $(`#client_${clientTabId} .progress.hotel_selection .progress-bar`);

            setTimeout(function() {
                progressBarStatus.css('width', '100%');
            }, 500);

            setTimeout(function() {
                progressBar.hide();
                progressBarStatus.css('width', '0');
            }, 1000);
        },

        // Resize iframes to avoid scrollbars
        initBookingIframesResizeEvent: function (tab, clientTabId) {
            const bookingEngineIframe = $(`#client${clientTabId}_${tab} .booking_engine_iframe_wrapper iframe`);
            let originalHeightIframe;

            if (bookingEngineIframe.length > 0) {
                //When booking widget iframe is loaded, resize it
                bookingEngineIframe.load(function() {
                    originalHeightIframe = bookingEngineIframe[0].contentWindow.document.body.scrollHeight;
                    bookingEngineIframe.css('height', originalHeightIframe + 'px');
                });
            }
        },

        initBookingMessageEvent: function() {
            window.addEventListener('message', function(event) {
                const actions = {
                    'resize_iframe': HotelsInfo.resizeIframe,
                    'get_current_position': HotelsInfo.getCurrentPosition,
                    'booking0_search_click': HotelsInfo.handleBooking0SearchClicked,
                    'limit_user_reservation': () => session_manager.set('limitUserReservation', $('#limit_user_reservation').val())
                };

                if (actions[event.data.message]) {
                    actions[event.data.message](event);
                }
            }, false);
        },

        resizeIframe: function(event) {
            const iframeSender = event.source.frameElement;
            iframeSender.style.setProperty('height', '');
            const iframeContent = iframeSender.contentWindow.document;
            const contentHeight = iframeContent.documentElement.scrollHeight;
            iframeSender.style.setProperty('height', contentHeight + 'px');
        },

        getCurrentPosition: function (event) {
            const bookingWidgetIframe = $('.booking_engine_iframe_wrapper iframe')[0];
            const menuHeight = $('.scroll_menu_fixed.visible:visible').outerHeight(true);
            const scrollTop = window.scrollY || document.documentElement.scrollTop;

            const childrenMessage = {
                message: 'set_popup_position',
                top: scrollTop + menuHeight,
            };

            bookingWidgetIframe.contentWindow.postMessage(childrenMessage, '*');
        },
    };
}();
function moveTabs(e, index){
    let all_taps = $("div[id^='tab_client_'].tab_client");
    let taps_visible = $("div[id^='tab_client_'].tab_client:not(.no_visible)");
    let tap_active= $("div[id^='tab_client_'].tab_client.active");
    if (typeof(index) =='string'){
        if (index === 'last'){
            all_taps.addClass("no_visible");
            all_taps.hide();

            tap_active.removeClass("active");
            all_taps.last().click();

            let last_tap = all_taps.slice(-4);
            last_tap.removeClass("no_visible");
            last_tap.show();
        }
        else if (index === 'first'){
            all_taps.addClass("no_visible");
            all_taps.hide();

            tap_active.removeClass("active");
            all_taps.first().click();

            let first_tap = all_taps.slice(0,4);
            first_tap.removeClass("no_visible");
            first_tap.show();
        }
    }else if (Number.isInteger(index)){
        if (index > 0){
            if (! all_taps.last().is(taps_visible.last())) {
                all_taps.eq(all_taps.index(taps_visible.first())).addClass("no_visible");
                all_taps.eq(all_taps.index(taps_visible.first())).hide();

                if (tap_active.eq(0).is(all_taps.eq(all_taps.index(taps_visible.first())))){
                    tap_active.removeClass("active");
                    all_taps.eq(all_taps.index(tap_active.first()) + index).click();
                }

                all_taps.eq((all_taps.index(taps_visible.last()) + index)).removeClass("no_visible");
                all_taps.eq((all_taps.index(taps_visible.last()) + index)).show();
            }
        }
        else{
            if (! all_taps.first().is(taps_visible.first())) {
                all_taps.eq(all_taps.index(taps_visible.last())).addClass("no_visible");
                all_taps.eq(all_taps.index(taps_visible.last())).hide();

                if (tap_active.eq(0).is(all_taps.eq(all_taps.index(taps_visible.last())))){
                    tap_active.removeClass("active");
                    all_taps.eq(all_taps.index(tap_active.first()) + index).click();
                }

                all_taps.eq((all_taps.index(taps_visible.first()) + index)).removeClass("no_visible");
                all_taps.eq((all_taps.index(taps_visible.first()) + index)).show();
            }
        }
    }
}
function refreshVision(){
    let all_taps = $("div[id^='tab_client_'].tab_client");
    let taps_visible = $("div[id^='tab_client_'].tab_client:not(.no_visible)");
    let index_first_element_visible = all_taps.index(taps_visible.first());
    let taps_to_refresh = all_taps.slice(index_first_element_visible, index_first_element_visible + 4 );
    if (taps_to_refresh.length < 5){
        taps_to_refresh= taps_to_refresh.add(all_taps.slice(index_first_element_visible-(4-taps_to_refresh.length), index_first_element_visible));

    }
    taps_to_refresh.removeClass("no_visible");
    taps_to_refresh.show();
}

