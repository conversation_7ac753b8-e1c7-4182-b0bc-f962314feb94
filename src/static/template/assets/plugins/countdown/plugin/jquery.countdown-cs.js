/* http://keith-wood.name/countdown.html
 * Czech initialisation for the jQuery countdown extension
 * Written by <PERSON> (<EMAIL>) (2008) */
(function($) {
	$.countdown.regional['cs'] = {
		labels: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Hodin', 'Minut', 'Sekund'],
		labels1: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', 'Min<PERSON>', '<PERSON>ku<PERSON>'],
		labels2: ['Roky', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON>ýdny', '<PERSON>ny', 'Hodiny', 'Minuty', '<PERSON>ku<PERSON>'],
		compactLabels: ['r', 'm', 't', 'd'],
		whichLabels: function(amount) {
			return (amount == 1 ? 1 : (amount >= 2 && amount <= 4 ? 2 : 0));
		},
		digits: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
		timeSeparator: ':', isRTL: false};
	$.countdown.setDefaults($.countdown.regional['cs']);
})(jQuery);
