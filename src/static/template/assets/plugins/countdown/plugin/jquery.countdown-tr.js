/* http://keith-wood.name/countdown.html
* Turkish initialisation for the jQuery countdown extension
* Written by <PERSON><PERSON><PERSON> (<EMAIL>) Aug 2008. */
(function($) {
	$.countdown.regional['tr'] = {
		labels: ['<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
		labels1: ['Yıl', 'Ay', 'Haft<PERSON>', '<PERSON>ü<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
		compactLabels: ['y', 'a', 'h', 'g'],
		whichLabels: null,
		digits: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
		timeSeparator: ':', isRTL: false};
	$.countdown.setDefaults($.countdown.regional['tr']);
})(jQuery);
