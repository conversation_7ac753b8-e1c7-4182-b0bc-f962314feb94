/* http://keith-wood.name/countdown.html
   Swedish initialisation for the jQuery countdown extension
   Written by <PERSON> (<EMAIL>). */
(function($) {
	$.countdown.regional['sv'] = {
		labels: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
		labels1: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Da<PERSON>', '<PERSON><PERSON>', 'Minut', 'Sekund'],
		compactLabels: ['Å', 'M', 'V', 'D'],
		whichLabels: null,
		digits: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'],
		timeSeparator: ':', isRTL: false};
	$.countdown.setDefaults($.countdown.regional['sv']);
})(jQuery);
