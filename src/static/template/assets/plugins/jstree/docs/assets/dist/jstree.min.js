/*! jsTree - v3.0.0-beta4 - 2014-01-18 - (MIT) */
(function(e){"use strict";"function"==typeof define&&define.amd?define("jstree",["jquery"],e):"object"==typeof exports?e(require("jquery")):e(jQuery)})(function(e,t){"use strict";if(!e.jstree){var n=0,r=0,i=!1,s=!1,a=!1,o=[],d=e("script:last").attr("src"),l=document,c=l.createElement("LI"),h,_;c.setAttribute("role","treeitem"),h=l.createElement("I"),h.className="jstree-icon jstree-ocl",c.appendChild(h),h=l.createElement("A"),h.className="jstree-anchor",h.setAttribute("href","#"),_=l.createElement("I"),_.className="jstree-icon jstree-themeicon",h.appendChild(_),c.appendChild(h),h=_=null,e.jstree={version:"3.0.0-beta4",defaults:{plugins:[]},plugins:{},path:d&&-1!==d.indexOf("/")?d.replace(/\/[^\/]+$/,""):""},e.jstree.create=function(t,r){var i=new e.jstree.core(++n),s=r;return r=e.extend(!0,{},e.jstree.defaults,r),s&&s.plugins&&(r.plugins=s.plugins),e.each(r.plugins,function(e,t){"core"!==e&&(i=i.plugin(t,r[t]))}),i.init(t,r),i},e.jstree.core=function(e){this._id=e,this._data={core:{themes:{name:!1,dots:!1,icons:!1},selected:[]}}},e.jstree.reference=function(n){if(n&&!e(n).length){n.id&&(n=n.id);var r=null;return e(".jstree").each(function(){var i=e(this).data("jstree");return i&&i._model.data[n]?(r=i,!1):t}),r}return e(n).closest(".jstree").data("jstree")},e.fn.jstree=function(n){var r="string"==typeof n,i=Array.prototype.slice.call(arguments,1),s=null;return this.each(function(){var a=e.jstree.reference(this),o=r&&a?a[n]:null;return s=r&&o?o.apply(a,i):null,a||r||n!==t&&!e.isPlainObject(n)||e(this).data("jstree",new e.jstree.create(this,n)),a&&!r&&(s=a),null!==s&&s!==t?!1:t}),null!==s&&s!==t?s:this},e.expr[":"].jstree=e.expr.createPseudo(function(n){return function(n){return e(n).hasClass("jstree")&&e(n).data("jstree")!==t}}),e.jstree.defaults.core={data:!1,strings:!1,check_callback:!1,animation:200,multiple:!0,themes:{name:!1,url:!1,dir:!1,dots:!0,icons:!0,stripes:!1,variant:!1,responsive:!0},expand_selected_onload:!0},e.jstree.core.prototype={plugin:function(t,n){var r=e.jstree.plugins[t];return r?(this._data[t]={},r.prototype=this,new r(n,this)):this},init:function(t,n){this._model={data:{"#":{id:"#",parent:null,parents:[],children:[],children_d:[],state:{loaded:!1}}},changed:[],force_full_redraw:!1,redraw_timeout:!1,default_state:{loaded:!0,opened:!1,selected:!1,disabled:!1}},this.element=e(t).addClass("jstree jstree-"+this._id),this.settings=n,this.element.bind("destroyed",e.proxy(this.teardown,this)),this._data.core.ready=!1,this._data.core.loaded=!1,this._data.core.rtl="rtl"===this.element.css("direction"),this.element[this._data.core.rtl?"addClass":"removeClass"]("jstree-rtl"),this.element.attr("role","tree"),this.bind(),this.trigger("init"),this._data.core.original_container_html=this.element.find(" > ul > li").clone(!0),this._data.core.original_container_html.find("li").addBack().contents().filter(function(){return 3===this.nodeType&&(!this.nodeValue||/^\s+$/.test(this.nodeValue))}).remove(),this.element.html("<ul class='jstree-container-ul'><li class='jstree-initial-node jstree-loading jstree-leaf jstree-last'><i class='jstree-icon jstree-ocl'></i><a class='jstree-anchor' href='#'><i class='jstree-icon jstree-themeicon-hidden'></i>"+this.get_string("Loading ...")+"</a></li></ul>"),this._data.core.li_height=this.get_container_ul().children("li:eq(0)").height()||18,this.trigger("loading"),this.load_node("#")},destroy:function(){this.element.unbind("destroyed",this.teardown),this.teardown()},teardown:function(){this.unbind(),this.element.removeClass("jstree").removeData("jstree").find("[class^='jstree']").addBack().attr("class",function(){return this.className.replace(/jstree[^ ]*|$/gi,"")}),this.element=null},bind:function(){e.support.touch&&this.element.addTouch(),this.element.on("dblclick.jstree",function(){if(document.selection&&document.selection.empty)document.selection.empty();else if(window.getSelection){var e=window.getSelection();try{e.removeAllRanges(),e.collapse()}catch(t){}}}).on("click.jstree",".jstree-ocl",e.proxy(function(e){this.toggle_node(e.target)},this)).on("click.jstree",".jstree-anchor",e.proxy(function(t){t.preventDefault(),e(t.currentTarget).focus(),this.activate_node(t.currentTarget,t)},this)).on("keydown.jstree",".jstree-anchor",e.proxy(function(t){var n=null;switch(t.which){case 13:case 32:t.type="click",e(t.currentTarget).trigger(t);break;case 37:t.preventDefault(),this.is_open(t.currentTarget)?this.close_node(t.currentTarget):(n=this.get_prev_dom(t.currentTarget),n&&n.length&&n.children(".jstree-anchor").focus());break;case 38:t.preventDefault(),n=this.get_prev_dom(t.currentTarget),n&&n.length&&n.children(".jstree-anchor").focus();break;case 39:t.preventDefault(),this.is_closed(t.currentTarget)?this.open_node(t.currentTarget,function(e){this.get_node(e,!0).children(".jstree-anchor").focus()}):(n=this.get_next_dom(t.currentTarget),n&&n.length&&n.children(".jstree-anchor").focus());break;case 40:t.preventDefault(),n=this.get_next_dom(t.currentTarget),n&&n.length&&n.children(".jstree-anchor").focus();break;case 46:t.preventDefault(),n=this.get_node(t.currentTarget),n&&n.id&&"#"!==n.id&&(n=this.is_selected(n)?this.get_selected():n);break;case 113:t.preventDefault(),n=this.get_node(t.currentTarget);break;default:}},this)).on("load_node.jstree",e.proxy(function(t,n){if(n.status&&("#"!==n.node.id||this._data.core.loaded||(this._data.core.loaded=!0,this.trigger("loaded")),!this._data.core.ready&&!this.get_container_ul().find(".jstree-loading:eq(0)").length)){if(this._data.core.ready=!0,this._data.core.selected.length){if(this.settings.core.expand_selected_onload){var r=[],i,s;for(i=0,s=this._data.core.selected.length;s>i;i++)r=r.concat(this._model.data[this._data.core.selected[i]].parents);for(r=e.vakata.array_unique(r),i=0,s=r.length;s>i;i++)this.open_node(r[i],!1,0)}this.trigger("changed",{action:"ready",selected:this._data.core.selected})}this.trigger("ready")}},this)).on("init.jstree",e.proxy(function(){var e=this.settings.core.themes;this._data.core.themes.dots=e.dots,this._data.core.themes.stripes=e.stripes,this._data.core.themes.icons=e.icons,this.set_theme(e.name||"default",e.url),this.set_theme_variant(e.variant)},this)).on("loading.jstree",e.proxy(function(){this[this._data.core.themes.dots?"show_dots":"hide_dots"](),this[this._data.core.themes.icons?"show_icons":"hide_icons"](),this[this._data.core.themes.stripes?"show_stripes":"hide_stripes"]()},this)).on("focus.jstree",".jstree-anchor",e.proxy(function(t){e(t.currentTarget).mouseenter()},this)).on("blur.jstree",".jstree-anchor",e.proxy(function(t){e(t.currentTarget).mouseleave()},this)).on("mouseenter.jstree",".jstree-anchor",e.proxy(function(e){var t=this.element.find(".jstree-anchor:focus").not(".jstree-clicked");t&&t.length&&t[0]!==e.currentTarget&&t.blur(),this.hover_node(e.currentTarget)},this)).on("mouseleave.jstree",".jstree-anchor",e.proxy(function(e){this.dehover_node(e.currentTarget)},this))},unbind:function(){this.element.off(".jstree"),e(document).off(".jstree-"+this._id)},trigger:function(e,t){t||(t={}),t.instance=this,this.element.triggerHandler(e.replace(".jstree","")+".jstree",t)},get_container:function(){return this.element},get_container_ul:function(){return this.element.children("ul:eq(0)")},get_string:function(t){var n=this.settings.core.strings;return e.isFunction(n)?n.call(this,t):n&&n[t]?n[t]:t},_firstChild:function(e){e=e?e.firstChild:null;while(null!==e&&1!==e.nodeType)e=e.nextSibling;return e},_nextSibling:function(e){e=e?e.nextSibling:null;while(null!==e&&1!==e.nodeType)e=e.nextSibling;return e},_previousSibling:function(e){e=e?e.previousSibling:null;while(null!==e&&1!==e.nodeType)e=e.previousSibling;return e},get_node:function(t,n){t&&t.id&&(t=t.id);var r;try{if(this._model.data[t])t=this._model.data[t];else if(((r=e(t,this.element)).length||(r=e("#"+t,this.element)).length)&&this._model.data[r.closest("li").attr("id")])t=this._model.data[r.closest("li").attr("id")];else{if(!(r=e(t,this.element)).length||!r.hasClass("jstree"))return!1;t=this._model.data["#"]}return n&&(t="#"===t.id?this.element:e(document.getElementById(t.id))),t}catch(i){return!1}},get_next_dom:function(t,n){var r;return t=this.get_node(t,!0),t[0]===this.element[0]?(r=this._firstChild(this.get_container_ul()[0]),r?e(r):!1):t&&t.length?n?(r=this._nextSibling(t[0]),r?e(r):!1):t.hasClass("jstree-open")?(r=this._firstChild(t.children("ul")[0]),r?e(r):!1):null!==(r=this._nextSibling(t[0]))?e(r):t.parentsUntil(".jstree","li").next("li").eq(0):!1},get_prev_dom:function(t,n){var r;if(t=this.get_node(t,!0),t[0]===this.element[0])return r=this.get_container_ul()[0].lastChild,r?e(r):!1;if(!t||!t.length)return!1;if(n)return r=this._previousSibling(t[0]),r?e(r):!1;if(null!==(r=this._previousSibling(t[0]))){t=e(r);while(t.hasClass("jstree-open"))t=t.children("ul:eq(0)").children("li:last");return t}return r=t[0].parentNode.parentNode,r&&"LI"===r.tagName?e(r):!1},get_parent:function(e){return e=this.get_node(e),e&&"#"!==e.id?e.parent:!1},get_children_dom:function(e){return e=this.get_node(e,!0),e[0]===this.element[0]?this.get_container_ul().children("li"):e&&e.length?e.children("ul").children("li"):!1},is_parent:function(e){return e=this.get_node(e),e&&(e.state.loaded===!1||e.children.length)},is_loaded:function(e){return e=this.get_node(e),e&&e.state.loaded},is_loading:function(e){return e=this.get_node(e,!0),e&&e.hasClass("jstree-loading")},is_open:function(e){return e=this.get_node(e),e&&e.state.opened},is_closed:function(e){return e=this.get_node(e),e&&this.is_parent(e)&&!e.state.opened},is_leaf:function(e){return!this.is_parent(e)},load_node:function(t,n){var r,i;if(e.isArray(t)){for(t=t.slice(),r=0,i=t.length;i>r;r++)this.load_node(t[r],n);return!0}return(t=this.get_node(t))?(this.get_node(t,!0).addClass("jstree-loading"),this._load_node(t,e.proxy(function(e){t.state.loaded=e,this.get_node(t,!0).removeClass("jstree-loading"),this.trigger("load_node",{node:t,status:e}),n&&n.call(this,t,e)},this)),!0):(n.call(this,t,!1),!1)},_load_node:function(n,r){var i=this.settings.core.data;return i?e.isFunction(i)?i.call(this,n,e.proxy(function(t){return r.call(this,this["string"==typeof t?"_append_html_data":"_append_json_data"](n,"string"==typeof t?e(t):t))},this)):"object"==typeof i?i.url?(i=e.extend(!0,{},i),e.isFunction(i.url)&&(i.url=i.url.call(this,n)),e.isFunction(i.data)&&(i.data=i.data.call(this,n)),e.ajax(i).done(e.proxy(function(i,s,a){var o=a.getResponseHeader("Content-Type");return-1!==o.indexOf("json")?r.call(this,this._append_json_data(n,i)):-1!==o.indexOf("html")?r.call(this,this._append_html_data(n,e(i))):t},this)).fail(e.proxy(function(){r.call(this,!1)},this))):r.call(this,this._append_json_data(n,i)):"string"==typeof i?r.call(this,this._append_html_data(n,i)):r.call(this,!1):r.call(this,"#"===n.id?this._append_html_data(n,this._data.core.original_container_html.clone(!0)):!1)},_node_changed:function(e){e=this.get_node(e),e&&this._model.changed.push(e.id)},_append_html_data:function(t,n){t=this.get_node(t);var r=n.is("ul")?n.children():n,i=t.id,s=[],a=[],o=this._model.data,d=o[i],l=this._data.core.selected.length,c,h,_;for(r.each(e.proxy(function(t,n){c=this._parse_model_from_html(e(n),i,d.parents.concat()),c&&(s.push(c),a.push(c),o[c].children_d.length&&(a=a.concat(o[c].children_d)))},this)),d.children=s,d.children_d=a,h=0,_=d.parents.length;_>h;h++)o[d.parents[h]].children_d=o[d.parents[h]].children_d.concat(a);return this.trigger("model",{nodes:a,parent:i}),"#"!==i?(this._node_changed(i),this.redraw()):(this.get_container_ul().children(".jstree-initial-node").remove(),this.redraw(!0)),this._data.core.selected.length!==l&&this.trigger("changed",{action:"model",selected:this._data.core.selected}),!0},_append_json_data:function(n,r){n=this.get_node(n);var i=r,s=n.id,a=[],o=[],d=this._model.data,l=d[s],c=this._data.core.selected.length,h,_,u;if(i.d&&(i=i.d,"string"==typeof i&&(i=e.vakata.json.decode(i))),e.isArray(i)||(i=[i]),i.length&&i[0].id!==t&&i[0].parent!==t){for(_=0,u=i.length;u>_;_++)i[_].children||(i[_].children=[]),d[i[_].id]=i[_];for(_=0,u=i.length;u>_;_++)d[i[_].parent].children.push(i[_].id),l.children_d.push(i[_].id);for(_=0,u=l.children.length;u>_;_++)h=this._parse_model_from_flat_json(d[l.children[_]],s,l.parents.concat()),o.push(h),d[h].children_d.length&&(o=o.concat(d[h].children_d))}else{for(_=0,u=i.length;u>_;_++)h=this._parse_model_from_json(i[_],s,l.parents.concat()),h&&(a.push(h),o.push(h),d[h].children_d.length&&(o=o.concat(d[h].children_d)));for(l.children=a,l.children_d=o,_=0,u=l.parents.length;u>_;_++)d[l.parents[_]].children_d=d[l.parents[_]].children_d.concat(o)}return this.trigger("model",{nodes:o,parent:s}),"#"!==s?(this._node_changed(s),this.redraw()):this.redraw(!0),this._data.core.selected.length!==c&&this.trigger("changed",{action:"model",selected:this._data.core.selected}),!0},_parse_model_from_html:function(n,i,s){s=s?[].concat(s):[],i&&s.unshift(i);var a,o,d=this._model.data,l={id:!1,text:!1,icon:!0,parent:i,parents:s,children:[],children_d:[],data:null,state:{},li_attr:{id:!1},a_attr:{href:"#"},original:!1},c,h,_;for(c in this._model.default_state)this._model.default_state.hasOwnProperty(c)&&(l.state[c]=this._model.default_state[c]);if(h=e.vakata.attributes(n,!0),e.each(h,function(n,r){return r=e.trim(r),r.length?(l.li_attr[n]=r,"id"===n&&(l.id=r),t):!0}),h=n.children("a").eq(0),h.length&&(h=e.vakata.attributes(h,!0),e.each(h,function(t,n){n=e.trim(n),n.length&&(l.a_attr[t]=n)})),h=n.children("a:eq(0)").length?n.children("a:eq(0)").clone():n.clone(),h.children("ins, i, ul").remove(),h=h.html(),h=e("<div />").html(h),l.text=h.html(),h=n.data(),l.data=h?e.extend(!0,{},h):null,l.state.opened=n.hasClass("jstree-open"),l.state.selected=n.children("a").hasClass("jstree-clicked"),l.state.disabled=n.children("a").hasClass("jstree-disabled"),l.data&&l.data.jstree)for(c in l.data.jstree)l.data.jstree.hasOwnProperty(c)&&(l.state[c]=l.data.jstree[c]);h=n.children("a").children(".jstree-themeicon"),h.length&&(l.icon=h.hasClass("jstree-themeicon-hidden")?!1:h.attr("rel")),l.state.icon&&(l.icon=l.state.icon),h=n.children("ul").children("li");do _="j"+this._id+"_"+ ++r;while(d[_]);return l.id=l.li_attr.id||_,h.length?(h.each(e.proxy(function(t,n){a=this._parse_model_from_html(e(n),l.id,s),o=this._model.data[a],l.children.push(a),o.children_d.length&&(l.children_d=l.children_d.concat(o.children_d))},this)),l.children_d=l.children_d.concat(l.children)):n.hasClass("jstree-closed")&&(l.state.loaded=!1),l.li_attr.class&&(l.li_attr.class=l.li_attr.class.replace("jstree-closed","").replace("jstree-open","")),l.a_attr.class&&(l.a_attr.class=l.a_attr.class.replace("jstree-clicked","").replace("jstree-disabled","")),d[l.id]=l,l.state.selected&&this._data.core.selected.push(l.id),l.id},_parse_model_from_flat_json:function(e,n,r){r=r?r.concat():[],n&&r.unshift(n);var i=e.id,s=this._model.data,a=this._model.default_state,o,d,l,c,h={id:i,text:e.text||"",icon:e.icon!==t?e.icon:!0,parent:n,parents:r,children:e.children||[],children_d:e.children_d||[],data:e.data,state:{},li_attr:{id:!1},a_attr:{href:"#"},original:!1};for(o in a)a.hasOwnProperty(o)&&(h.state[o]=a[o]);if(e&&e.data&&e.data.jstree&&e.data.jstree.icon&&(h.icon=e.data.jstree.icon),e&&e.data&&(h.data=e.data,e.data.jstree))for(o in e.data.jstree)e.data.jstree.hasOwnProperty(o)&&(h.state[o]=e.data.jstree[o]);if(e&&"object"==typeof e.state)for(o in e.state)e.state.hasOwnProperty(o)&&(h.state[o]=e.state[o]);if(e&&"object"==typeof e.li_attr)for(o in e.li_attr)e.li_attr.hasOwnProperty(o)&&(h.li_attr[o]=e.li_attr[o]);if(h.li_attr.id||(h.li_attr.id=i),e&&"object"==typeof e.a_attr)for(o in e.a_attr)e.a_attr.hasOwnProperty(o)&&(h.a_attr[o]=e.a_attr[o]);for(e&&e.children&&e.children===!0&&(h.state.loaded=!1,h.children=[],h.children_d=[]),s[h.id]=h,o=0,d=h.children.length;d>o;o++)l=this._parse_model_from_flat_json(s[h.children[o]],h.id,r),c=s[l],h.children_d.push(l),c.children_d.length&&(h.children_d=h.children_d.concat(c.children_d));return delete e.data,delete e.children,s[h.id].original=e,h.state.selected&&this._data.core.selected.push(h.id),h.id},_parse_model_from_json:function(e,n,i){i=i?i.concat():[],n&&i.unshift(n);var s=!1,a,o,d,l,c=this._model.data,h=this._model.default_state,_;do s="j"+this._id+"_"+ ++r;while(c[s]);_={id:!1,text:"string"==typeof e?e:"",icon:"object"==typeof e&&e.icon!==t?e.icon:!0,parent:n,parents:i,children:[],children_d:[],data:null,state:{},li_attr:{id:!1},a_attr:{href:"#"},original:!1};for(a in h)h.hasOwnProperty(a)&&(_.state[a]=h[a]);if(e&&e.id&&(_.id=e.id),e&&e.text&&(_.text=e.text),e&&e.data&&e.data.jstree&&e.data.jstree.icon&&(_.icon=e.data.jstree.icon),e&&e.data&&(_.data=e.data,e.data.jstree))for(a in e.data.jstree)e.data.jstree.hasOwnProperty(a)&&(_.state[a]=e.data.jstree[a]);if(e&&"object"==typeof e.state)for(a in e.state)e.state.hasOwnProperty(a)&&(_.state[a]=e.state[a]);if(e&&"object"==typeof e.li_attr)for(a in e.li_attr)e.li_attr.hasOwnProperty(a)&&(_.li_attr[a]=e.li_attr[a]);if(_.li_attr.id&&!_.id&&(_.id=_.li_attr.id),_.id||(_.id=s),_.li_attr.id||(_.li_attr.id=_.id),e&&"object"==typeof e.a_attr)for(a in e.a_attr)e.a_attr.hasOwnProperty(a)&&(_.a_attr[a]=e.a_attr[a]);if(e&&e.children&&e.children.length){for(a=0,o=e.children.length;o>a;a++)d=this._parse_model_from_json(e.children[a],_.id,i),l=c[d],_.children.push(d),l.children_d.length&&(_.children_d=_.children_d.concat(l.children_d));_.children_d=_.children_d.concat(_.children)}return e&&e.children&&e.children===!0&&(_.state.loaded=!1,_.children=[],_.children_d=[]),delete e.data,delete e.children,_.original=e,c[_.id]=_,_.state.selected&&this._data.core.selected.push(_.id),_.id},_redraw:function(){var e=this._model.force_full_redraw?this._model.data["#"].children.concat([]):this._model.changed.concat([]),t=document.createElement("UL"),n,r,i;for(r=0,i=e.length;i>r;r++)n=this.redraw_node(e[r],!0,this._model.force_full_redraw),n&&this._model.force_full_redraw&&t.appendChild(n);this._model.force_full_redraw&&(t.className=this.get_container_ul()[0].className,this.element.empty().append(t)),this._model.force_full_redraw=!1,this._model.changed=[],this.trigger("redraw",{nodes:e})},redraw:function(e){e&&(this._model.force_full_redraw=!0),this._redraw()},redraw_node:function(t,n,r){var i=this.get_node(t),s=!1,a=!1,o=!1,d=!1,l=!1,h=!1,_="",u=document,f=this._model.data;if(!i)return!1;if("#"===i.id)return this.redraw(!0);if(n=n||0===i.children.length,t=u.getElementById(i.id))t=e(t),r||(s=t.parent().parent()[0],s===this.element[0]&&(s=null),a=t.index()),f[i.id].data=t.data(),n||!i.children.length||t.children("ul").length||(n=!0),n||(o=t.children("UL")[0]),t.remove();else if(n=!0,!r){if(s="#"!==i.parent?e("#"+i.parent,this.element)[0]:null,!(null===s||s&&f[i.parent].state.opened))return!1;a=e.inArray(i.id,null===s?f["#"].children:f[i.parent].children)}t=c.cloneNode(!0),_="jstree-node ";for(d in i.li_attr)if(i.li_attr.hasOwnProperty(d)){if("id"===d)continue;"class"!==d?t.setAttribute(d,i.li_attr[d]):_+=i.li_attr[d]}_+=!i.children.length&&i.state.loaded?" jstree-leaf":i.state.opened?" jstree-open":" jstree-closed",null!==i.parent&&f[i.parent].children[f[i.parent].children.length-1]===i.id&&(_+=" jstree-last"),t.id=i.id,t.className=_,_=(i.state.selected?" jstree-clicked":"")+(i.state.disabled?" jstree-disabled":"");for(l in i.a_attr)if(i.a_attr.hasOwnProperty(l)){if("href"===l&&"#"===i.a_attr[l])continue;"class"!==l?t.childNodes[1].setAttribute(l,i.a_attr[l]):_+=" "+i.a_attr[l]}if(_.length&&(t.childNodes[1].className="jstree-anchor "+_),(i.icon&&i.icon!==!0||i.icon===!1)&&(i.icon===!1?t.childNodes[1].childNodes[0].className+=" jstree-themeicon-hidden":-1===i.icon.indexOf("/")&&-1===i.icon.indexOf(".")?t.childNodes[1].childNodes[0].className+=" "+i.icon+" jstree-themeicon-custom":(t.childNodes[1].childNodes[0].style.backgroundImage="url("+i.icon+")",t.childNodes[1].childNodes[0].style.backgroundPosition="center center",t.childNodes[1].childNodes[0].style.backgroundSize="auto",t.childNodes[1].childNodes[0].className+=" jstree-themeicon-custom")),t.childNodes[1].innerHTML+=i.text,i.data&&e.data(t,i.data),n&&i.children.length&&i.state.opened){for(h=u.createElement("UL"),h.setAttribute("role","group"),h.className="jstree-children",d=0,l=i.children.length;l>d;d++)h.appendChild(this.redraw_node(i.children[d],n,!0));t.appendChild(h)}return o&&t.appendChild(o),r||(s||(s=this.element[0]),s.getElementsByTagName("UL").length?s=s.getElementsByTagName("UL")[0]:(d=u.createElement("UL"),d.setAttribute("role","group"),d.className="jstree-children",s.appendChild(d),s=d),s.childNodes.length>a?s.insertBefore(t,s.childNodes[a]):s.appendChild(t)),t},open_node:function(n,r,i){var s,a,o,d;if(e.isArray(n)){for(n=n.slice(),s=0,a=n.length;a>s;s++)this.open_node(n[s],r,i);return!0}if(n=this.get_node(n),!n||"#"===n.id)return!1;if(i=i===t?this.settings.core.animation:i,!this.is_closed(n))return r&&r.call(this,n,!1),!1;if(this.is_loaded(n))o=this.get_node(n,!0),d=this,o.length&&(n.children.length&&!this._firstChild(o.children("ul")[0])&&(n.state.opened=!0,this.redraw_node(n,!0),o=this.get_node(n,!0)),i?o.children("ul").css("display","none").end().removeClass("jstree-closed").addClass("jstree-open").children("ul").stop(!0,!0).slideDown(i,function(){this.style.display="",d.trigger("after_open",{node:n})}):o[0].className=o[0].className.replace("jstree-closed","jstree-open")),n.state.opened=!0,r&&r.call(this,n,!0),this.trigger("open_node",{node:n}),i&&o.length||this.trigger("after_open",{node:n});else{if(this.is_loading(n))return setTimeout(e.proxy(function(){this.open_node(n,r,i)},this),500);this.load_node(n,function(e,t){return t?this.open_node(e,r,i):r?r.call(this,e,!1):!1})}},_open_to:function(t){if(t=this.get_node(t),!t||"#"===t.id)return!1;var n,r,i=t.parents;for(n=0,r=i.length;r>n;n+=1)"#"!==n&&this.open_node(i[n],!1,0);return e(document.getElementById(t.id))},close_node:function(n,r){var i,s,a,o;if(e.isArray(n)){for(n=n.slice(),i=0,s=n.length;s>i;i++)this.close_node(n[i],r);return!0}return n=this.get_node(n),n&&"#"!==n.id?(r=r===t?this.settings.core.animation:r,a=this,o=this.get_node(n,!0),o.length&&(r?o.children("ul").attr("style","display:block !important").end().removeClass("jstree-open").addClass("jstree-closed").children("ul").stop(!0,!0).slideUp(r,function(){this.style.display="",o.children("ul").remove(),a.trigger("after_close",{node:n})}):(o[0].className=o[0].className.replace("jstree-open","jstree-closed"),o.children("ul").remove())),n.state.opened=!1,this.trigger("close_node",{node:n}),r&&o.length||this.trigger("after_close",{node:n}),t):!1},toggle_node:function(n){var r,i;if(e.isArray(n)){for(n=n.slice(),r=0,i=n.length;i>r;r++)this.toggle_node(n[r]);return!0}return this.is_closed(n)?this.open_node(n):this.is_open(n)?this.close_node(n):t},open_all:function(e,t,n){if(e||(e="#"),e=this.get_node(e),!e)return!1;var r="#"===e.id?this.get_container_ul():this.get_node(e,!0),i,s,a;if(!r.length){for(i=0,s=e.children_d.length;s>i;i++)this.is_closed(this._mode.data[e.children_d[i]])&&(this._mode.data[e.children_d[i]].state.opened=!0);return this.trigger("open_all",{node:e})}n=n||r,a=this,r=this.is_closed(e)?r.find("li.jstree-closed").addBack():r.find("li.jstree-closed"),r.each(function(){a.open_node(this,function(e,r){r&&this.is_parent(e)&&this.open_all(e,t,n)},t||0)}),0===n.find("li.jstree-closed").length&&this.trigger("open_all",{node:this.get_node(n)})},close_all:function(e,t){if(e||(e="#"),e=this.get_node(e),!e)return!1;var n="#"===e.id?this.get_container_ul():this.get_node(e,!0),r=this,i,s;if(!n.length){for(i=0,s=e.children_d.length;s>i;i++)this._mode.data[e.children_d[i]].state.opened=!1;return this.trigger("close_all",{node:e})}n=this.is_open(e)?n.find("li.jstree-open").addBack():n.find("li.jstree-open"),n.vakata_reverse().each(function(){r.close_node(this,t||0)}),this.trigger("close_all",{node:e})},is_disabled:function(e){return e=this.get_node(e),e&&e.state&&e.state.disabled},enable_node:function(n){var r,i;if(e.isArray(n)){for(n=n.slice(),r=0,i=n.length;i>r;r++)this.enable_node(n[r]);return!0}return n=this.get_node(n),n&&"#"!==n.id?(n.state.disabled=!1,this.get_node(n,!0).children(".jstree-anchor").removeClass("jstree-disabled"),this.trigger("enable_node",{node:n}),t):!1},disable_node:function(n){var r,i;if(e.isArray(n)){for(n=n.slice(),r=0,i=n.length;i>r;r++)this.disable_node(n[r]);return!0}return n=this.get_node(n),n&&"#"!==n.id?(n.state.disabled=!0,this.get_node(n,!0).children(".jstree-anchor").addClass("jstree-disabled"),this.trigger("disable_node",{node:n}),t):!1},activate_node:function(e,t){if(this.is_disabled(e))return!1;if(this.settings.core.multiple&&(t.metaKey||t.ctrlKey||t.shiftKey)&&(!t.shiftKey||this._data.core.last_clicked&&this.get_parent(e)&&this.get_parent(e)===this._data.core.last_clicked.parent))if(t.shiftKey){var n=this.get_node(e).id,r=this._data.core.last_clicked.id,i=this.get_node(this._data.core.last_clicked.parent).children,s=!1,a,o;for(a=0,o=i.length;o>a;a+=1)i[a]===n&&(s=!s),i[a]===r&&(s=!s),s||i[a]===n||i[a]===r?this.select_node(i[a]):this.deselect_node(i[a])}else this.is_selected(e)?this.deselect_node(e):this.select_node(e);else this.deselect_all(!0),this.select_node(e),this._data.core.last_clicked=this.get_node(e);this.trigger("activate_node",{node:this.get_node(e)})},hover_node:function(e){return e=this.get_node(e,!0),e&&e.length?(e.children(".jstree-anchor").addClass("jstree-hovered"),this.trigger("hover_node",{node:this.get_node(e)}),t):!1},dehover_node:function(e){return e=this.get_node(e,!0),e&&e.length?(e.children(".jstree-anchor").removeClass("jstree-hovered"),this.trigger("dehover_node",{node:this.get_node(e)}),t):!1},select_node:function(n,r,i){var s,a,o,d;if(e.isArray(n)){for(n=n.slice(),a=0,o=n.length;o>a;a++)this.select_node(n[a],r,i);return!0}return n=this.get_node(n),n&&"#"!==n.id?(s=this.get_node(n,!0),n.state.selected||(n.state.selected=!0,this._data.core.selected.push(n.id),i||(s=this._open_to(n)),s&&s.length&&s.children(".jstree-anchor").addClass("jstree-clicked"),this.trigger("select_node",{node:n,selected:this._data.core.selected}),r||this.trigger("changed",{action:"select_node",node:n,selected:this._data.core.selected})),t):!1},deselect_node:function(n,r){var i,s,a;if(e.isArray(n)){for(n=n.slice(),i=0,s=n.length;s>i;i++)this.deselect_node(n[i],r);return!0}return n=this.get_node(n),n&&"#"!==n.id?(a=this.get_node(n,!0),n.state.selected&&(n.state.selected=!1,this._data.core.selected=e.vakata.array_remove_item(this._data.core.selected,n.id),a.length&&a.children(".jstree-anchor").removeClass("jstree-clicked"),this.trigger("deselect_node",{node:n,selected:this._data.core.selected}),r||this.trigger("changed",{action:"deselect_node",node:n,selected:this._data.core.selected})),t):!1},select_all:function(e){var t=this._data.core.selected.concat([]),n,r;for(this._data.core.selected=this._model.data["#"].children_d.concat(),n=0,r=this._data.core.selected.length;r>n;n++)this._model.data[this._data.core.selected[n]]&&(this._model.data[this._data.core.selected[n]].state.selected=!0);this.redraw(!0),this.trigger("select_all",{selected:this._data.core.selected}),e||this.trigger("changed",{action:"select_all",selected:this._data.core.selected,old_selection:t})},deselect_all:function(e){var t=this._data.core.selected.concat([]),n,r;for(n=0,r=this._data.core.selected.length;r>n;n++)this._model.data[this._data.core.selected[n]]&&(this._model.data[this._data.core.selected[n]].state.selected=!1);this._data.core.selected=[],this.element.find(".jstree-clicked").removeClass("jstree-clicked"),this.trigger("deselect_all",{selected:this._data.core.selected,node:t}),e||this.trigger("changed",{action:"deselect_all",selected:this._data.core.selected,old_selection:t})},is_selected:function(e){return e=this.get_node(e),e&&"#"!==e.id?e.state.selected:!1},get_selected:function(t){return t?e.map(this._data.core.selected,e.proxy(function(e){return this.get_node(e)},this)):this._data.core.selected},get_state:function(){var e={core:{open:[],scroll:{left:this.element.scrollLeft(),top:this.element.scrollTop()},selected:[]}},t;for(t in this._model.data)this._model.data.hasOwnProperty(t)&&"#"!==t&&(this._model.data[t].state.opened&&e.core.open.push(t),this._model.data[t].state.selected&&e.core.selected.push(t));return e},set_state:function(n,r){if(n){if(n.core){var i,s,a,o;return e.isArray(n.core.open)?(i=!0,s=!1,a=this,e.each(n.core.open.concat([]),function(t,r){s=document.getElementById(r),s&&(a.is_loaded(r)?(a.is_closed(r)&&a.open_node(r,!1,0),e.vakata.array_remove_item(n.core.open,r)):(a.is_loading(r)||a.open_node(r,e.proxy(function(){this.set_state(n)},a),0),i=!1))}),i&&(delete n.core.open,this.set_state(n,r)),!1):n.core.scroll?(n.core.scroll&&n.core.scroll.left!==t&&this.element.scrollLeft(n.core.scroll.left),n.core.scroll&&n.core.scroll.top!==t&&this.element.scrollTop(n.core.scroll.top),delete n.core.scroll,delete n.core.open,this.set_state(n,r),!1):n.core.selected?(o=this,this.deselect_all(),e.each(n.core.selected,function(e,t){o.select_node(t)}),delete n.core.selected,this.set_state(n,r),!1):e.isEmptyObject(n)?(r&&r.call(this),this.trigger("set_state"),!1):!0}return!0}return!1},refresh:function(){this._data.core.state=this.get_state(),this.load_node("#",function(t,n){n&&this.set_state(e.extend(!0,{},this._data.core.state),function(){this.trigger("refresh")}),this._data.core.state=null})},set_id:function(t,n){if(t=this.get_node(t),!t||"#"===t.id)return!1;var r,i,s=this._model.data;for(s[t.parent].children[e.inArray(t.id,s[t.parent].children)]=n,r=0,i=t.parents.length;i>r;r++)s[t.parents[r]].children_d[e.inArray(t.id,s[t.parents[r]].children_d)]=n;for(r=0,i=t.children.length;i>r;r++)s[t.children[r]].parent=n;for(r=0,i=t.children_d.length;i>r;r++)s[t.children_d[r]].parents[e.inArray(t.id,s[t.children_d[r]].parents)]=n;return r=this.get_node(t.id,!0),r&&r.attr("id",n),delete s[t.id],t.id=n,s[n]=t,!0},get_text:function(e){return e=this.get_node(e),e&&"#"!==e.id?e.text:!1},set_text:function(t,n){var r,i,s,a;if(e.isArray(t)){for(t=t.slice(),r=0,i=t.length;i>r;r++)this.set_text(t[r],n);return!0}return t=this.get_node(t),t&&"#"!==t.id?(t.text=n,s=this.get_node(t,!0),s.length&&(s=s.children(".jstree-anchor:eq(0)"),a=s.children("I").clone(),s.html(n).prepend(a),this.trigger("set_text",{obj:t,text:n})),!0):!1},get_json:function(e,t){if(e=this.get_node(e||"#"),!e)return!1;var n={id:e.id,text:e.text,icon:this.get_icon(e),li_attr:e.li_attr,a_attr:e.a_attr,state:{},data:t&&t.no_data?!1:this.get_node(e,!0).length?this.get_node(e,!0).data():e.data,children:[]},r,i;if(!t||!t.no_state)for(r in e.state)e.state.hasOwnProperty(r)&&(n.state[r]=e.state[r]);if(t&&t.no_id&&n.li_attr&&n.li_attr.id&&(delete n.li_attr.id,delete n.id),!t||!t.no_children)for(r=0,i=e.children.length;i>r;r++)n.children.push(this.get_json(e.children[r],t));return"#"===e.id?n.children:n},create_node:function(n,r,i,s,a){if(n=this.get_node(n),!n)return!1;if(i=i===t?"last":i,!i.match(/^(before|after)$/)&&!a&&!this.is_loaded(n))return this.load_node(n,function(){this.create_node(n,r,i,s,!0)});r||(r={text:this.get_string("New node")}),r.text===t&&(r.text=this.get_string("New node"));var o,d,l,c;switch("#"===n.id&&("before"===i&&(i="first"),"after"===i&&(i="last")),i){case"before":o=this.get_node(n.parent),i=e.inArray(n.id,o.children),n=o;break;case"after":o=this.get_node(n.parent),i=e.inArray(n.id,o.children)+1,n=o;break;case"inside":case"first":i=0;break;case"last":i=n.children.length;break;default:i||(i=0)}if(i>n.children.length&&(i=n.children.length),r.id||(r.id=!0),!this.check("create_node",r,n,i))return!1;if(r.id===!0&&delete r.id,r=this._parse_model_from_json(r,n.id,n.parents.concat()),!r)return!1;for(o=this.get_node(r),d=[],d.push(r),d=d.concat(o.children_d),this.trigger("model",{nodes:d,parent:n.id}),n.children_d=n.children_d.concat(d),l=0,c=n.parents.length;c>l;l++)this._model.data[n.parents[l]].children_d=this._model.data[n.parents[l]].children_d.concat(d);for(r=o,o=[],l=0,c=n.children.length;c>l;l++)o[l>=i?l+1:l]=n.children[l];
return o[i]=r.id,n.children=o,this.redraw_node(n,!0),s&&s.call(this,this.get_node(r)),this.trigger("create_node",{node:this.get_node(r),parent:n.id,position:i}),r.id},rename_node:function(t,n){var r,i,s;if(e.isArray(t)){for(t=t.slice(),r=0,i=t.length;i>r;r++)this.rename_node(t[r],n);return!0}return t=this.get_node(t),t&&"#"!==t.id?(s=t.text,this.check("rename_node",t,this.get_parent(t),n)?(this.set_text(t,n),this.trigger("rename_node",{node:t,text:n,old:s}),!0):!1):!1},delete_node:function(t){var n,r,i,s,a,o,d,l,c,h;if(e.isArray(t)){for(t=t.slice(),n=0,r=t.length;r>n;n++)this.delete_node(t[n]);return!0}if(t=this.get_node(t),!t||"#"===t.id)return!1;if(i=this.get_node(t.parent),s=e.inArray(t.id,i.children),h=!1,!this.check("delete_node",t,i,s))return!1;for(-1!==s&&(i.children=e.vakata.array_remove(i.children,s)),a=t.children_d.concat([]),a.push(t.id),l=0,c=a.length;c>l;l++){for(o=0,d=t.parents.length;d>o;o++)s=e.inArray(a[l],this._model.data[t.parents[o]].children_d),-1!==s&&(this._model.data[t.parents[o]].children_d=e.vakata.array_remove(this._model.data[t.parents[o]].children_d,s));this._model.data[a[l]].state.selected&&(h=!0,s=e.inArray(a[l],this._data.core.selected),-1!==s&&(this._data.core.selected=e.vakata.array_remove(this._data.core.selected,s)))}for(this.trigger("delete_node",{node:t,parent:i.id}),h&&this.trigger("changed",{action:"delete_node",node:t,selected:this._data.core.selected,parent:i.id}),l=0,c=a.length;c>l;l++)delete this._model.data[a[l]];return this.redraw_node(i,!0),!0},check:function(t,n,r,i){n=n&&n.id?n:this.get_node(n),r=r&&r.id?r:this.get_node(r);var s=t.match(/^move_node|copy_node|create_node$/i)?r:n,a=this.settings.core.check_callback;return"move_node"!==t||n.id!==r.id&&e.inArray(n.id,r.children)!==i&&-1===e.inArray(r.id,n.children_d)?(s=this.get_node(s,!0),s.length&&(s=s.data("jstree")),s&&s.functions&&(s.functions[t]===!1||s.functions[t]===!0)?s.functions[t]:a===!1||e.isFunction(a)&&a.call(this,t,n,r,i)===!1||a&&a[t]===!1?!1:!0):!1},move_node:function(n,r,i,s,a){var o,d,l,c,h,_,u,f,g,p,m,v,j;if(e.isArray(n)){for(n=n.reverse().slice(),o=0,d=n.length;d>o;o++)this.move_node(n[o],r,i,s,a);return!0}if(n=n&&n.id?n:this.get_node(n),r=this.get_node(r),i=i===t?0:i,!r||!n||"#"===n.id)return!1;if(!(""+i).match(/^(before|after)$/)&&!a&&!this.is_loaded(r))return this.load_node(r,function(){this.move_node(n,r,i,s,!0)});if(l=""+(n.parent||"#"),c=(""+i).match(/^(before|after)$/)&&"#"!==r.id?this.get_node(r.parent):r,h=this._model.data[n.id]?this:e.jstree.reference(n.id),_=!h||!h._id||this._id!==h._id)return this.copy_node(n,r,i,s,a)?(h&&h.delete_node(n),!0):!1;switch("#"===c.id&&("before"===i&&(i="first"),"after"===i&&(i="last")),i){case"before":i=e.inArray(r.id,c.children);break;case"after":i=e.inArray(r.id,c.children)+1;break;case"inside":case"first":i=0;break;case"last":i=c.children.length;break;default:i||(i=0)}if(i>c.children.length&&(i=c.children.length),!this.check("move_node",n,c,i))return!1;if(n.parent===c.id){for(u=c.children.concat(),f=e.inArray(n.id,u),-1!==f&&(u=e.vakata.array_remove(u,f),i>f&&i--),f=[],g=0,p=u.length;p>g;g++)f[g>=i?g+1:g]=u[g];f[i]=n.id,c.children=f,this._node_changed(c.id),this.redraw("#"===c.id)}else{for(f=n.children_d.concat(),f.push(n.id),g=0,p=n.parents.length;p>g;g++){for(u=[],j=h._model.data[n.parents[g]].children_d,m=0,v=j.length;v>m;m++)-1===e.inArray(j[m],f)&&u.push(j[m]);h._model.data[n.parents[g]].children_d=u}for(h._model.data[l].children=e.vakata.array_remove_item(h._model.data[l].children,n.id),g=0,p=c.parents.length;p>g;g++)this._model.data[c.parents[g]].children_d=this._model.data[c.parents[g]].children_d.concat(f);for(u=[],g=0,p=c.children.length;p>g;g++)u[g>=i?g+1:g]=c.children[g];for(u[i]=n.id,c.children=u,c.children_d.push(n.id),c.children_d=c.children_d.concat(n.children_d),n.parent=c.id,f=c.parents.concat(),f.unshift(c.id),j=n.parents.length,n.parents=f,f=f.concat(),g=0,p=n.children_d.length;p>g;g++)this._model.data[n.children_d[g]].parents=this._model.data[n.children_d[g]].parents.slice(0,-1*j),Array.prototype.push.apply(this._model.data[n.children_d[g]].parents,f);this._node_changed(l),this._node_changed(c.id),this.redraw("#"===l||"#"===c.id)}return s&&s.call(this,n,c,i),this.trigger("move_node",{node:n,parent:c.id,position:i,old_parent:l,is_multi:_,old_instance:h,new_instance:this}),!0},copy_node:function(n,r,i,s,a){var o,d,l,c,h,_,u,f,g,p,m;if(e.isArray(n)){for(n=n.reverse().slice(),o=0,d=n.length;d>o;o++)this.copy_node(n[o],r,i,s,a);return!0}if(n=n&&n.id?n:this.get_node(n),r=this.get_node(r),i=i===t?0:i,!r||!n||"#"===n.id)return!1;if(!(""+i).match(/^(before|after)$/)&&!a&&!this.is_loaded(r))return this.load_node(r,function(){this.copy_node(n,r,i,s,!0)});switch(f=""+(n.parent||"#"),g=(""+i).match(/^(before|after)$/)&&"#"!==r.id?this.get_node(r.parent):r,p=this._model.data[n.id]?this:e.jstree.reference(n.id),m=!p||!p._id||this._id!==p._id,"#"===g.id&&("before"===i&&(i="first"),"after"===i&&(i="last")),i){case"before":i=e.inArray(r.id,g.children);break;case"after":i=e.inArray(r.id,g.children)+1;break;case"inside":case"first":i=0;break;case"last":i=g.children.length;break;default:i||(i=0)}if(i>g.children.length&&(i=g.children.length),!this.check("copy_node",n,g,i))return!1;if(u=p?p.get_json(n,{no_id:!0,no_data:!0,no_state:!0}):n,!u)return!1;if(u.id===!0&&delete u.id,u=this._parse_model_from_json(u,g.id,g.parents.concat()),!u)return!1;for(c=this.get_node(u),l=[],l.push(u),l=l.concat(c.children_d),this.trigger("model",{nodes:l,parent:g.id}),h=0,_=g.parents.length;_>h;h++)this._model.data[g.parents[h]].children_d=this._model.data[g.parents[h]].children_d.concat(l);for(l=[],h=0,_=g.children.length;_>h;h++)l[h>=i?h+1:h]=g.children[h];return l[i]=c.id,g.children=l,g.children_d.push(c.id),g.children_d=g.children_d.concat(c.children_d),this._node_changed(g.id),this.redraw("#"===g.id),s&&s.call(this,c,g,i),this.trigger("copy_node",{node:c,original:n,parent:g.id,position:i,old_parent:f,is_multi:m,old_instance:p,new_instance:this}),c.id},cut:function(n){if(n||(n=this._data.core.selected.concat()),e.isArray(n)||(n=[n]),!n.length)return!1;var r=[],o,d,l;for(d=0,l=n.length;l>d;d++)o=this.get_node(n[d]),o&&o.id&&"#"!==o.id&&r.push(o);return r.length?(i=r,a=this,s="move_node",this.trigger("cut",{node:n}),t):!1},copy:function(n){if(n||(n=this._data.core.selected.concat()),e.isArray(n)||(n=[n]),!n.length)return!1;var r=[],o,d,l;for(d=0,l=n.length;l>d;d++)o=this.get_node(n[d]),o&&o.id&&"#"!==o.id&&r.push(o);return r.length?(i=r,a=this,s="copy_node",this.trigger("copy",{node:n}),t):!1},get_buffer:function(){return{mode:s,node:i,inst:a}},can_paste:function(){return s!==!1&&i!==!1},paste:function(e){return e=this.get_node(e),e&&s&&s.match(/^(copy_node|move_node)$/)&&i?(this[s](i,e)&&this.trigger("paste",{parent:e.id,node:i,mode:s}),i=!1,s=!1,a=!1,t):!1},edit:function(n,r){if(n=this._open_to(n),!n||!n.length)return!1;var i=this._data.core.rtl,s=this.element.width(),a=n.children(".jstree-anchor"),o=e("<span>"),d="string"==typeof r?r:this.get_text(n),l=e("<div />",{css:{position:"absolute",top:"-200px",left:i?"0px":"-1000px",visibility:"hidden"}}).appendTo("body"),c=e("<input />",{value:d,"class":"jstree-rename-input",css:{padding:"0",border:"1px solid silver","box-sizing":"border-box",display:"inline-block",height:this._data.core.li_height+"px",lineHeight:this._data.core.li_height+"px",width:"150px"},blur:e.proxy(function(){var e=o.children(".jstree-rename-input"),t=e.val();""===t&&(t=d),l.remove(),o.replaceWith(a),o.remove(),this.rename_node(n,t)===!1&&this.set_text(n,d)},this),keydown:function(e){var t=e.which;27===t&&(this.value=d),(27===t||13===t||37===t||38===t||39===t||40===t||32===t)&&e.stopImmediatePropagation(),(27===t||13===t)&&(e.preventDefault(),this.blur())},click:function(e){e.stopImmediatePropagation()},mousedown:function(e){e.stopImmediatePropagation()},keyup:function(e){c.width(Math.min(l.text("pW"+this.value).width(),s))},keypress:function(e){return 13===e.which?!1:t}}),h={fontFamily:a.css("fontFamily")||"",fontSize:a.css("fontSize")||"",fontWeight:a.css("fontWeight")||"",fontStyle:a.css("fontStyle")||"",fontStretch:a.css("fontStretch")||"",fontVariant:a.css("fontVariant")||"",letterSpacing:a.css("letterSpacing")||"",wordSpacing:a.css("wordSpacing")||""};this.set_text(n,""),o.attr("class",a.attr("class")).append(a.contents().clone()).append(c),a.replaceWith(o),l.css(h),c.css(h).width(Math.min(l.text("pW"+c[0].value).width(),s))[0].select()},set_theme:function(t,n){if(!t)return!1;if(n===!0){var r=this.settings.core.themes.dir;r||(r=e.jstree.path+"/themes"),n=r+"/"+t+"/style.css"}n&&-1===e.inArray(n,o)&&(e("head").append('<link rel="stylesheet" href="'+n+'" type="text/css" />'),o.push(n)),this._data.core.themes.name&&this.element.removeClass("jstree-"+this._data.core.themes.name),this._data.core.themes.name=t,this.element.addClass("jstree-"+t),this.element[this.settings.core.themes.responsive?"addClass":"removeClass"]("jstree-"+t+"-responsive"),this.trigger("set_theme",{theme:t})},get_theme:function(){return this._data.core.themes.name},set_theme_variant:function(e){this._data.core.themes.variant&&this.element.removeClass("jstree-"+this._data.core.themes.name+"-"+this._data.core.themes.variant),this._data.core.themes.variant=e,e&&this.element.addClass("jstree-"+this._data.core.themes.name+"-"+this._data.core.themes.variant)},get_theme_variant:function(){return this._data.core.themes.variant},show_stripes:function(){this._data.core.themes.stripes=!0,this.get_container_ul().addClass("jstree-striped")},hide_stripes:function(){this._data.core.themes.stripes=!1,this.get_container_ul().removeClass("jstree-striped")},toggle_stripes:function(){this._data.core.themes.stripes?this.hide_stripes():this.show_stripes()},show_dots:function(){this._data.core.themes.dots=!0,this.get_container_ul().removeClass("jstree-no-dots")},hide_dots:function(){this._data.core.themes.dots=!1,this.get_container_ul().addClass("jstree-no-dots")},toggle_dots:function(){this._data.core.themes.dots?this.hide_dots():this.show_dots()},show_icons:function(){this._data.core.themes.icons=!0,this.get_container_ul().removeClass("jstree-no-icons")},hide_icons:function(){this._data.core.themes.icons=!1,this.get_container_ul().addClass("jstree-no-icons")},toggle_icons:function(){this._data.core.themes.icons?this.hide_icons():this.show_icons()},set_icon:function(t,n){var r,i,s,a;if(e.isArray(t)){for(t=t.slice(),r=0,i=t.length;i>r;r++)this.set_icon(t[r],n);return!0}return t=this.get_node(t),t&&"#"!==t.id?(a=t.icon,t.icon=n,s=this.get_node(t,!0).children(".jstree-anchor").children(".jstree-themeicon"),n===!1?this.hide_icon(t):n===!0?s.removeClass("jstree-themeicon-custom "+a).css("background","").removeAttr("rel"):-1===n.indexOf("/")&&-1===n.indexOf(".")?(s.removeClass(a).css("background",""),s.addClass(n+" jstree-themeicon-custom").attr("rel",n)):(s.removeClass(a).css("background",""),s.addClass("jstree-themeicon-custom").css("background","url('"+n+"') center center no-repeat").attr("rel",n)),!0):!1},get_icon:function(e){return e=this.get_node(e),e&&"#"!==e.id?e.icon:!1},hide_icon:function(t){var n,r;if(e.isArray(t)){for(t=t.slice(),n=0,r=t.length;r>n;n++)this.hide_icon(t[n]);return!0}return t=this.get_node(t),t&&"#"!==t?(t.icon=!1,this.get_node(t,!0).children("a").children(".jstree-themeicon").addClass("jstree-themeicon-hidden"),!0):!1},show_icon:function(t){var n,r,i;if(e.isArray(t)){for(t=t.slice(),n=0,r=t.length;r>n;n++)this.show_icon(t[n]);return!0}return t=this.get_node(t),t&&"#"!==t?(i=this.get_node(t,!0),t.icon=i.length?i.children("a").children(".jstree-themeicon").attr("rel"):!0,t.icon||(t.icon=!0),i.children("a").children(".jstree-themeicon").removeClass("jstree-themeicon-hidden"),!0):!1}},e.vakata={},e.fn.vakata_reverse=[].reverse,e.vakata.attributes=function(t,n){t=e(t)[0];var r=n?{}:[];return t&&t.attributes&&e.each(t.attributes,function(t,i){-1===e.inArray(i.nodeName.toLowerCase(),["style","contenteditable","hasfocus","tabindex"])&&null!==i.nodeValue&&""!==e.trim(i.nodeValue)&&(n?r[i.nodeName]=i.nodeValue:r.push(i.nodeName))}),r},e.vakata.array_unique=function(e){var t=[],n,r,i;for(n=0,i=e.length;i>n;n++){for(r=0;n>=r;r++)if(e[n]===e[r])break;r===n&&t.push(e[n])}return t},e.vakata.array_remove=function(e,t,n){var r=e.slice((n||t)+1||e.length);return e.length=0>t?e.length+t:t,e.push.apply(e,r),e},e.vakata.array_remove_item=function(t,n){var r=e.inArray(n,t);return-1!==r?e.vakata.array_remove(t,r):t},function(){var t={},n=function(e){e=e.toLowerCase();var t=/(chrome)[ \/]([\w.]+)/.exec(e)||/(webkit)[ \/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||0>e.indexOf("compatible")&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(e)||[];return{browser:t[1]||"",version:t[2]||"0"}},r=n(window.navigator.userAgent);r.browser&&(t[r.browser]=!0,t.version=r.version),t.chrome?t.webkit=!0:t.webkit&&(t.safari=!0),e.vakata.browser=t}(),e.vakata.browser.msie&&8>e.vakata.browser.version&&(e.jstree.defaults.core.animation=0),function(e,t){var n=function(e){var t=/["\\\x00-\x1f\x7f-\x9f]/g,n={"\b":"\\b","	":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"};return e.match(t)?'"'+e.replace(t,function(e){var t=n[e];return"string"==typeof t?t:(t=e.charCodeAt(),"\\u00"+Math.floor(t/16).toString(16)+(t%16).toString(16))})+'"':'"'+e+'"'};e.vakata.json={encode:JSON&&JSON.stringify?JSON.stringify:function(r){if(null===r)return"null";var i=[],s;switch(typeof r){case"undefined":return t;case"number":case"boolean":return""+r;case"string":return n(r);case"object":if(e.isFunction(r.toJSON))return e.vakata.json.encode(r.toJSON());if(r.constructor===Date)return'"'+r.getUTCFullYear()+"-"+("0"+(r.getUTCMonth()+1)+"").slice(-2)+"-"+("0"+r.getUTCDate()+"").slice(-2)+"T"+("0"+r.getUTCHours()+"").slice(-2)+":"+("0"+r.getUTCMinutes()+"").slice(-2)+":"+("0"+r.getUTCSeconds()+"").slice(-2)+"."+("00"+r.getUTCMilliseconds()+"").slice(-3)+'Z"';if(r.constructor===Array){for(s=0;r.length>s;s++)i.push(e.vakata.json.encode(r[s])||"null");return"["+i.join(",")+"]"}return e.each(r,function(r,s){return e.isFunction(s)?!0:(r="number"==typeof r?'"'+r+'"':n(r),s=e.vakata.json.encode(s),i.push(r+":"+s),t)}),"{"+i.join(", ")+"}"}},decode:JSON&&JSON.parse?JSON.parse:function(t){return e.parseJSON(t)}}}(jQuery);var u=document.createElement("I");u.className="jstree-icon jstree-checkbox",e.jstree.defaults.checkbox={visible:!0,three_state:!0,whole_node:!0,keep_selected_style:!0},e.jstree.plugins.checkbox=function(t,n){this.bind=function(){n.bind.call(this),this._data.checkbox.uto=!1,this.element.on("init.jstree",e.proxy(function(){this._data.checkbox.visible=this.settings.checkbox.visible,this.settings.checkbox.keep_selected_style||this.element.addClass("jstree-checkbox-no-clicked")},this)).on("loading.jstree",e.proxy(function(){this[this._data.checkbox.visible?"show_checkboxes":"hide_checkboxes"]()},this)),this.settings.checkbox.three_state&&this.element.on("changed.jstree move_node.jstree copy_node.jstree redraw.jstree open_node.jstree",e.proxy(function(){this._data.checkbox.uto&&clearTimeout(this._data.checkbox.uto),this._data.checkbox.uto=setTimeout(e.proxy(this._undetermined,this),50)},this)).on("model.jstree",e.proxy(function(t,n){var r=this._model.data,i=r[n.parent],s=n.nodes,a=[],o,d,l,c,h,_;if(i.state.selected){for(d=0,l=s.length;l>d;d++)r[s[d]].state.selected=!0;this._data.core.selected=this._data.core.selected.concat(s)}else for(d=0,l=s.length;l>d;d++)if(r[s[d]].state.selected){for(c=0,h=r[s[d]].children_d.length;h>c;c++)r[r[s[d]].children_d[c]].state.selected=!0;this._data.core.selected=this._data.core.selected.concat(r[s[d]].children_d)}for(d=0,l=i.children_d.length;l>d;d++)r[i.children_d[d]].children.length||a.push(r[i.children_d[d]].parent);for(a=e.vakata.array_unique(a),c=0,h=a.length;h>c;c++){i=r[a[c]];while(i&&"#"!==i.id){for(o=0,d=0,l=i.children.length;l>d;d++)o+=r[i.children[d]].state.selected;if(o!==l)break;i.state.selected=!0,this._data.core.selected.push(i.id),_=this.get_node(i,!0),_&&_.length&&_.children(".jstree-anchor").addClass("jstree-clicked"),i=this.get_node(i.parent)}}this._data.core.selected=e.vakata.array_unique(this._data.core.selected)},this)).on("select_node.jstree",e.proxy(function(t,n){var r=n.node,i=this._model.data,s=this.get_node(r.parent),a=this.get_node(r,!0),o,d,l,c;for(this._data.core.selected=e.vakata.array_unique(this._data.core.selected.concat(r.children_d)),o=0,d=r.children_d.length;d>o;o++)i[r.children_d[o]].state.selected=!0;while(s&&"#"!==s.id){for(l=0,o=0,d=s.children.length;d>o;o++)l+=i[s.children[o]].state.selected;if(l!==d)break;s.state.selected=!0,this._data.core.selected.push(s.id),c=this.get_node(s,!0),c&&c.length&&c.children(".jstree-anchor").addClass("jstree-clicked"),s=this.get_node(s.parent)}a.length&&a.find(".jstree-anchor").addClass("jstree-clicked")},this)).on("deselect_node.jstree",e.proxy(function(t,n){var r=n.node,i=this.get_node(r,!0),s,a,o;for(s=0,a=r.children_d.length;a>s;s++)this._model.data[r.children_d[s]].state.selected=!1;for(s=0,a=r.parents.length;a>s;s++)this._model.data[r.parents[s]].state.selected=!1,o=this.get_node(r.parents[s],!0),o&&o.length&&o.children(".jstree-anchor").removeClass("jstree-clicked");for(o=[],s=0,a=this._data.core.selected.length;a>s;s++)-1===e.inArray(this._data.core.selected[s],r.children_d)&&-1===e.inArray(this._data.core.selected[s],r.parents)&&o.push(this._data.core.selected[s]);this._data.core.selected=e.vakata.array_unique(o),i.length&&i.find(".jstree-anchor").removeClass("jstree-clicked")},this)).on("delete_node.jstree",e.proxy(function(e,t){var n=this.get_node(t.parent),r=this._model.data,i,s,a,o;while(n&&"#"!==n.id){for(a=0,i=0,s=n.children.length;s>i;i++)a+=r[n.children[i]].state.selected;if(a!==s)break;n.state.selected=!0,this._data.core.selected.push(n.id),o=this.get_node(n,!0),o&&o.length&&o.children(".jstree-anchor").addClass("jstree-clicked"),n=this.get_node(n.parent)}},this)).on("move_node.jstree",e.proxy(function(t,n){var r=n.is_multi,i=n.old_parent,s=this.get_node(n.parent),a=this._model.data,o,d,l,c,h;if(!r){o=this.get_node(i);while(o&&"#"!==o.id){for(d=0,l=0,c=o.children.length;c>l;l++)d+=a[o.children[l]].state.selected;if(d!==c)break;o.state.selected=!0,this._data.core.selected.push(o.id),h=this.get_node(o,!0),h&&h.length&&h.children(".jstree-anchor").addClass("jstree-clicked"),o=this.get_node(o.parent)}}o=s;while(o&&"#"!==o.id){for(d=0,l=0,c=o.children.length;c>l;l++)d+=a[o.children[l]].state.selected;if(d===c)o.state.selected||(o.state.selected=!0,this._data.core.selected.push(o.id),h=this.get_node(o,!0),h&&h.length&&h.children(".jstree-anchor").addClass("jstree-clicked"));else{if(!o.state.selected)break;o.state.selected=!1,this._data.core.selected=e.vakata.array_remove_item(this._data.core.selected,o.id),h=this.get_node(o,!0),h&&h.length&&h.children(".jstree-anchor").removeClass("jstree-clicked")}o=this.get_node(o.parent)}},this))},this._undetermined=function(){var t,n,r=this._model.data,i=this._data.core.selected,s=[],a=this;for(t=0,n=i.length;n>t;t++)r[i[t]]&&r[i[t]].parents&&(s=s.concat(r[i[t]].parents));for(this.element.find(".jstree-closed").not(":has(ul)").each(function(){var e=a.get_node(this);!e.state.loaded&&e.original&&e.original.state&&e.original.state.undetermined&&e.original.state.undetermined===!0&&(s.push(e.id),s=s.concat(e.parents))}),s=e.vakata.array_unique(s),t=e.inArray("#",s),-1!==t&&(s=e.vakata.array_remove(s,t)),this.element.find(".jstree-undetermined").removeClass("jstree-undetermined"),t=0,n=s.length;n>t;t++)r[s[t]].state.selected||(i=this.get_node(s[t],!0),i&&i.length&&i.children("a").children(".jstree-checkbox").addClass("jstree-undetermined"))},this.redraw_node=function(t,r,i){if(t=n.redraw_node.call(this,t,r,i)){var s=t.getElementsByTagName("A")[0];s.insertBefore(u.cloneNode(),s.childNodes[0])}return!i&&this.settings.checkbox.three_state&&(this._data.checkbox.uto&&clearTimeout(this._data.checkbox.uto),this._data.checkbox.uto=setTimeout(e.proxy(this._undetermined,this),50)),t},this.activate_node=function(t,r){return(this.settings.checkbox.whole_node||e(r.target).hasClass("jstree-checkbox"))&&(r.ctrlKey=!0),n.activate_node.call(this,t,r)},this.show_checkboxes=function(){this._data.core.themes.checkboxes=!0,this.element.children("ul").removeClass("jstree-no-checkboxes")},this.hide_checkboxes=function(){this._data.core.themes.checkboxes=!1,this.element.children("ul").addClass("jstree-no-checkboxes")},this.toggle_checkboxes=function(){this._data.core.themes.checkboxes?this.hide_checkboxes():this.show_checkboxes()}},e.jstree.defaults.contextmenu={select_node:!0,show_at_node:!0,items:function(t){return{create:{separator_before:!1,separator_after:!0,_disabled:!1,label:"Create",action:function(t){var n=e.jstree.reference(t.reference),r=n.get_node(t.reference);n.create_node(r,{},"last",function(e){setTimeout(function(){n.edit(e)},0)})}},rename:{separator_before:!1,separator_after:!1,_disabled:!1,label:"Rename",action:function(t){var n=e.jstree.reference(t.reference),r=n.get_node(t.reference);n.edit(r)}},remove:{separator_before:!1,icon:!1,separator_after:!1,_disabled:!1,label:"Delete",action:function(t){var n=e.jstree.reference(t.reference),r=n.get_node(t.reference);n.is_selected(r)?n.delete_node(n.get_selected()):n.delete_node(r)}},ccp:{separator_before:!0,icon:!1,separator_after:!1,label:"Edit",action:!1,submenu:{cut:{separator_before:!1,separator_after:!1,label:"Cut",action:function(t){var n=e.jstree.reference(t.reference),r=n.get_node(t.reference);n.is_selected(r)?n.cut(n.get_selected()):n.cut(r)}},copy:{separator_before:!1,icon:!1,separator_after:!1,label:"Copy",action:function(t){var n=e.jstree.reference(t.reference),r=n.get_node(t.reference);n.is_selected(r)?n.copy(n.get_selected()):n.copy(r)}},paste:{separator_before:!1,icon:!1,_disabled:!this.can_paste(),separator_after:!1,label:"Paste",action:function(t){var n=e.jstree.reference(t.reference),r=n.get_node(t.reference);n.paste(r)}}}}}}},e.jstree.plugins.contextmenu=function(n,r){this.bind=function(){r.bind.call(this),this.element.on("contextmenu.jstree","a",e.proxy(function(e){e.preventDefault(),this.is_loading(e.currentTarget)||this.show_contextmenu(e.currentTarget,e.pageX,e.pageY)},this)).on("click.jstree","a",e.proxy(function(t){this._data.contextmenu.visible&&e.vakata.context.hide()},this)),e(document).on("context_hide.vakata",e.proxy(function(){this._data.contextmenu.visible=!1},this))},this.teardown=function(){this._data.contextmenu.visible&&e.vakata.context.hide(),r.teardown.call(this)},this.show_contextmenu=function(n,r,i){if(n=this.get_node(n),!n||"#"===n.id)return!1;var s=this.settings.contextmenu,a=this.get_node(n,!0),o=a.children(".jstree-anchor"),d=!1,l=!1;(s.show_at_node||r===t||i===t)&&(d=o.offset(),r=d.left,i=d.top+this._data.core.li_height),this.settings.contextmenu.select_node&&!this.is_selected(n)&&(this.deselect_all(),this.select_node(n)),l=s.items,e.isFunction(l)&&(l=l.call(this,n)),e(document).one("context_show.vakata",e.proxy(function(t,n){var r="jstree-contextmenu jstree-"+this.get_theme()+"-contextmenu";e(n.element).addClass(r)},this)),this._data.contextmenu.visible=!0,e.vakata.context.show(o,{x:r,y:i},l),this.trigger("show_contextmenu",{node:n,x:r,y:i})}},function(e){var n=!1,r={element:!1,reference:!1,position_x:0,position_y:0,items:[],html:"",is_visible:!1};e.vakata.context={settings:{hide_onmouseleave:0,icons:!0},_trigger:function(t){e(document).triggerHandler("context_"+t+".vakata",{reference:r.reference,element:r.element,position:{x:r.position_x,y:r.position_y}})},_execute:function(e){return e=r.items[e],e&&!e._disabled&&e.action?e.action.call(null,{item:e,reference:r.reference,element:r.element,position:{x:r.position_x,y:r.position_y}}):!1},_parse:function(n,i){if(!n)return!1;i||(r.html="",r.items=[]);var s="",a=!1,o;return i&&(s+="<ul>"),e.each(n,function(n,i){return i?(r.items.push(i),!a&&i.separator_before&&(s+="<li class='vakata-context-separator'><a href='#' "+(e.vakata.context.settings.icons?"":'style="margin-left:0px;"')+">&#160;<"+"/a><"+"/li>"),a=!1,s+="<li class='"+(i._class||"")+(i._disabled?" vakata-contextmenu-disabled ":"")+"'>",s+="<a href='#' rel='"+(r.items.length-1)+"'>",e.vakata.context.settings.icons&&(s+="<ins ",i.icon&&(s+=-1!==i.icon.indexOf("/")||-1!==i.icon.indexOf(".")?" style='background:url(\""+i.icon+"\") center center no-repeat' ":" class='"+i.icon+"' "),s+=">&#160;</ins><span>&#160;</span>"),s+=i.label+"<"+"/a>",i.submenu&&(o=e.vakata.context._parse(i.submenu,!0),o&&(s+=o)),s+="</li>",i.separator_after&&(s+="<li class='vakata-context-separator'><a href='#' "+(e.vakata.context.settings.icons?"":'style="margin-left:0px;"')+">&#160;<"+"/a><"+"/li>",a=!0),t):!0}),s=s.replace(/<li class\='vakata-context-separator'\><\/li\>$/,""),i&&(s+="</ul>"),i||(r.html=s,e.vakata.context._trigger("parse")),s.length>10?s:!1},_show_submenu:function(t){if(t=e(t),t.length&&t.children("ul").length){var r=t.children("ul"),i=t.offset().left+t.outerWidth(),s=t.offset().top,a=r.width(),o=r.height(),d=e(window).width()+e(window).scrollLeft(),l=e(window).height()+e(window).scrollTop();n?t[0>i-(a+10+t.outerWidth())?"addClass":"removeClass"]("vakata-context-left"):t[i+a+10>d?"addClass":"removeClass"]("vakata-context-right"),s+o+10>l&&r.css("bottom","-1px"),r.show()}},show:function(t,i,s){var a,o,d,l,c,h,_,u,f=!0;switch(r.element&&r.element.length&&r.element.width(""),f){case!i&&!t:return!1;case!!i&&!!t:r.reference=t,r.position_x=i.x,r.position_y=i.y;break;case!i&&!!t:r.reference=t,a=t.offset(),r.position_x=a.left+t.outerHeight(),r.position_y=a.top;break;case!!i&&!t:r.position_x=i.x,r.position_y=i.y}t&&!s&&e(t).data("vakata_contextmenu")&&(s=e(t).data("vakata_contextmenu")),e.vakata.context._parse(s)&&r.element.html(r.html),r.items.length&&(o=r.element,d=r.position_x,l=r.position_y,c=o.width(),h=o.height(),_=e(window).width()+e(window).scrollLeft(),u=e(window).height()+e(window).scrollTop(),n&&(d-=o.outerWidth(),e(window).scrollLeft()+20>d&&(d=e(window).scrollLeft()+20)),d+c+20>_&&(d=_-(c+20)),l+h+20>u&&(l=u-(h+20)),r.element.css({left:d,top:l}).show().find("a:eq(0)").focus().parent().addClass("vakata-context-hover"),r.is_visible=!0,e.vakata.context._trigger("show"))},hide:function(){r.is_visible&&(r.element.hide().find("ul").hide().end().find(":focus").blur(),r.is_visible=!1,e.vakata.context._trigger("hide"))}},e(function(){n="rtl"===e("body").css("direction");var t=!1;r.element=e("<ul class='vakata-context'></ul>"),r.element.on("mouseenter","li",function(n){n.stopImmediatePropagation(),e.contains(this,n.relatedTarget)||(t&&clearTimeout(t),r.element.find(".vakata-context-hover").removeClass("vakata-context-hover").end(),e(this).siblings().find("ul").hide().end().end().parentsUntil(".vakata-context","li").addBack().addClass("vakata-context-hover"),e.vakata.context._show_submenu(this))}).on("mouseleave","li",function(t){e.contains(this,t.relatedTarget)||e(this).find(".vakata-context-hover").addBack().removeClass("vakata-context-hover")}).on("mouseleave",function(n){e(this).find(".vakata-context-hover").removeClass("vakata-context-hover"),e.vakata.context.settings.hide_onmouseleave&&(t=setTimeout(function(t){return function(){e.vakata.context.hide()}}(this),e.vakata.context.settings.hide_onmouseleave))}).on("click","a",function(e){e.preventDefault()}).on("mouseup","a",function(t){e(this).blur().parent().hasClass("vakata-context-disabled")||e.vakata.context._execute(e(this).attr("rel"))===!1||e.vakata.context.hide()}).on("keydown","a",function(t){var n=null;switch(t.which){case 13:case 32:t.type="mouseup",t.preventDefault(),e(t.currentTarget).trigger(t);break;case 37:r.is_visible&&(r.element.find(".vakata-context-hover").last().parents("li:eq(0)").find("ul").hide().find(".vakata-context-hover").removeClass("vakata-context-hover").end().end().children("a").focus(),t.stopImmediatePropagation(),t.preventDefault());break;case 38:r.is_visible&&(n=r.element.find("ul:visible").addBack().last().children(".vakata-context-hover").removeClass("vakata-context-hover").prevAll("li:not(.vakata-context-separator)").first(),n.length||(n=r.element.find("ul:visible").addBack().last().children("li:not(.vakata-context-separator)").last()),n.addClass("vakata-context-hover").children("a").focus(),t.stopImmediatePropagation(),t.preventDefault());break;case 39:r.is_visible&&(r.element.find(".vakata-context-hover").last().children("ul").show().children("li:not(.vakata-context-separator)").removeClass("vakata-context-hover").first().addClass("vakata-context-hover").children("a").focus(),t.stopImmediatePropagation(),t.preventDefault());break;case 40:r.is_visible&&(n=r.element.find("ul:visible").addBack().last().children(".vakata-context-hover").removeClass("vakata-context-hover").nextAll("li:not(.vakata-context-separator)").first(),n.length||(n=r.element.find("ul:visible").addBack().last().children("li:not(.vakata-context-separator)").first()),n.addClass("vakata-context-hover").children("a").focus(),t.stopImmediatePropagation(),t.preventDefault());break;case 27:e.vakata.context.hide(),t.preventDefault();break;default:}}).appendTo("body"),e(document).on("mousedown",function(t){r.is_visible&&!e.contains(r.element[0],t.target)&&e.vakata.context.hide()}).on("context_show.vakata",function(e,t){r.element.find("li:has(ul)").children("a").addClass("vakata-context-parent"),n&&r.element.addClass("vakata-context-rtl").css("direction","rtl"),r.element.find("ul").hide().end()})})}(e),e.jstree.defaults.dnd={copy:!0,open_timeout:500},e.jstree.plugins.dnd=function(n,r){this.bind=function(){r.bind.call(this),this.element.on("mousedown","a",e.proxy(function(n){var r=this.get_node(n.target),i=this.is_selected(r)?this.get_selected().length:1;return r&&r.id&&"#"!==r.id&&1===n.which?(this.element.trigger("mousedown.jstree"),e.vakata.dnd.start(n,{jstree:!0,origin:this,obj:this.get_node(r,!0),nodes:i>1?this.get_selected():[r.id]},'<div id="jstree-dnd" class="jstree-'+this.get_theme()+'"><i class="jstree-icon jstree-er"></i>'+(i>1?i+" "+this.get_string("nodes"):this.get_text(n.currentTarget,!0))+'<ins class="jstree-copy" style="display:none;">+</ins></div>')):t},this))}},e(function(){var n=!1,r=!1,i=e('<div id="jstree-marker">&#160;</div>').hide().appendTo("body");e(document).bind("dnd_start.vakata",function(e,t){n=!1}).bind("dnd_move.vakata",function(s,a){if(r&&clearTimeout(r),a.data.jstree&&(!a.event.target.id||"jstree-marker"!==a.event.target.id)){var o=e.jstree.reference(a.event.target),d=!1,l=!1,c=!1,h,_,u,f,g,p,m,v,j,y,k,x;if(o&&o._data&&o._data.dnd)if(i.attr("class","jstree-"+o.get_theme()),a.helper.children().attr("class","jstree-"+o.get_theme()).find(".jstree-copy:eq(0)")[a.data.origin&&a.data.origin.settings.dnd.copy&&(a.event.metaKey||a.event.ctrlKey)?"show":"hide"](),a.event.target!==o.element[0]&&a.event.target!==o.get_container_ul()[0]||0!==o.get_container_ul().children().length){if(d=e(a.event.target).closest("a"),d&&d.length&&d.parent().is(".jstree-closed, .jstree-open, .jstree-leaf")&&(l=d.offset(),c=a.event.pageY-l.top,u=d.height(),p=u/3>c?["b","i","a"]:c>u-u/3?["a","i","b"]:c>u/2?["i","a","b"]:["i","b","a"],e.each(p,function(s,c){switch(c){case"b":h=l.left-6,_=l.top-5,f=o.get_parent(d),g=d.parent().index();break;case"i":h=l.left-2,_=l.top-5+u/2+1,f=d.parent(),g=0;break;case"a":h=l.left-6,_=l.top-5+u,f=o.get_parent(d),g=d.parent().index()+1}for(m=!0,v=0,j=a.data.nodes.length;j>v;v++)if(y=a.data.origin&&a.data.origin.settings.dnd.copy&&(a.event.metaKey||a.event.ctrlKey)?"copy_node":"move_node",k=g,"move_node"===y&&"a"===c&&a.data.origin&&a.data.origin===o&&f===o.get_parent(a.data.nodes[v])&&(x=o.get_node(f),k>e.inArray(a.data.nodes[v],x.children)&&(k-=1)),m=m&&o.check(y,a.data.nodes[v],f,k),!m)break;return m?("i"===c&&d.parent().is(".jstree-closed")&&o.settings.dnd.open_timeout&&(r=setTimeout(function(e,t){return function(){e.open_node(t)}}(o,d),o.settings.dnd.open_timeout)),n={ins:o,par:f,pos:g},i.css({left:h+"px",top:_+"px"}).show(),a.helper.find(".jstree-icon:eq(0)").removeClass("jstree-er").addClass("jstree-ok"),p=!0,!1):t
}),p===!0))return}else{for(m=!0,v=0,j=a.data.nodes.length;j>v;v++)if(m=m&&o.check(a.data.origin&&a.data.origin.settings.dnd.copy&&(a.event.metaKey||a.event.ctrlKey)?"copy_node":"move_node",a.data.nodes[v],"#","last"),!m)break;if(m)return n={ins:o,par:"#",pos:"last"},i.hide(),a.helper.find(".jstree-icon:eq(0)").removeClass("jstree-er").addClass("jstree-ok"),t}n=!1,a.helper.find(".jstree-icon").removeClass("jstree-ok").addClass("jstree-er"),i.hide()}}).bind("dnd_scroll.vakata",function(e,t){t.data.jstree&&(i.hide(),n=!1,t.helper.find(".jstree-icon:eq(0)").removeClass("jstree-ok").addClass("jstree-er"))}).bind("dnd_stop.vakata",function(e,t){if(r&&clearTimeout(r),t.data.jstree&&(i.hide(),n)){var s,a,o=[];for(s=0,a=t.data.nodes.length;a>s;s++)o[s]=t.data.origin?t.data.origin.get_node(t.data.nodes[s]):t.data.nodes[s];n.ins[t.data.origin&&t.data.origin.settings.dnd.copy&&(t.event.metaKey||t.event.ctrlKey)?"copy_node":"move_node"](o,n.par,n.pos)}}).bind("keyup keydown",function(t,n){n=e.vakata.dnd._get(),n.data&&n.data.jstree&&n.helper.find(".jstree-copy:eq(0)")[n.data.origin&&n.data.origin.settings.dnd.copy&&(t.metaKey||t.ctrlKey)?"show":"hide"]()})}),function(e){e.fn.vakata_reverse=[].reverse;var n={element:!1,is_down:!1,is_drag:!1,helper:!1,helper_w:0,data:!1,init_x:0,init_y:0,scroll_l:0,scroll_t:0,scroll_e:!1,scroll_i:!1};e.vakata.dnd={settings:{scroll_speed:10,scroll_proximity:20,helper_left:5,helper_top:10,threshold:5},_trigger:function(t,n){var r=e.vakata.dnd._get();r.event=n,e(document).triggerHandler("dnd_"+t+".vakata",r)},_get:function(){return{data:n.data,element:n.element,helper:n.helper}},_clean:function(){n.helper&&n.helper.remove(),n.scroll_i&&(clearInterval(n.scroll_i),n.scroll_i=!1),n={element:!1,is_down:!1,is_drag:!1,helper:!1,helper_w:0,data:!1,init_x:0,init_y:0,scroll_l:0,scroll_t:0,scroll_e:!1,scroll_i:!1},e(document).unbind("mousemove",e.vakata.dnd.drag),e(document).unbind("mouseup",e.vakata.dnd.stop)},_scroll:function(t){if(!n.scroll_e||!n.scroll_l&&!n.scroll_t)return n.scroll_i&&(clearInterval(n.scroll_i),n.scroll_i=!1),!1;if(!n.scroll_i)return n.scroll_i=setInterval(e.vakata.dnd._scroll,100),!1;if(t===!0)return!1;var r=n.scroll_e.scrollTop(),i=n.scroll_e.scrollLeft();n.scroll_e.scrollTop(r+n.scroll_t*e.vakata.dnd.settings.scroll_speed),n.scroll_e.scrollLeft(i+n.scroll_l*e.vakata.dnd.settings.scroll_speed),(r!==n.scroll_e.scrollTop()||i!==n.scroll_e.scrollLeft())&&e.vakata.dnd._trigger("scroll",n.scroll_e)},start:function(t,r,i){n.is_drag&&e.vakata.dnd.stop({});try{t.currentTarget.unselectable="on",t.currentTarget.onselectstart=function(){return!1},t.currentTarget.style&&(t.currentTarget.style.MozUserSelect="none")}catch(s){}return n.init_x=t.pageX,n.init_y=t.pageY,n.data=r,n.is_down=!0,n.element=t.currentTarget,i!==!1&&(n.helper=e("<div id='vakata-dnd'></div>").html(i).css({display:"block",margin:"0",padding:"0",position:"absolute",top:"-2000px",lineHeight:"16px",zIndex:"10000"})),e(document).bind("mousemove",e.vakata.dnd.drag),e(document).bind("mouseup",e.vakata.dnd.stop),!1},drag:function(r){if(n.is_down){if(!n.is_drag){if(!(Math.abs(r.pageX-n.init_x)>e.vakata.dnd.settings.threshold||Math.abs(r.pageY-n.init_y)>e.vakata.dnd.settings.threshold))return;n.helper&&(n.helper.appendTo("body"),n.helper_w=n.helper.outerWidth()),n.is_drag=!0,e.vakata.dnd._trigger("start",r)}var i=!1,s=!1,a=!1,o=!1,d=!1,l=!1,c=!1,h=!1,_=!1,u=!1;n.scroll_t=0,n.scroll_l=0,n.scroll_e=!1,e(r.target).parentsUntil("body").addBack().vakata_reverse().filter(function(){return/^auto|scroll$/.test(e(this).css("overflow"))&&(this.scrollHeight>this.offsetHeight||this.scrollWidth>this.offsetWidth)}).each(function(){var i=e(this),s=i.offset();return this.scrollHeight>this.offsetHeight&&(s.top+i.height()-r.pageY<e.vakata.dnd.settings.scroll_proximity&&(n.scroll_t=1),r.pageY-s.top<e.vakata.dnd.settings.scroll_proximity&&(n.scroll_t=-1)),this.scrollWidth>this.offsetWidth&&(s.left+i.width()-r.pageX<e.vakata.dnd.settings.scroll_proximity&&(n.scroll_l=1),r.pageX-s.left<e.vakata.dnd.settings.scroll_proximity&&(n.scroll_l=-1)),n.scroll_t||n.scroll_l?(n.scroll_e=e(this),!1):t}),n.scroll_e||(i=e(document),s=e(window),a=i.height(),o=s.height(),d=i.width(),l=s.width(),c=i.scrollTop(),h=i.scrollLeft(),a>o&&r.pageY-c<e.vakata.dnd.settings.scroll_proximity&&(n.scroll_t=-1),a>o&&o-(r.pageY-c)<e.vakata.dnd.settings.scroll_proximity&&(n.scroll_t=1),d>l&&r.pageX-h<e.vakata.dnd.settings.scroll_proximity&&(n.scroll_l=-1),d>l&&l-(r.pageX-h)<e.vakata.dnd.settings.scroll_proximity&&(n.scroll_l=1),(n.scroll_t||n.scroll_l)&&(n.scroll_e=i)),n.scroll_e&&e.vakata.dnd._scroll(!0),n.helper&&(_=parseInt(r.pageY+e.vakata.dnd.settings.helper_top,10),u=parseInt(r.pageX+e.vakata.dnd.settings.helper_left,10),a&&_+25>a&&(_=a-50),d&&u+n.helper_w>d&&(u=d-(n.helper_w+2)),n.helper.css({left:u+"px",top:_+"px"})),e.vakata.dnd._trigger("move",r)}},stop:function(t){n.is_drag&&e.vakata.dnd._trigger("stop",t),e.vakata.dnd._clean()}}}(jQuery),e.jstree.defaults.search={ajax:!1,fuzzy:!0,case_sensitive:!1,show_only_matches:!1,close_opened_onclear:!0},e.jstree.plugins.search=function(t,n){this.bind=function(){n.bind.call(this),this._data.search.str="",this._data.search.dom=e(),this._data.search.res=[],this._data.search.opn=[],this.settings.search.show_only_matches&&this.element.on("search.jstree",function(t,n){n.nodes.length&&(e(this).find("li").hide().filter(".jstree-last").filter(function(){return this.nextSibling}).removeClass("jstree-last"),n.nodes.parentsUntil(".jstree").addBack().show().filter("ul").each(function(){e(this).children("li:visible").eq(-1).addClass("jstree-last")}))}).on("clear_search.jstree",function(t,n){n.nodes.length&&e(this).find("li").css("display","").filter(".jstree-last").filter(function(){return this.nextSibling}).removeClass("jstree-last")})},this.search=function(t,n){if(t===!1||""===e.trim(t))return this.clear_search();var r=this.settings.search,i=r.ajax?e.extend({},r.ajax):!1,s=null,a=[],o=[],d,l;if(this._data.search.res.length&&this.clear_search(),!n&&i!==!1)return i.data||(i.data={}),i.data.str=t,e.ajax(r.ajax).done(e.proxy(function(e){this._search_load(e,t)},this));if(this._data.search.str=t,this._data.search.dom=e(),this._data.search.res=[],this._data.search.opn=[],s=new e.vakata.search(t,!0,{caseSensitive:r.case_sensitive,fuzzy:r.fuzzy}),e.each(this._model.data,function(e,t){t.text&&s.search(t.text).isMatch&&(a.push(e),o=o.concat(t.parents))}),a.length){for(o=e.vakata.array_unique(o),this._search_open(o),d=0,l=a.length;l>d;d++)s=this.get_node(a[d],!0),s&&(this._data.search.dom=this._data.search.dom.add(s));this._data.search.res=a,this._data.search.dom.children(".jstree-anchor").addClass("jstree-search")}this.trigger("search",{nodes:this._data.search.dom,str:t,res:this._data.search.res})},this.clear_search=function(){this._data.search.dom.children(".jstree-anchor").removeClass("jstree-search"),this.settings.search.close_opened_onclear&&this.close_node(this._data.search.opn),this.trigger("clear_search",{nodes:this._data.search.dom,str:this._data.search.str,res:this._data.search.res}),this._data.search.str="",this._data.search.res=[],this._data.search.opn=[],this._data.search.dom=e()},this._search_open=function(t){var n=this;e.each(t.concat([]),function(e,r){r=document.getElementById(r),r&&n.is_closed(r)&&(n._data.search.opn.push(r.id),n.open_node(r,function(){n._search_open(t)}))})},this._search_load=function(t,n){var r=!0,i=this,s=i._model.data;e.each(t.concat([]),function(e,a){s[a]&&(s[a].state.loaded||(i.load_node(a,function(){i._search_load(t,n)}),r=!1))}),r&&this.search(n,!0)}},function(e){e.vakata.search=function(e,t,n){n=n||{},n.fuzzy!==!1&&(n.fuzzy=!0),e=n.caseSensitive?e:e.toLowerCase();var r=n.location||0,i=n.distance||100,s=n.threshold||.6,a=e.length,o,d,l,c;return a>32&&(n.fuzzy=!1),n.fuzzy&&(o=1<<a-1,d=function(){var t={},n=0;for(n=0;a>n;n++)t[e.charAt(n)]=0;for(n=0;a>n;n++)t[e.charAt(n)]|=1<<a-n-1;return t}(),l=function(e,t){var n=e/a,s=Math.abs(r-t);return i?n+s/i:s?1:n}),c=function(t){if(t=n.caseSensitive?t:t.toLowerCase(),e===t||-1!==t.indexOf(e))return{isMatch:!0,score:0};if(!n.fuzzy)return{isMatch:!1,score:1};var i,c,h=t.length,_=s,u=t.indexOf(e,r),f,g,p=a+h,m,v,j,y,k,x=1,b=[];for(-1!==u&&(_=Math.min(l(0,u),_),u=t.lastIndexOf(e,r+a),-1!==u&&(_=Math.min(l(0,u),_))),u=-1,i=0;a>i;i++){f=0,g=p;while(g>f)_>=l(i,r+g)?f=g:p=g,g=Math.floor((p-f)/2+f);for(p=g,v=Math.max(1,r-g+1),j=Math.min(r+g,h)+a,y=Array(j+2),y[j+1]=(1<<i)-1,c=j;c>=v;c--)if(k=d[t.charAt(c-1)],y[c]=0===i?(1|y[c+1]<<1)&k:(1|y[c+1]<<1)&k|(1|(m[c+1]|m[c])<<1)|m[c+1],y[c]&o&&(x=l(i,c-1),_>=x)){if(_=x,u=c-1,b.push(u),!(u>r))break;v=Math.max(1,2*r-u)}if(l(i+1,r)>_)break;m=y}return{isMatch:u>=0,score:x}},t===!0?{search:c}:c(t)}}(jQuery),e.jstree.defaults.sort=function(e,t){return this.get_text(e)>this.get_text(t)?1:-1},e.jstree.plugins.sort=function(t,n){this.bind=function(){n.bind.call(this),this.element.on("model.jstree",e.proxy(function(e,t){this.sort(t.parent,!0)},this)).on("rename_node.jstree create_node.jstree",e.proxy(function(e,t){this.sort(t.parent||t.node.parent,!1),this.redraw_node(t.parent||t.node.parent,!0)},this)).on("move_node.jstree copy_node.jstree",e.proxy(function(e,t){this.sort(t.parent,!1),this.redraw_node(t.parent,!0)},this))},this.sort=function(t,n){var r,i;if(t=this.get_node(t),t&&t.children&&t.children.length&&(t.children.sort(e.proxy(this.settings.sort,this)),n))for(r=0,i=t.children_d.length;i>r;r++)this.sort(t.children_d[r],!1)}};var f=!1;e.jstree.defaults.state={key:"jstree",events:"changed.jstree open_node.jstree close_node.jstree",ttl:!1},e.jstree.plugins.state=function(t,n){this.bind=function(){n.bind.call(this),this.element.on("ready.jstree",e.proxy(function(t,n){this.element.one("restore_state.jstree set_state.jstree",e.proxy(function(){this.element.on(this.settings.state.events,e.proxy(function(){f&&clearTimeout(f),f=setTimeout(e.proxy(function(){this.save_state()},this),100)},this))},this)),this.restore_state()},this))},this.save_state=function(){e.vakata.storage.set(this.settings.state.key,this.get_state(),this.settings.state.ttl)},this.restore_state=function(){var t=e.vakata.storage.get(this.settings.state.key);t&&this.set_state(t),this.trigger("restore_state",{state:t})},this.clear_state=function(){return e.vakata.storage.del(this.settings.state.key)}},function(e,t,n){var r=function(e){return e},i=function(e){return decodeURIComponent(e.replace(/\+/g," "))},s=e.vakata.cookie=function(a,o,d){var l,c,h,_,u,f,g,p;if(o!==n)return d=e.extend({},s.defaults,d),null===o&&(d.expires=-1),"number"==typeof d.expires&&(l=d.expires,c=d.expires=new Date,c.setDate(c.getDate()+l)),o=s.json?e.vakata.json.encode(o):o+"",o=[encodeURIComponent(a),"=",s.raw?o:encodeURIComponent(o),d.expires?"; expires="+d.expires.toUTCString():"",d.path?"; path="+d.path:"",d.domain?"; domain="+d.domain:"",d.secure?"; secure":""].join(""),t.cookie=o,o;for(h=s.raw?r:i,_=t.cookie.split("; "),u=0,f=_.length;f>u;u++)if(g=_[u].split("="),h(g.shift())===a)return p=h(g.join("=")),s.json?e.vakata.json.decode(p):p;return null};s.defaults={},e.vakata.removeCookie=function(t,n){return null!==e.vakata.cookie(t)?(e.vakata.cookie(t,null,n),!0):!1}}(jQuery,document),function(e,t){var n={},r={jStorage:"{}"},i=null,s=0,a=e.vakata.json.encode,o=e.vakata.json.decode,d=!1,l=!1;function c(){if(r.jStorage)try{n=o(r.jStorage+"")}catch(e){r.jStorage="{}"}else r.jStorage="{}";s=r.jStorage?(r.jStorage+"").length:0}function h(){try{r.jStorage=a(n),"userDataBehavior"===d&&(i.setAttribute("jStorage",r.jStorage),i.save("jStorage")),"cookie"===d&&e.vakata.cookie("__vjstorage",r.jStorage,{expires:365}),s=r.jStorage?(r.jStorage+"").length:0}catch(t){}}function _(e){if(!e||"string"!=typeof e&&"number"!=typeof e)throw new TypeError("Key name must be string or numeric");if("__jstorage_meta"===e)throw new TypeError("Reserved key name");return!0}function u(){var e=+new Date,t,r,i=1/0,s=!1;if(l!==!1&&clearTimeout(l),n.__jstorage_meta&&"object"==typeof n.__jstorage_meta.TTL){r=n.__jstorage_meta.TTL;for(t in r)r.hasOwnProperty(t)&&(e>=r[t]?(delete r[t],delete n[t],s=!0):i>r[t]&&(i=r[t]));1/0!==i&&(l=setTimeout(u,i-e)),s&&h()}}function f(){var n=!1,s;if(Object.prototype.hasOwnProperty.call(window,"localStorage"))try{window.localStorage.setItem("_tmptest","tmpval"),n=!0,window.localStorage.removeItem("_tmptest")}catch(a){}if(n)try{window.localStorage&&(r=window.localStorage,d="localStorage")}catch(a){}else if(Object.prototype.hasOwnProperty.call(window,"globalStorage"))try{window.globalStorage&&(r=window.globalStorage[window.location.hostname],d="globalStorage")}catch(a){}else{if(i=document.createElement("link"),i.addBehavior){i.style.behavior="url(#default#userData)",document.getElementsByTagName("head")[0].appendChild(i);try{i.load("jStorage"),s="{}",s=i.getAttribute("jStorage"),r.jStorage=s,d="userDataBehavior"}catch(a){}}if(!d&&(e.vakata.cookie("__vjstorage")||e.vakata.cookie("__vjstorage","{}",{expires:365})&&"{}"===e.vakata.cookie("__vjstorage"))&&(i=null,r.jStorage=e.vakata.cookie("__vjstorage"),d="cookie"),!d)return i=null,t}c(),u()}e.vakata.storage={version:"0.3.0",set:function(t,r,i){return _(t),"object"==typeof r&&(r=o(a(r))),n[t]=r,h(),i&&parseInt(i,10)&&e.vakata.storage.setTTL(t,parseInt(i,10)),r},get:function(e,r){return _(e),n.hasOwnProperty(e)?n[e]:r===t?null:r},del:function(e){return _(e),n.hasOwnProperty(e)?(delete n[e],n.__jstorage_meta&&"object"==typeof n.__jstorage_meta.TTL&&n.__jstorage_meta.TTL.hasOwnProperty(e)&&delete n.__jstorage_meta.TTL[e],h(),!0):!1},setTTL:function(e,t){var r=+new Date;return _(e),t=Number(t)||0,n.hasOwnProperty(e)?(n.__jstorage_meta||(n.__jstorage_meta={}),n.__jstorage_meta.TTL||(n.__jstorage_meta.TTL={}),t>0?n.__jstorage_meta.TTL[e]=r+t:delete n.__jstorage_meta.TTL[e],h(),u(),!0):!1},getTTL:function(e){var t=+new Date,r;return _(e),n.hasOwnProperty(e)&&n.__jstorage_meta.TTL&&n.__jstorage_meta.TTL[e]?(r=n.__jstorage_meta.TTL[e]-t,r||0):0},flush:function(){return n={},h(),!0},storageObj:function(){return e.extend(!0,{},n)},index:function(){var t=[];return e.each(n,function(e,n){"__jstorage_meta"!==e&&t.push(e)}),t},storageSize:function(){return s},currentBackend:function(){return d},storageAvailable:function(){return!!d}},f()}(jQuery),e.jstree.defaults.types={"#":{},"default":{}},e.jstree.plugins.types=function(n,r){this.init=function(e,t){r.init.call(this,e,t),this._model.data["#"].type="#"},this.bind=function(){r.bind.call(this),this.element.on("model.jstree",e.proxy(function(e,t){var n=this._model.data,r=t.nodes,i=this.settings.types,s,a,o="default";for(s=0,a=r.length;a>s;s++)o="default",n[r[s]].original&&n[r[s]].original.type&&i[n[r[s]].original.type]&&(o=n[r[s]].original.type),n[r[s]].data&&n[r[s]].data.jstree&&n[r[s]].data.jstree.type&&i[n[r[s]].data.jstree.type]&&(o=n[r[s]].data.jstree.type),n[r[s]].type=o,n[r[s]].icon===!0&&i[o].icon&&(n[r[s]].icon=i[o].icon)},this))},this.get_json=function(t,n){var i,s,a=this._model.data,o=r.get_json.call(this,t,n);if(o===!1)return!1;if(e.isArray(o))for(i=0,s=o.length;s>i;i++)o[i].type=o[i].id&&a[o[i].id]&&a[o[i].id].type?a[o[i].id].type:"default";else o.type=o.id&&a[o.id]&&a[o.id].type?a[o.id].type:"default";return o},this.check=function(n,i,s,a){if(r.check.call(this,n,i,s,a)===!1)return!1;i=i&&i.id?i:this.get_node(i),s=s&&s.id?s:this.get_node(s);var o=this._model.data,d,l,c,h;switch(n){case"create_node":case"move_node":case"copy_node":if("move_node"!==n||-1===e.inArray(i.id,s.children)){if(d=this.get_rules(s),d.max_children!==t&&-1!==d.max_children&&d.max_children===s.children.length)return!1;if(d.valid_children!==t&&-1!==d.valid_children&&-1===e.inArray(i.type,d.valid_children))return!1;if(i.children_d&&i.parents){for(l=0,c=0,h=i.children_d.length;h>c;c++)l=Math.max(l,o[i.children_d[c]].parents.length);l=l-i.parents.length+1}0>=l&&(l=1);do{if(d.max_depth!==t&&-1!==d.max_depth&&l>d.max_depth)return!1;s=this.get_node(s.parent),d=this.get_rules(s),l++}while(s)}}return!0},this.get_rules=function(e){if(e=this.get_node(e),!e)return!1;var n=this.get_type(e,!0);return n.max_depth===t&&(n.max_depth=-1),n.max_children===t&&(n.max_children=-1),n.valid_children===t&&(n.valid_children=-1),n},this.get_type=function(t,n){return t=this.get_node(t),t?n?e.extend({type:t.type},this.settings.types[t.type]):t.type:!1},this.set_type=function(t,n){var r,i,s,a,o;if(e.isArray(t)){for(t=t.slice(),i=0,s=t.length;s>i;i++)this.set_type(t[i],n);return!0}return r=this.settings.types,t=this.get_node(t),r[n]&&t?(a=t.type,o=this.get_icon(t),t.type=n,(o===!0||r[a]&&r[a].icon&&o===r[a].icon)&&this.set_icon(t,r[n].icon||!0),!0):!1}},e.jstree.plugins.unique=function(t,n){this.check=function(t,r,i,s){if(n.check.call(this,t,r,i,s)===!1)return!1;if(r=r&&r.id?r:this.get_node(r),i=i&&i.id?i:this.get_node(i),!i||!i.children)return!0;var a="rename_node"===t?s:r.text,o=[],d=this._model.data,l,c;for(l=0,c=i.children.length;c>l;l++)o.push(d[i.children[l]].text);switch(t){case"delete_node":return!0;case"rename_node":case"copy_node":return-1===e.inArray(a,o);case"move_node":return r.parent===i.id||-1===e.inArray(a,o)}return!0}};var g=document.createElement("DIV");g.setAttribute("unselectable","on"),g.className="jstree-wholerow",g.innerHTML="&#160;",e.jstree.plugins.wholerow=function(t,n){this.bind=function(){n.bind.call(this),this.element.on("loading",e.proxy(function(){g.style.height=this._data.core.li_height+"px"},this)).on("ready.jstree set_state.jstree",e.proxy(function(){this.hide_dots()},this)).on("ready.jstree",e.proxy(function(){this.get_container_ul().addClass("jstree-wholerow-ul")},this)).on("deselect_all.jstree",e.proxy(function(e,t){this.element.find(".jstree-wholerow-clicked").removeClass("jstree-wholerow-clicked")},this)).on("changed.jstree",e.proxy(function(e,t){this.element.find(".jstree-wholerow-clicked").removeClass("jstree-wholerow-clicked");var n=!1,r,i;for(r=0,i=t.selected.length;i>r;r++)n=this.get_node(t.selected[r],!0),n&&n.length&&n.children(".jstree-wholerow").addClass("jstree-wholerow-clicked")},this)).on("open_node.jstree",e.proxy(function(e,t){this.get_node(t.node,!0).find(".jstree-clicked").parent().children(".jstree-wholerow").addClass("jstree-wholerow-clicked")},this)).on("hover_node.jstree dehover_node.jstree",e.proxy(function(t,n){this.element.find(".jstree-wholerow-hovered").removeClass("jstree-wholerow-hovered"),"hover_node"===t.type&&this.get_node(n.node,!0).each(function(){e(this).children(".jstree-wholerow").addClass("jstree-wholerow-hovered")})},this)).on("contextmenu.jstree",".jstree-wholerow",e.proxy(function(t){t.preventDefault(),e(t.currentTarget).closest("li").children("a:eq(0)").trigger("contextmenu",t)},this)).on("click.jstree",".jstree-wholerow",function(t){t.stopImmediatePropagation();var n=e.Event("click",{metaKey:t.metaKey,ctrlKey:t.ctrlKey,altKey:t.altKey,shiftKey:t.shiftKey});e(t.currentTarget).closest("li").children("a:eq(0)").trigger(n).focus()}).on("click.jstree",".jstree-leaf > .jstree-ocl",e.proxy(function(t){t.stopImmediatePropagation();var n=e.Event("click",{metaKey:t.metaKey,ctrlKey:t.ctrlKey,altKey:t.altKey,shiftKey:t.shiftKey});e(t.currentTarget).closest("li").children("a:eq(0)").trigger(n).focus()},this)).on("mouseover.jstree",".jstree-wholerow, .jstree-icon",e.proxy(function(e){return e.stopImmediatePropagation(),this.hover_node(e.currentTarget),!1},this)).on("mouseleave.jstree","li",e.proxy(function(e){this.dehover_node(e.currentTarget)},this))},this.teardown=function(){this.settings.wholerow&&this.element.find(".jstree-wholerow").remove(),n.teardown.call(this)},this.redraw_node=function(t,r,i){if(t=n.redraw_node.call(this,t,r,i)){var s=g.cloneNode(!0);-1!==e.inArray(t.id,this._data.core.selected)&&(s.className+=" jstree-wholerow-clicked"),t.insertBefore(s,t.childNodes[0])}return t}}}});