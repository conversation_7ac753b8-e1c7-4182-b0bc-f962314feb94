html { overflow-y:scroll; background:#D9E3CB url('./images/tree.png') left bottom fixed no-repeat; }
body { background:transparent; line-height:1.6em; }
.list-group-item-text { line-height:1.6em; }
.container { min-width:320px; max-width:960px; }
#head { background:#333; border-bottom:4px solid #73796B; min-height:75px; }
#logo { margin:0; padding:0; height:60px; }
#logo span { position:absolute; right:0; top:1px; font-size:10px; background:#D9E3CB; box-shadow:0 0 4px black; padding:1px 6px 3px 6px; border-radius:4px; text-indent:0; color:black; font-weight:bold; }
#logo a { position:relative; display:block; margin:0 auto; height:100%; width:160px; overflow:hidden; text-indent:110%; white-space:nowrap; background:url('./images/logo.png') left 5px no-repeat; }
#content { box-shadow:0 20px 0px 20px rgba(255,255,255,0.3); }
#menu { text-align:center; vertical-align:top; }
#menu > li { margin:0 10px 0 0; display:inline-block; float:none; }
#menu > li > a { border-radius:5px; color:white; margin:12px 0 0 0; padding-top:8px; padding-bottom:8px; text-shadow:1px 1px 0 rgba(0,0,0,0.5); background:transparent; }
#menu > li > a:hover { background:#73796B; }
#menu > .active > a,
#menu > .active > a:hover { background:white; color:black; text-shadow:1px 1px 0 rgba(255,255,255,0.5); }
#head form { margin:14px auto; max-width:240px; }
#head input { border-radius:10px; background:white url('./images/find.png') 10px center no-repeat; padding-left:32px; }
.page { margin-top:-10px; background:white; border-radius:5px; box-shadow:0 0 10px rgba(0,0,0,0.7); padding-top:20px; padding-bottom:15px; display:none; }
h2 { margin:0 0 1em 0; padding:0 0 0.75em 0; text-align:center; color:#333; border-bottom:1px dotted #666; }
h3 { text-align:left; color:#73796B; font-family:Georgia, serif; font-style:italic; padding:0.5em; border-bottom:1px dotted; margin:0 0 1em 0; }
h3 > i { font-size:0.6em; }
h4 { margin-top:1em; }

#docs .nav {
	margin:0 -15px 1em -15px; font-size:1.2em; padding-left:25px; text-align:center;
	background-image: -webkit-gradient(linear, 0 100%, 0 0, from(#eee), color-stop(0.6, #fff));
	background-image: -webkit-linear-gradient(bottom, #eee, #fff 60%);
	background-image: -moz-linear-gradient(bottom, #eee, #fff 60%);
	background-image: -o-linear-gradient(bottom, #eee, #fff 60%);
	background-image: linear-gradient(bottom, #eee, #fff 60%);
}
#docs h3 { margin-left:-15px; margin-right:-15px; padding-left:25px; }

.spaced > li { margin-bottom:1.8em; }

.item { padding:12px 10px 0 10px; margin-bottom:10px; border-radius:5px; border:1px solid #eee; }
.item > .item-inner { display:none; }
.item:nth-child(2n) { background:#fcfcfc; }
.item > h4 { margin:0 0 10px 0; font-size:1em; overflow:hidden; cursor:pointer; }
.item > h4 > code { padding:5px 10px; font-size:1.1em; float:left; }
.item p { padding:10px 10px; margin:0; }
.params { margin:10px 10px; }
.params li { padding:10px 0; border-top:1px dotted silver; }
.params p code { font-size:14px; padding-left:6px; padding-right:6px; line-height:20px; display:inline-block; }
.param { display:inline-block; padding-left:10px; padding-right:10px; font-size:14px; line-height:20px; float:left; }
.return { color:white; background:#C7254E; float:left; font-size:14px; line-height:20px; }
.trigger { color:white; background:#286B1C; font-size:14px; line-height:20px; }
.type { color:white; background:silver; }
.params p { margin:0 0 0 190px; padding:0; }
.item > h4 > .meta { float:right; background:silver; color:white; cursor:auto; margin-left:10px; }
.item > h4 > .plugin { background:#d9e3cb; color:black; }
.private { opacity:0.5; transition:opacity 0.4s; }
.private:hover { opacity:1; }
.prop { background:#DCEAF4; color:navy; }
.func { background:#F4DCDF; color:#8b0000; }
.evnt { background:#CFF2C9; color:#286B1C; }
.func strong { text-shadow:1px 1px 0 white; }

.list-margin li { margin-bottom:10px; }
.list-margin strong { font-style:italic; }
pre code { color:#333; }
pre code strong { color:#000; }
.comment { color:#999; text-shadow:1px 1px 0 white; }
.comment strong { display:inline-block; width:15px; line-height:15px; font-size:10px; text-align:center; border-radius:8px; background:gray; padding:0; color:white; text-shadow:none; }

#main-buttons { text-align:center; padding-bottom:2em; }
#main-buttons > small { color:#666; }
#main-buttons > .btn { font-weight:bold; width:135px; text-shadow:1px 1px 0 #666; margin-right:10px; margin-bottom:10px; }
.features { margin:0 auto 2em auto; max-width:85%; }
.features > li { width:45%; padding:8px 0; }
.features > li > .glyphicon { margin-right:10px; }

.list-group-item-heading { font-weight:bold; font-size:16px; }
.list-group-item-text { color:#666; }
pre .point { display:inline-block; border-radius:5px; width:100%; padding:5px 0; }

#jstree1, #jstree2, .demo { max-width:100%; overflow:auto; font:10px Verdana, sans-serif; box-shadow:0 0 5px #ccc; padding:10px; border-radius:5px; }

#plugins .demo, #plugins pre { min-height:200px; }

#no_res { text-align:center; border:0 !important; }
@media (max-width: 740px) {
	.param, .return, .trigger { float:none; }
	.params p { margin-left:10px; margin-top:10px; line-height:26px; }
	.features { max-width:100%; }
	.features > li { width:auto; margin:0px 30px 0 0; }
	.features > li > .glyphicon { margin-right:2px; }
}