<!DOCTYPE html>
<!--[if lt IE 7]>      <html class="no-js lt-ie9 lt-ie8 lt-ie7"> <![endif]-->
<!--[if IE 7]>         <html class="no-js lt-ie9 lt-ie8"> <![endif]-->
<!--[if IE 8]>         <html class="no-js lt-ie9"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js"> <!--<![endif]-->
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<title>jsTree</title>
	<meta name="viewport" content="width=device-width" />
	<!--[if lt IE 9]><script src="./assets/html5.js"></script><![endif]-->
	
	<meta name="robots" content="index,follow" />
	<link rel="stylesheet" href="./assets/bootstrap/css/bootstrap.min.css" />
	<link rel="stylesheet" href="./assets/dist/themes/default/style.min.css" />
	<link rel="stylesheet" href="./assets/docs.css" />
	<!--[if lt IE 9]><script src="./assets/respond.js"></script><![endif]-->
	
	<link rel="icon" href="./assets/favicon.ico" type="image/x-icon" />
	<link rel="apple-touch-icon-precomposed" href="./assets/apple-touch-icon-precomposed.png" />
	<script>window.$q=[];window.$=window.jQuery=function(a){window.$q.push(a);};</script>
</head>
<body>
	<header id="head">
		<div class="container">
			<div class="row">
				<div class="col-md-3 col-sm-12">
					<h1 id="logo"><a href="http://jstree.com">jsTree - jQuery tree plugin</a></h1>
				</div>
				<div class="col-md-6 col-sm-8">
					<nav>
						<ul class="nav nav-pills" id="menu"><li><a href="/docs"><i class="glyphicon glyphicon-home"></i> Home</a></li><li><a href="/demo"><i class="glyphicon glyphicon-flash"></i> Demo</a></li><li><a href="/api"><i class="glyphicon glyphicon-cog"></i> API</a></li><li><a href="/plugins"><i class="glyphicon glyphicon-link"></i> Plugins</a></li></ul>
					</nav>
				</div>
				<div class="col-md-3 col-sm-4">
					<form role="search">
						<input type="text" id="srch" class="form-control" placeholder="API search" />
					</form>
				</div>
			</div>
		</div>
	</header>
	<div class="container" id="content">
		<!--.-->
		<div class="row page" id="docs">
			<div class="col-md-12" style="">
				<h3><i class="glyphicon glyphicon-leaf"></i> What is jsTree?</h3>
				<div class="row">
					<div class="col-md-8">
						<p>jsTree is <strong>jquery plugin</strong>, that provides <strong>interactive trees</strong>. It is absolutely <strong>free</strong>, <a href="https://github.com/vakata/jstree/">open source</a> and distributed under the <a href="http://www.opensource.org/licenses/mit-license.php">MIT license</a>. jsTree is easily extendable, themable and configurable, it supports <strong>HTML &amp; JSON data sources and AJAX loading</strong>.</p>
						<p>jsTree functions properly in either box-model (content-box or border-box), can be loaded as an AMD module, and has a built in mobile theme for responsive design, that can easily be customized. It uses jQuery's event system, so binding callbacks on various events in the tree is familiar and easy.</p>
						<p>Just a few of the features worth noting:</p>
						<ul class="features list-unstyled list-inline">
							<li><i class="glyphicon glyphicon-ok"></i> drag &amp; drop support</li>
							<li><i class="glyphicon glyphicon-ok"></i> keyboard navigation</li>
							<li><i class="glyphicon glyphicon-ok"></i> inline edit, create and delete</li>
							<li><i class="glyphicon glyphicon-ok"></i> tri-state checkboxes</li>
							<li><i class="glyphicon glyphicon-ok"></i> fuzzy searching</li>
							<li><i class="glyphicon glyphicon-ok"></i> customizable node types</li>
						</ul>
						<p id="main-buttons">
							<img src="./assets/images/browsers.png" style="max-width:100%; max-height:36px;" alt="" /><br /><small>All modern browsers are supported, as well as IE8</small><br /><br />
							<a href="https://github.com/vakata/jstree/archive/master.zip" class="btn btn-success"><i class="glyphicon glyphicon-download"></i>&nbsp;&nbsp;Download</a>
							<a href="https://groups.google.com/forum/#!forum/jstree" class="btn btn-warning"><i class="glyphicon glyphicon-comment"></i>&nbsp;&nbsp;Discuss</a>
							<a href="https://github.com/vakata/jstree/issues?state=open" class="btn btn-danger"><i class="glyphicon glyphicon-exclamation-sign"></i>&nbsp;&nbsp;Report bugs</a>
							<a href="https://www.paypal.com/cgi-bin/webscr?cmd=_xclick&amp;business=<EMAIL>&amp;currency_code=USD&amp;amount=&amp;return=http://jstree.com/donation&amp;item_name=Buy+me+a+coffee+for+jsTree" class="btn btn-primary"><i class="glyphicon glyphicon-thumbs-up"></i>&nbsp;&nbsp;&nbsp;Donate</a>
						</p>
					</div>
					<div class="col-md-4">
						<div id="jstree1" class="demo">
							<ul>
								<li>Root node 1
									<ul>
										<li data-jstree='{ "selected" : true }'><a href="#"><em>initially</em> <strong>selected</strong></a></li>
										<li data-jstree='{ "icon" : "http://jstree.com/tree-icon.png" }'>custom icon URL</li>
										<li data-jstree='{ "opened" : true }'>initially open
											<ul>
												<li>Another node</li>
											</ul>
										</li>
										<li data-jstree='{ "icon" : "glyphicon glyphicon-leaf" }'>Custom icon class (bootstrap)</li>
									</ul>
								</li>
								<li><a href="http://www.jstree.com">Root node 2</a></li>
							</ul>
						</div>
						<div id="jstree2" class="demo" style="margin-top:2em;"></div>
						<script>
						$(function () {
							$('#jstree1').jstree();
							$('#jstree2').jstree({'plugins':["wholerow","checkbox"], 'core' : {
								'data' : [
									{
										"text" : "Same but with checkboxes",
										"children" : [
											{ "text" : "initially selected", "state" : { "selected" : true } },
											{ "text" : "custom icon URL", "icon" : "http://jstree.com/tree-icon.png" },
											{ "text" : "initially open", "state" : { "opened" : true }, "children" : [ "Another node" ] },
											{ "text" : "custom icon class", "icon" : "glyphicon glyphicon-leaf" }
										]
									},
									"And wholerow selection"
								]
							}});
						});
						</script>
					</div>
				</div>

				<ul class="nav nav-tabs">
					<li class="active"><a href="/docs/overview" class="nava">Overview</a></li>
					<li><a href="/docs/config" class="nava">Configuration</a></li>
					<li><a href="/docs/html" class="nava">HTML data</a></li>
					<li><a href="/docs/json" class="nava">JSON data</a></li>
					<li><a href="/docs/events" class="nava">Events</a></li>
					<li><a href="/docs/interaction" class="nava">Interaction</a></li>
				</ul>
				<div class="tab-content">
					<!--...-->
					<div id="overview" class="tab-content-item">
				<div class="row">
					<div class="col-md-12">
						<h3><i class="glyphicon glyphicon-leaf"></i> Getting Started - everything at a glance</h3>
					</div>
				</div>
				<div class="row" style="margin-top:1em;">
					<div class="col-md-12">
						<ol class="spaced">
							<li>
								<h4 class="list-group-item-heading"><a href="https://github.com/vakata/jstree/archive/master.zip">Download jsTree</a></h4>
								<p class="list-group-item-text">All the files you need are in the <code>dist/</code> folder of the download.</p>
							</li>
							<li>
								<h4 class="list-group-item-heading">Include a jsTree theme</h4>
								<p class="list-group-item-text">Themes can be autloaded too, but it is best for performance to include the CSS file.</p>
								<pre style="margin-top:1em;"><code>&lt;link rel="stylesheet" href="<strong>dist/themes/default/style.min.css</strong>" /&gt;</code></pre>
							</li>
							<li>
								<h4 class="list-group-item-heading">Setup a container</h4>
								<p class="list-group-item-text">This is the element where you want the tree to appear, a <code>&lt;div&gt;</code> is enough. This example has a nested <code>&lt;ul&gt;</code> as there is no other data source configured (such as JSON).</p>
								<pre style="margin-top:1em;"><code>  &lt;div <strong>id="jstree_demo_div"</strong>&gt;&lt;div&gt;</code></pre>
							</li>
							<li>
								<h4 class="list-group-item-heading">Include jQuery</h4>
								<p class="list-group-item-text">jsTree requires <strong>1.9.0 or greater</strong> in your webpage.</p>
								<pre style="margin-top:1em;"><code>&lt;script src="<strong>dist/libs/jquery.js</strong>"&gt;&lt;/script&gt;</code></pre>
							</li>
							<li>
								<h4 class="list-group-item-heading">Include jsTree</h4>
								<p class="list-group-item-text">For production include the minified version: <code>dist/jstree.min.js</code>, there is a development version too: <code>dist/jstree.js</code></p>
								<pre style="margin-top:1em;"><code>&lt;script src="<strong>dist/jstree.min.js</strong>"&gt;&lt;/script&gt;</code></pre>
							</li>
							<li>
								<h4 class="list-group-item-heading">Create an instance</h4>
								<p class="list-group-item-text">Once the DOM is ready you can start creating jstree instances.</p>
								<pre style="margin-top:1em;"><code>$(function () { <strong>$('#jstree_demo_div').jstree();</strong> }</code></pre>
							</li>
							<li>
								<h4 class="list-group-item-heading">Listen for events</h4>
								<p class="list-group-item-text">jsTree uses events to notify you when something changes while users (or you) interact with the tree. So binding to jstree events is as easy binding to a click. There is a <a href="/api/?q=.jstree%20Event">list of events</a> and what information they provide in the API documentation.</p>
								<pre style="margin-top:1em;"><code>$('#jstree_demo_div').on("changed.jstree", function (e, data) {
  console.log(data.selected);
});</code></pre>
							</li>
							<li>
								<h4 class="list-group-item-heading">Interact with your instances</h4>
								<p class="list-group-item-text">Once an instance is ready you can invoke methods on it. There is a <a href="/api/?q=(">list of available methods</a> in the API documentation. The three examples below do exactly the same thing</p>
								<pre style="margin-top: 1em;"><code>$('button').on('click', function () {
  <strong>$('#jstree').jstree(true).select_node('child_node_1');</strong>
  <strong>$('#jstree').jstree('select_node', 'child_node_1');</strong>
  <strong>$.jstree.reference('#jstree').select_node('child_node_1');</strong>
});</code></pre>
							</li>
						</ol>
					</div>
				</div>
				<div class="row">
					<div class="col-md-12">
						<p style="text-align:center;"><button class="btn btn-info" onclick="$(this).blur().parent().next().slideToggle(); return false;">show the complete code</button></p>
<pre style="padding:0px 20px; display:none;"><code>
&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
  &lt;meta charset="utf-8"&gt;
  &lt;title&gt;jsTree test&lt;/title&gt;
  <span class="comment">&lt;!-- <strong>2</strong> load the theme CSS file --&gt;</span>
  &lt;link rel="stylesheet" href="<strong>dist/themes/default/style.min.css</strong>" /&gt;
&lt;/head&gt;
&lt;body&gt;
  <span class="comment">&lt;!-- <strong>3</strong> setup a container element --&gt;</span>
  &lt;div id="jstree"&gt;
    <span class="comment">&lt;!-- in this example the tree is populated from inline HTML --&gt;</span>
    &lt;ul&gt;
      &lt;li&gt;Root node 1
        &lt;ul&gt;
          &lt;li id="child_node_1"&gt;Child node 1&lt;/li&gt;
          &lt;li&gt;Child node 2&lt;/li&gt;
        &lt;/ul&gt;
      &lt;/li&gt;
      &lt;li&gt;Root node 2&lt;/li&gt;
    &lt;/ul&gt;
  &lt;/div&gt;
  &lt;button&gt;demo button&lt;/button&gt;

  <span class="comment">&lt;!-- <strong>4</strong> include the <a href="http://jquery.org">jQuery</a> library --&gt;</span>
  &lt;script src="<strong>dist/libs/jquery.js</strong>"&gt;&lt;/script&gt;
  <span class="comment">&lt;!-- <strong>5</strong> include the minified <a href="http://jstree.com">jstree</a> source --&gt;</span>
  &lt;script src="<strong>dist/jstree.min.js</strong>"&gt;&lt;/script&gt;
  &lt;script&gt;
  $(function () {
    <span class="comment">// <strong>6</strong> create an instance when the DOM is ready</span>
    <strong>$('#jstree').jstree();</strong>
    <span class="comment">// <strong>7</strong> bind to events triggered on the tree</span>
    <strong>$('#jstree').on("changed.jstree", function (e, data) {
      console.log(data.selected);
    });</strong>
    <span class="comment">// <strong>8</strong> interact with the tree - either way is OK</span>
    $('button').on('click', function () {
      <strong>$('#jstree').jstree(true).select_node('child_node_1');</strong>
      <strong>$('#jstree').jstree('select_node', 'child_node_1');</strong>
      <strong>$.jstree.reference('#jstree').select_node('child_node_1');</strong>
    });
  });
  &lt;/script&gt;
&lt;/body&gt;
&lt;/html&gt;
 </code></pre>
					</div>
				</div>

				</div><!--...--><div id="config" class="tab-content-item">

				<div class="row">
					<div class="col-md-12">
						<h3><i class="glyphicon glyphicon-leaf"></i> Configuring instances</h3>
					</div>
				</div>
				<div class="row">
					<div class="col-md-12">
					<p>Creating an instance as described in the overview does not modify any of the defaults:</p>
					<pre><code>$('#jstree').jstree();</code></pre>
					<p>You can change the defaults for all future instances:</p>
					<pre><code>$.jstree.defaults.core.theme.variant = "large";
$('#jstree').jstree();</code></pre>
					<p>But most of the time you will want to change the defaults only for the instance that is being created. This is achieved by passing in a config object when creating the instance:</p>
					<pre><code>$('#jstree').jstree(<strong>{
  "plugins" : [ "wholerow", "checkbox" ]
}</strong>);</code></pre>
					<p>As seen in the previous example - there is one special key in the config object named <code>plugins</code>. It is an array of strings, which contain the names of the plugins you want active on that instance.</p>
					<p>All options that do not depend on a plugin are contained in a key of the config object named <code>core</code>, the options for each plugin are contained within a key with the same name as the plugin:</p>
					<pre><code>$('#jstree').jstree({
  <strong>"core"</strong> : {
    "theme" : {
      "variant" : "large"
    }
  },
  <strong>"checkbox"</strong> : {
    "keep_selected_style" : false
  },
  "plugins" : [ "wholerow", "checkbox" ]
});</code></pre>

					<p>You can have a look at all the <a href="/api/?q=$.jstree.defaults">options and their default values</a>. This list is what you can configure on each instance.<br />For example, by default the tree allows multiple selection as stated in <code>$.jstree.defaults.core.multiple</code>, to overwrite that make sure your config object contains <code>"core" : { "multiple" : false }</code>. If you have multiple overrides for the same key (like <code>"core"</code> here), group them:</p>
					<pre><code>$("#jstree").jstree({
  "core" : {
    "multiple" : false,
    "animation" : 0
  }
});</code></pre>
					</div>
				</div>

				</div><!--...--><div id="html" class="tab-content-item">

				<div class="row">
					<div class="col-md-12">
						<h3><i class="glyphicon glyphicon-leaf"></i> Populating a tree using HTML</h3>
					</div>
				</div>
				<h4>Basic markup</h4>
				<div class="row">
					<div class="col-md-4">
						<p>jsTree can turn a regular unordered list into a tree. The minimal required markup is a <code>&lt;ul&gt;</code> node with some nested <code>&lt;li&gt;</code> nodes with some text inside.</p>
						<p>You should have a container wrapping the <code>&lt;ul&gt;</code> and create the instance on that container. Like so:<br /><code>$('#html1').jstree();</code>.
					</div>
					<div class="col-md-4">
						<pre><code>&lt;div id="html1"&gt;
  &lt;ul&gt;
    &lt;li&gt;Root node 1&lt;/li&gt;
    &lt;li&gt;Root node 2&lt;/li&gt;
  &lt;/ul&gt;
&lt;/div&gt;</code></pre>
					</div>
					<div class="col-md-4">
						<div class="demo" id="using_html_1">
							<ul>
								<li>Root node 1</li>
								<li>Root node 2</li>
							</ul>
						</div>
					</div>
					<script>
					$(function () {
						$('#using_html_1').jstree();
					});
					</script>
				</div>
				<h4>Nodes with children</h4>
				<div class="row">
					<div class="col-md-4">
						<p>To create a node with child nodes simpy nest an <code>&lt;ul&gt;</code>.</p>
						<p>Internally jstree converts the text to a link, so if there already is a link in the markup jstree won't mind. Like <code>Child node 2</code>.<br />Clicking on the link however will not direct the user to a new page, to do that - intercept the <code>changed.jstree</code> event and act accordingly.</p>
						<p>Keep reading for the section on handling events.</p>
					</div>
					<div class="col-md-4">
						<pre><code>&lt;div id="html1"&gt;
  &lt;ul&gt;
    &lt;li&gt;Root node 1<strong>
      &lt;ul&gt;
        &lt;li&gt;Child node 1&lt;/li&gt;
        &lt;li&gt;&lt;a href="#"&gt;Child node 2&lt;/a&gt;&lt;/li&gt;
      &lt;/ul&gt;</strong>
    &lt;/li&gt;
  &lt;/ul&gt;
&lt;/div&gt;</code></pre>
					</div>
					<div class="col-md-4">
						<div id="using_html_2" class="demo">
							<ul>
								<li>Root node 1
									<ul>
										<li>Child node 1</li>
										<li><a href="#">Child node 2</a></li>
									</ul>
								</li>
							</ul>
						</div>
						<script>
						$(function () {
							$('#using_html_2').jstree();
						});
						</script>
					</div>
				</div>
				<h4>Setting initial state with classes</h4>
				<div class="row">
					<div class="col-md-4">
						<p>To make a node initially selected you can set the <code>jstree-clicked</code> class on the <code>&lt;a&gt;</code> element.</p>
						<p>Similarly you can set the <code>jstree-open</code> class on any <code>&lt;li&gt;</code> element to make it initially extended, so that its children are visible.</p>
						<p>It is a good idea to give your nodes <strong>unique</strong> IDs by adding the <code>id</code> attribute to any <code>&lt;li&gt;</code> element. This will be useful if you need to sync with a backend as you will get the ID back in any events jstree triggers.</p>
					</div>
					<div class="col-md-4">
						<pre><code>&hellip;
&lt;li <strong>class="jstree-open"</strong> <strong>id="node_1"</strong>&gt;Root&lt;/li&gt;
  &lt;ul&gt;
    &lt;li&gt;
      &lt;a href="#" <strong>class="jstree-clicked"</strong>&gt;Child&lt;/a&gt;
    &lt;/li&gt;
  &lt;/ul&gt;
&lt;/li&gt;
&hellip;</code></pre>
					</div>
					<div class="col-md-4">
						<div id="using_html_3" class="demo">
							<ul>
								<li class="jstree-open">Root
									<ul>
										<li><a href="#" class="jstree-clicked">Child</a></li>
									</ul>
								</li>
							</ul>
						</div>
						<script>
						$(function () {
							$('#using_html_3').jstree();
						});
						</script>
					</div>
				</div>
				<h4>Setting initial state with data attribute</h4>
				<div class="row">
					<div class="col-md-4">
						<p>You can also set the state on a node using a <code>data-jstree</code> attribute.</p>
						<p>You can use any combination of the following: <code>opened</code>, <code>selected</code>, <code>disabled</code>, <code>icon</code>.</p>
						<p>Specifying an address (anything containing a <code>/</code>) will display that image as the node icon. Using a string will apply that class to the <code>&lt;i&gt;</code> element that is used to represent the icon.<br />For example if you are using Twitter Bootstrap you can use <code>"icon" : "glyphicon glyphicon-leaf"</code> to display a leaf icon.</p>
					</div>
					<div class="col-md-4">
						<pre><code>&lt;li <strong>data-jstree='{"opened":true,"selected":true}'</strong>&gt;Root
  &lt;ul&gt;
    &lt;li <strong>data-jstree='{"disabled":true}'</strong>&gt;Child&lt;/li&gt;
    &lt;li <strong>data-jstree='{"icon":"http://jstree.com/tree.png"}'</strong>&gt;
      Child&lt;/li&gt;
    &lt;li <strong>data-jstree='{"icon":"glyphicon glyphicon-leaf"}'</strong>&gt;
      Child&lt;/li&gt;
  &lt;/ul&gt;
&lt;/li&gt;</code></pre>
					</div>
					<div class="col-md-4">
						<div id="using_html_4" class="demo">
							<ul>
								<li data-jstree='{"opened":true,"selected":true}'>Root
								  <ul>
								    <li data-jstree='{"disabled":true}'>Child</li>
								    <li data-jstree='{"icon":"http://jstree.com/tree.png"}'>
								      Child</li>
								    <li data-jstree='{"icon":"glyphicon glyphicon-leaf"}'>
								      Child</li>
								  </ul>
								</li>
							</ul>
						</div>
						<script>
						$(function () {
							$('#using_html_4').jstree();
						});
						</script>
					</div>
				</div>
				<h4>Loading with AJAX</h4>
				<div class="row">
					<div class="col-md-4">
						<p>You can also use AJAX to populate the tree with HTML your server returns. The format remains the same as the above, the only difference is that the HTML is not inside the container, but returned from the server.</p>
						<p>To take advantage of this option you need to use the <a href="/api/?f=$.jstree.defaults.core.data"><code>$.jstree.defaults.core.data</code></a> config option.</p>
						<p>Just use a standard jQuery-like AJAX config and jstree will automatically make an AJAX request populate the tree with the response.</p><p>Add a class of <code>jstree-closed</code> to any LI node you return and do not nest an UL node and jstree will make another AJAX call as soon as the user opens this node.</p>
						<p>In addition to the standard jQuery ajax options here you can supply functions for <code>data</code> and <code>url</code>, the functions will be run in the current instance's scope and a param will be passed indicating which node is being loaded, the return value of those functions will be used as URL and data respectively.</p>
					</div>
					<div class="col-md-4">
						<pre><code>$('#tree').jstree({
'core' : {
  'data' : {
    'url' : 'ajax_nodes.html',
    'data' : function (node) {
      'url' : 'ajax_nodes.html',
      'data' : function (node) {
        return { 'id' : node.id };
      }
      return { 'id' : node.id };
    }
  }
});

// Example response:
&lt;ul&gt;
&lt;li&gt;Node 1&lt;/li&gt;
&lt;li class="jstree-closed"&gt;Node 2&lt;/li&gt;
&lt;/ul&gt;</code></pre>
					</div>
					<div class="col-md-4">
						<div id="using_html_5" class="demo"></div>
						<script>
						$(function () {
							$('#using_html_5').jstree({
								'core' : {
									'data' : {
										'url' : './assets/ajax_nodes.html',
										'data' : function (node) {
											return { 'id' : node.id };
										}
									}
								}
							});
						});
						</script>
					</div>
				</div>

				</div><!--...--><div id="json" class="tab-content-item">

				<div class="row">
					<div class="col-md-12">
						<h3><i class="glyphicon glyphicon-leaf"></i> Populating the tree using JSON</h3>
					</div>
				</div>
				<h4>The format</h4>
				<div class="row">
					<div class="col-md-6">
						<p>jsTree needs a specific format to work with JSON. In the standard syntax no fields are required - pass only what you need. Keep in mind you will be able to access any additional properties you specify - jsTree won't touch them and you will be able to use them later on.</p>
						<p>To change the icon of the node use the <code>icon</code> property. Specifying a string containing a <code>/</code> will display that image as the node icon. Using any other string will apply that class to the <code>&lt;i&gt;</code> element that is used to represent the icon. You can use boolean <code>false</code> to make jsTree render the node with no icon.</p>
						<p>You can set the state on a node using the <code>state</code> property. Use any combination of the following: <code>opened</code>, <code>selected</code>, <code>disabled</code>.</p>
						<p>Both <code>li_attr</code> and <code>a_attr</code> are passed directly to jQuery's attr function.</p>
						<p>When using AJAX set <code>children</code> to boolean <code>true</code> and jsTree will render the node as closed and make an additional request for that node when the user opens it.</p>
						<p>Any nested children should either be objects following the same format, or plain strings (in which case the string is used for the node's text and everything else is autogenerated).</p>
					</div>
					<div class="col-md-6">
						<pre><code><span class="comment">// Expected format of the node (there are no required fields)</span>
{
  id          : "string" <span class="comment">// will be autogenerated if omitted</span>
  text        : "string" <span class="comment">// node text</span>
  icon        : "string" <span class="comment">// string for custom</span>
  state       : {
    opened    : boolean  <span class="comment">// is the node open</span>
    disabled  : boolean  <span class="comment">// is the node disabled</span>
    selected  : boolean  <span class="comment">// is the node selected</span>
  },
  children    : []  <span class="comment">// array of strings or objects</span>
  li_attr     : {}  <span class="comment">// attributes for the generated LI node</span>
  a_attr      : {}  <span class="comment">// attributes for the generated A node</span>
}</code></pre>
					</div>
				</div>
				<h4>Alternative JSON format</h4>
				<div class="row">
					<div class="col-md-6">
						<p>If you do not want to use the nested <code>children</code> approach, you can use the alternative syntax where each node object has two required fields: <code>id</code> &amp; <code>parent</code> and no <code>children</code> property (everything else remains the same).</p>
						<p>jsTree will automatically build the hierarchy. To indicate a node should be a root node set its <code>parent</code> property to <code>"#"</code>.</p>
						<p>This should be used mainly when you render the whole tree at once and is useful when data is stored in a database using adjacency.</p>
					</div>
					<div class="col-md-6">
						<pre><code><span class="comment">// Alternative format of the node (id &amp; parent are required)</span>
{
  <strong>id          : "string"</strong> <span class="comment">// required</span>
  <strong>parent      : "string"</strong> <span class="comment">// required</span>
  text        : "string" <span class="comment">// node text</span>
  icon        : "string" <span class="comment">// string for custom</span>
  state       : {
    opened    : boolean  <span class="comment">// is the node open</span>
    disabled  : boolean  <span class="comment">// is the node disabled</span>
    selected  : boolean  <span class="comment">// is the node selected</span>
  },
  li_attr     : {}  <span class="comment">// attributes for the generated LI node</span>
  a_attr      : {}  <span class="comment">// attributes for the generated A node</span>
}</code></pre>
					</div>
				</div>
				<h4>Using JSON</h4>
				<div class="row">
					<div class="col-md-4">
						<p>To populate the tree with a JSON object you need to use the <a href="/api/?f=$.jstree.defaults.core.data"><code>$.jstree.defaults.core.data</code></a> config option.</p>
						<p>The expected format is an array of nodes, where each node should be an object as described above or a simple string (in which case the string is used for the node's text property ane everything else is autogenerated). Any nested nodes are supplied in the same manner in the <code>children</code> property of their parent.</p>
					</div>
					<div class="col-md-4">
						<pre><code>$('#using_json').jstree({ 'core' : {
    'data' : [
       'Simple root node',
       {
         'text' : 'Root node 2',
         'state' : {
           'opened' : true,
           'selected' : true
         },
         'children' : [
           { 'text' : 'Child 1' },
           'Child 2'
         ]
      }
    ]
} });</code></pre>
					</div>
					<div class="col-md-4">
						<div id="using_json" class="demo">
						</div>
						<script>
						$(function () {
							$('#using_json').jstree({ 'core' : {
							    'data' : [
							       'Simple root node',
							       {
							         'text' : 'Root node 2',
							         'state' : {
							           'opened' : true, 'selected' : true
							         },
							         'children' : [
							           { 'text' : 'Child 1' },
							           'Child 2'
							         ]
							      }
							    ]
							} });
						});</script>
					</div>
				</div>
				<h4>Using the alternative JSON format</h4>
				<div class="row">
					<div class="col-md-8">
						<pre><code>$('#using_json_2').jstree({ 'core' : {
    'data' : [
       { "id" : "ajson1", "parent" : "#", "text" : "Simple root node" },
       { "id" : "ajson2", "parent" : "#", "text" : "Root node 2" },
       { "id" : "ajson3", "parent" : "ajson2", "text" : "Child 1" },
       { "id" : "ajson4", "parent" : "ajson2", "text" : "Child 2" },
    ]
} });</code></pre>
					</div>
					<div class="col-md-4">
						<div id="using_json_2" class="demo">
						</div>
						<script>
						$(function () {
							$('#using_json_2').jstree({ 'core' : {
							    'data' : [
							       { "id" : "ajson1", "parent" : "#", "text" : "Simple root node" },
							       { "id" : "ajson2", "parent" : "#", "text" : "Root node 2", "state" : { "opened" : true } },
							       { "id" : "ajson3", "parent" : "ajson2", "text" : "Child 1" },
							       { "id" : "ajson4", "parent" : "ajson2", "text" : "Child 2" },
							    ]
							} });
						});</script>
					</div>
				</div>
				<h4>Using AJAX</h4>
				<div class="row">
					<div class="col-md-4">
						<p>You can also use AJAX to populate the tree with JSON your server returns. The format remains the same as the above, the only difference is that the JSON is not inside the config object, but returned from the server.</p>
						<p>To take advantage of this option you need to use the <a href="/api/?f=$.jstree.defaults.core.data"><code>$.jstree.defaults.core.data</code></a> config option.</p>
						<p>Just use a standard jQuery-like AJAX config and jstree will automatically make an AJAX request populate the tree with the response.</p>
						<p>In addition to the standard jQuery ajax options here you can supply functions for <code>data</code> and <code>url</code>, the functions will be run in the current instance's scope and a param will be passed indicating which node is being loaded, the return value of those functions will be used as URL and data respectively.</p>
						<p>If you do not return correct json headers from the server, at least set the <code>dataType</code> jQuery AJAX option to <code>"json"</code>.</p>
					</div>
					<div class="col-md-4">
						<pre><code>
$('#tree').jstree({
'core' : {
  'data' : {
    'url' : function (node) {
      return node.id === '#' ? 
        'ajax_roots.json' : 
        'ajax_children.json';
    },
    'data' : function (node) {
      return { 'id' : node.id };
    }
  }
});
						</code></pre>
					</div>
					<div class="col-md-4">
						<div id="using_json_3" class="demo"></div>
						<script>
						$(function () {
							$('#using_json_3').jstree({
								'core' : {
									'data' : {
									    'url' : function (node) {
									      return node.id === '#' ? './assets/ajax_roots.json' : './assets/ajax_children.json';
									    },
									    'data' : function (node) {
									      return { 'id' : node.id };
									    }
									}
								}
							});
						});
						</script>
					</div>
				</div>
				<h4>Using a function</h4>
				<div class="row">
					<div class="col-md-4">
						<p>You can supply a function too. That function will receive two arguments - the node being loaded and a callback function to call with the children for that node once you are ready.</p>
					</div>
					<div class="col-md-4">
						<pre><code>
$('#tree').jstree({
    'core' : {
        'data' : function (obj, cb) {
            cb.call(this,
              ['Root 1', 'Root 2']);
        }
    }});
						</code></pre>
					</div>
					<div class="col-md-4">
						<div id="using_json_4" class="demo"></div>
						<script>
						$(function () {
							$('#using_json_4').jstree({
							    'core' : {
							        'data' : function (obj, callback) {
							            callback.call(this, ['Root 1', 'Root 2']);
							        }
							    }});
						});
						</script>
					</div>
				</div>

				</div><!--...--><div id="events" class="tab-content-item">

				<div class="row">
					<div class="col-md-12">
						<h3><i class="glyphicon glyphicon-leaf"></i> Listening for events</h3>
					</div>
				</div>
				<div class="row">
					<div class="col-md-12">
						<p>jsTree triggers various events on the container. You can review <a href="/api/?q=.jstree%20Event">the list of all events</a> to know what to listen for.</p>
						<p>To get more information about the event inspect its <code>data</code> argument.</p>
						<p>In most cases where a node is involved you will get the whole node object passed in. If you get an ID string somewhere and want to inspect the node just use <a href="/api/?f=get_node">.get_node()</a>.</p>
					</div>
				</div>
				<div class="row">
					<div class="col-md-8">
						<pre><code>
$('#jstree')
  <span class="comment">// listen for event</span>
  <strong>.on('changed.jstree',</strong> function (e, <strong>data</strong>) {
    var i, j, r = [];
    for(i = 0, j = data.selected.length; i &lt; j; i++) {
      r.push(data.instance.get_node(data.selected[i]).text);
    }
    $('#event_result').html('Selected: ' + r.join(', '));
  })
  <span class="comment">// create the instance</span>
  .jstree();
						</code></pre>
					</div>
					<div class="col-md-4">
						<div id="using_events" class="demo">
							<ul>
								<li id="r1" class="jstree-open" data-jstree='{"selected":true}'>Root 1
									<ul>
										<li id="n1">Child 1</li>
										<li id="n2">Child 2</li>
									</ul>
								</li>
								<li id="r2" class="jstree-open">Root 2
									<ul>
										<li id="n3">Child 3</li>
										<li id="n4">Child 4</li>
									</ul>
								</li>
							</ul>
						</div>
						<div id="event_result" style="margin-top:2em; text-align:center;">&nbsp;</div>
						<script>
						$(function () {
							$('#using_events')
								.on('changed.jstree', function (e, data) {
									var i, j, r = [];
									for(i = 0, j = data.selected.length; i < j; i++) {
										r.push(data.instance.get_node(data.selected[i]).text);
									}
									$('#event_result').html('Selected:<br /> ' + r.join(', '));
								})
								.jstree();
						});
						</script>
					</div>
				</div>

				</div><!--...--><div id="interaction" class="tab-content-item">

				<div class="row">
					<div class="col-md-12">
						<h3><i class="glyphicon glyphicon-leaf"></i> Invoking methods on an instance</h3>
					</div>
				</div>
				<div class="row">
					<div class="col-md-12">
						<div class="alert alert-warning">Please keep in mind that by default all modifications to the tree are prevented (create, rename, move, delete). To enable them set <a href="/api/?q=check_callback&amp;f=$.jstree.defaults.core.check_callback">core.check_callback</a> to <code>true</code></div>
					</div>
				</div>
				<div class="row">
					<div class="col-md-4">
						<p>To invoke a method on an instance you must obtain a reference of the instance and invoke the method. The example shows how to obtain a reference and invoke a method.</p>
						<p>Check the API for <a href="/api/?q=(">a list of available methods</a>.
					</div>
					<div class="col-md-4">
						<pre><code>
<span class="comment">// 3 ways of doing the same thing</span>
$('#jstree').jstree(true)
  .select_node('mn1');
$('#jstree')
  .jstree('select_node', 'mn2');
$.jstree.reference('#jstree')
  .select_node('mn3');
						</code></pre>
					</div>
					<div class="col-md-4">
						<div style="text-align:center; margin-bottom:1em;">
							<button class="btn btn-info" onclick="$('#using_methods').jstree(true).select_node('mn1');">select 1</button> 
							<button class="btn btn-info" onclick="$('#using_methods').jstree('select_node', 'mn2');">select 2</button> 
							<button class="btn btn-info" onclick="$.jstree.reference('#using_methods').select_node('mn3');">select 3</button> 
						</div>
						<div id="using_methods" class="demo">
							<ul>
								<li id="mn1">Node 1</li>
								<li id="mn2">Node 2</li>
								<li id="mn3">Node 3</li>
							</ul>
						</div>
						<script>
						$(function () {
							$('#using_methods').jstree();
						});
						</script>
					</div>
				</div>

				</div><!--...--></div>

			</div>
		</div>
		<!--..-->
		<div class="row page" id="demo">
			<div class="col-md-12">
				<div class="row">
					<div class="col-md-4 col-sm-8 col-xs-8">
						<button type="button" class="btn btn-success btn-sm" onclick="demo_create();"><i class="glyphicon glyphicon-asterisk"></i> Create</button>
						<button type="button" class="btn btn-warning btn-sm" onclick="demo_rename();"><i class="glyphicon glyphicon-pencil"></i> Rename</button>
						<button type="button" class="btn btn-danger btn-sm" onclick="demo_delete();"><i class="glyphicon glyphicon-remove"></i> Delete</button>
					</div>
					<div class="col-md-2 col-sm-4 col-xs-4" style="text-align:right;">
						<input type="text" value="" style="box-shadow:inset 0 0 4px #eee; width:120px; margin:0; padding:6px 12px; border-radius:4px; border:1px solid silver; font-size:1.1em;" id="demo_q" placeholder="Search" />
					</div>
				</div>
				<div class="row">
					<div class="col-md-6">
						<div id="jstree_demo" class="demo" style="margin-top:1em; min-height:400px;"></div>
						<script>
						function demo_create() {
							var ref = $('#jstree_demo').jstree(true),
								sel = ref.get_selected();
							if(!sel.length) { return false; }
							sel = sel[0];
							sel = ref.create_node(sel, {"type":"file"});
							if(sel) {
								ref.edit(sel);
							}
						};
						function demo_rename() {
							var ref = $('#jstree_demo').jstree(true),
								sel = ref.get_selected();
							if(!sel.length) { return false; }
							sel = sel[0];
							ref.edit(sel);
						};
						function demo_delete() {
							var ref = $('#jstree_demo').jstree(true),
								sel = ref.get_selected();
							if(!sel.length) { return false; }
							ref.delete_node(sel);
						};
						$(function () {
							var to = false;
							$('#demo_q').keyup(function () {
								if(to) { clearTimeout(to); }
								to = setTimeout(function () {
									var v = $('#demo_q').val();
									$('#jstree_demo').jstree(true).search(v);
								}, 250);
							});

							$('#jstree_demo')
								.jstree({
									"core" : {
										"animation" : 0,
										"check_callback" : true,
										"themes" : { "stripes" : true },
										'data' : {
											'url' : function (node) {
												return node.id === '#' ? './assets/ajax_demo_roots.json' : './assets/ajax_demo_children.json';
											},
											'data' : function (node) {
												return { 'id' : node.id };
											}
										}
									},
									"types" : {
										"#" : { "max_children" : 1, "max_depth" : 4, "valid_children" : ["root"] },
										"root" : { "icon" : "./assets/images/tree_icon.png", "valid_children" : ["default"] },
										"default" : { "valid_children" : ["default","file"] },
										"file" : { "icon" : "glyphicon glyphicon-file", "valid_children" : [] }
									},
									"plugins" : [ "contextmenu", "dnd", "search", "state", "types", "wholerow" ]
								});
						});
						</script>
					</div>
					<div class="col-md-6">
						<pre style="margin-top:1em;"><code>$('#jstree_demo').jstree({
  "core" : {
    "animation" : 0,
    "check_callback" : true,
    "themes" : { "stripes" : true },
    'data' : {
      'url' : function (node) {
        return node.id === '#' ?
          'ajax_demo_roots.json' : 'ajax_demo_children.json';
      },
      'data' : function (node) {
        return { 'id' : node.id };
      }
    }
  },
  "types" : {
    "#" : {
      "max_children" : 1, 
      "max_depth" : 4, 
      "valid_children" : ["root"]
    },
    "root" : {
      "icon" : "./assets/images/tree_icon.png",
      "valid_children" : ["default"]
    },
    "default" : {
      "valid_children" : ["default","file"]
    },
    "file" : {
      "icon" : "glyphicon glyphicon-file",
      "valid_children" : []
    }
  },
  "plugins" : [
    "contextmenu", "dnd", "search",
    "state", "types", "wholerow"
  ]
});</code></pre>
					</div>
				</div>
				<div class="row" style="display:none;">
					<div class="col-md-12">
						<p style="text-align:center; margin-top:2em;"><a href="http://jsfiddle.net/vakata/3khw3/">This demo is available as a jsFiddle &raquo;</a></p>
					</div>
				</div>
			</div>
		</div>
		<!--..-->
		<div class="row page" id="api">
			<div class="col-md-12">
				<div id="api_inner" style="">
					<div id="no_res" style="text-align:center;"><h4 style="font-size:1.1em; font-weight:bold;">Sorry, no results found.</h4><button class="btn btn-link" onclick="$('#srch').val('').keyup();">clear this search</button></div>
					<div id="cl_src" style="text-align:center;"><h4 style="font-size:1.1em; font-weight:bold;">This is a filtered view.</h4><button class="btn btn-link" onclick="$('#srch').val('').keyup();">clear this search</button></div>
				</div>
			</div>
		</div>
		<!--..-->
		<div class="row page" id="plugins">
			<div class="col-md-12">
				<div class="row">
					<div class="col-md-12">
						<h4>Plugins?</h4>
						<p>jsTree has some functionality moved out of the core so you can only use it when you need it. To enable a plugin use the <code>plugins</code> <a href="/api/?f=$.jstree.defaults.plugins">config option</a> and add that plugin's name to the array.</p>
						<p>For example enabling all the plugins can be done this way:<br/><code>"plugins" : [ "checkbox", "contextmenu", "dnd", "search", "sort", "state", "types", "unique", "wholerow" ]</code></p>
						<p>Here is a quick overview for each one.</p>
					</div>
				</div>

				<h3><i class="glyphicon glyphicon-leaf"></i>&nbsp;Checkbox plugin</h3>
				<div class="row" style="margin-top:3em;">
					<div class="col-md-4">
						<p>This plugin renders checkbox icons in front of each node, making multiple selection much easier. <br>It also supports tri-state behavior, meaning that if a node has a few of its children checked it will be rendered as undetermined, and state will be propagated up.</p>
						<p><em>Undetermined state is automatically calculated, but if you are using AJAX and loading on demand and want to render a node as underemined pass <code>"undetermined" : true</code> in its state.</em></p>
						<p>You can find all the <a href="/api/?q=$.jstree.defaults.checkbox">checkbox plugin config options</a> in the API.</p>
					</div>
					<div class="col-md-4">
<pre><code>$(function () {
  $("#plugins1").jstree({
    "checkbox" : {
      "keep_selected_style" : false
    },
    "plugins" : [ "checkbox" ]
  });
});</code></pre>
					</div>
					<div class="col-md-4">
						<div id="plugins1" class="demo plugin-demo">
							<ul>
								<li data-jstree='{"opened":true}'>Root node
									<ul>
										<li>Child node 1</li>
										<li>Child node 2</li>
									</ul>
								</li>
							</ul>
						</div>
						<script>
						$(function () {
							$("#plugins1").jstree({
								"checkbox" : {
									"keep_selected_style" : false
								},
								"plugins" : [ "checkbox" ]
							});
						});
						</script>
					</div>
				</div>

				<h3><i class="glyphicon glyphicon-leaf"></i>&nbsp;Contextmenu plugin</h3>
				<div class="row">
					<div class="col-md-4">
						<p>This plugin makes it possible to right click nodes and shows a list of configurable actions in a menu.</p>
						<p>You can find all the <a href="/api/?q=$.jstree.defaults.contextmenu">contextmenu plugin config options</a> in the API.</p>
					</div>
					<div class="col-md-4">
<pre><code>$(function () {
  $("#plugins2").jstree({
     "core" : {
       // so that create works
       "check_callback" : true
     },
    "plugins" : [ "contextmenu" ]
  });
});</code></pre>
					</div>
					<div class="col-md-4">
						<div id="plugins2" class="demo plugin-demo">
							<ul>
								<li data-jstree='{"opened":true}'>Root node
									<ul>
										<li>Context click me</li>
									</ul>
								</li>
							</ul>
						</div>
						<script>
						$(function () {
							$("#plugins2")
								.jstree({
									"core" : {
										"check_callback" : true
									},
									"plugins" : [ "contextmenu" ]
								});
						});
						</script>
					</div>
				</div>

				<h3><i class="glyphicon glyphicon-leaf"></i>&nbsp;Drag &amp; drop plugin</h3>
				<div class="row">
					<div class="col-md-4">
						<p>This plugin makes it possible to drag and drop tree nodes and rearrange the tree.</p>
						<p>You can find all the <a href="/api/?q=$.jstree.defaults.dnd">dnd plugin config options</a> in the API.</p>
					</div>
					<div class="col-md-4">
<pre><code>$(function () {
  $("#plugins3").jstree({
    "core" : {
      "check_callback" : true
    },
    "plugins" : [ "dnd" ]
  });
});</code></pre>
					</div>
					<div class="col-md-4">
						<div id="plugins3" class="demo plugin-demo">
							<ul>
								<li data-jstree='{"opened":true}'>Root node
									<ul>
										<li>Child node 1</li>
										<li>Child node 2</li>
									</ul>
								</li>
								<li>Root node 2</li>
							</ul>
						</div>
						<script>
						$(function () {
							$("#plugins3").jstree({
								"core" : {
									"check_callback" : true
								},
								"plugins" : [ "dnd" ]
							});
						});
						</script>
					</div>
				</div>

				<h3><i class="glyphicon glyphicon-leaf"></i>&nbsp;Search plugin</h3>
				<div class="row">
					<div class="col-md-4">
						<p>This plugin adds the possibility to <a href="/api/?f=search(">search for items</a> in the tree and even to show only matching nodes.</p>
						<p>You can find all the <a href="/api/?q=$.jstree.defaults.search">search plugin config options</a> in the API.</p>
					</div>
					<div class="col-md-4">
<pre><code>$(function () {
  $("#plugins4").jstree({
    "plugins" : [ "search" ]
  });
  var to = false;
  $('#plugins4_q').keyup(function () {
    if(to) { clearTimeout(to); }
    to = setTimeout(function () {
      var v = $('#plugins4_q').val();
      $('#plugins4').jstree(true).search(v);
    }, 250);
  });
});</code></pre>
					</div>
					<div class="col-md-4">
						<input type="text" id="plugins4_q" value="" class="input" style="margin:0em auto 1em auto; display:block; padding:4px; border-radius:4px; border:1px solid silver;" />
						<div id="plugins4" class="demo plugin-demo">
							<ul>
								<li data-jstree='{"opened":true}'>Root node
									<ul>
										<li>Child node 1</li>
										<li>Child node 2</li>
									</ul>
								</li>
								<li>Root node 2</li>
							</ul>
						</div>
						<script>
						$(function () {
							$("#plugins4").jstree({
								"plugins" : [ "search" ]
							});
							var to = false;
							$('#plugins4_q').keyup(function () {
								if(to) { clearTimeout(to); }
								to = setTimeout(function () {
									var v = $('#plugins4_q').val();
									$('#plugins4').jstree(true).search(v);
								}, 250);
							});
						});
						</script>
					</div>
				</div>

				<h3><i class="glyphicon glyphicon-leaf"></i>&nbsp;Sort plugin</h3>
				<div class="row">
					<div class="col-md-4">
						<p>This plugin automatically arranges all sibling nodes according to a <a href="/api/?f=$.jstree.defaults.sort">comparison config option function</a>, which defaults to alphabetical order.</p>
					</div>
					<div class="col-md-4">
<pre><code>$(function () {
  $("#plugins5").jstree({
    "plugins" : [ "sort" ]
  });
});</code></pre>
					</div>
					<div class="col-md-4">
						<div id="plugins5" class="demo plugin-demo">
							<ul>
								<li data-jstree='{"opened":true}'>Root node
									<ul>
										<li>2</li>
										<li>1</li>
										<li>3</li>
										<li>0</li>
									</ul>
								</li>
								<li>Root node 2</li>
							</ul>
						</div>
						<script>
						$(function () {
							$("#plugins5").jstree({
								"plugins" : [ "sort" ]
							});
						});
						</script>
					</div>
				</div>

				<h3><i class="glyphicon glyphicon-leaf"></i>&nbsp;State plugin</h3>
				<div class="row">
					<div class="col-md-4">
						<p>This plugin saves all opened and selected nodes in the user's browser, so when returning to the same tree the previous state will be restored.</p>
						<p>You can find all the <a href="/api/?q=$.jstree.defaults.state">state plugin config options</a> in the API. Make a selection and refresh this page to see the change persisted.</p>
					</div>
					<div class="col-md-4">
<pre><code>$(function () {
  $("#plugins6").jstree({
    "state" : { "key" : "demo2" },
    "plugins" : [ "state" ]
  });
});</code></pre>
					</div>
					<div class="col-md-4">
						<div id="plugins6" class="demo plugin-demo">
							<ul>
								<li data-jstree='{"opened":true}'>Root node
									<ul>
										<li>A</li>
										<li>few</li>
										<li>more</li>
										<li>nodes</li>
									</ul>
								</li>
								<li>Root node 2</li>
							</ul>
						</div>
						<script>
						$(function () {
							$("#plugins6").jstree({
								"state" : { "key" : "demo2" },
								"plugins" : [ "state" ]
							});
						});
						</script>
					</div>
				</div>

				<h3><i class="glyphicon glyphicon-leaf"></i>&nbsp;Types plugin</h3>
				<div class="row">
					<div class="col-md-4">
						<p>This plugin makes it possible to add predefined types for groups of nodes, which means to easily control nesting rules and icon for each group.</p>
						<p>To set a node's type you can use <code>set_type</code> or supply a <code>type</code> property with the node's data.</p>
						<p>You can find all the <a href="/api/?f=$.jstree.defaults.types">types plugin config options &amp; functions</a> in the API.</p>
					</div>
					<div class="col-md-4">
<pre><code>$(function () {
  $("#plugins7").jstree({
    "types" : {
      "default" : {
        "icon" : "glyphicon glyphicon-flash"
      },
      "demo" : {
        "icon" : "glyphicon glyphicon-ok"
      }
    },
    "plugins" : [ "types" ]
  });
});</code></pre>
					</div>
					<div class="col-md-4">
						<div id="plugins7" class="demo plugin-demo">
							<ul>
								<li data-jstree='{"opened":true, "type":"demo"}'>Root node
									<ul>
										<li>Node</li>
									</ul>
								</li>
								<li>Root node 2</li>
							</ul>
						</div>
						<script>
						$(function () {
							$("#plugins7").jstree({
								"types" : {
									"default" : {
										"icon" : "glyphicon glyphicon-flash"
									},
									"demo" : {
										"icon" : "glyphicon glyphicon-ok"
									}
								},
								"plugins" : [ "types" ]
							});
						});
						</script>
					</div>
				</div>
				
				<h3><i class="glyphicon glyphicon-leaf"></i>&nbsp;Unique plugin</h3>
				<div class="row">
					<div class="col-md-4">
						<p>Enforces that no nodes with the same name can coexist as siblings. This plugin has no options, it just prevents renaming and moving nodes to a parent, which already contains a node with the same name.</p>
					</div>
					<div class="col-md-4">
<pre><code>$(function () {
  $("#plugins8").jstree({
    "core" : {
      "check_callback" : true
    },
    "plugins" : [ "unique", "dnd" ]
  });
});</code></pre>
					</div>
					<div class="col-md-4">
						<div id="plugins8" class="demo plugin-demo">
							<ul>
								<li data-jstree='{"opened":true}'>Root node
									<ul>
										<li>Node</li>
									</ul>
								</li>
								<li>Root node 2
									<ul>
										<li>Node</li>
									</ul>
								</li>
							</ul>
						</div>
						<script>
						$(function () {
							$("#plugins8").jstree({
								"core" : {
									"check_callback" : true
								},
								"plugins" : [ "unique", "dnd" ]
							});
						});
						</script>
					</div>
				</div>
				
				<h3><i class="glyphicon glyphicon-leaf"></i>&nbsp;Wholerow plugin</h3>
				<div class="row">
					<div class="col-md-4">
						<p>Makes each node appear block level which makes selection easier. May cause slow down for large trees in old browsers.</p>
					</div>
					<div class="col-md-4">
<pre><code>$(function () {
  $("#plugins9").jstree({
    "plugins" : [ "wholerow" ]
  });
});</code></pre>
					</div>
					<div class="col-md-4">
						<div id="plugins9" class="demo plugin-demo">
							<ul>
								<li data-jstree='{"opened":true}'>Root node
									<ul>
										<li data-jstree='{"selected":true}'>Node</li>
									</ul>
								</li>
								<li>Root node 2</li>
							</ul>
						</div>
						<script>
						$(function () {
							$("#plugins9").jstree({
								"plugins" : [ "wholerow" ]
							});
						});
						</script>
					</div>
				</div>
			</div>
		</div>
		<!--.-->
	</div>
	<a class="hidden-xs hidden-sm" href="https://github.com/vakata/jstree"><img style="position: absolute; top: 0; left: 0; border: 0;" src="https://s3.amazonaws.com/github/ribbons/forkme_left_green_007200.png" alt="Fork me on GitHub"></a>
	<script src="./assets/jquery-1.10.2.min.js"></script>
	<script src="./assets/jquery.address-1.6.js"></script>
	<script src="./assets/vakata.js"></script>
	<script src="./assets/dist/jstree.min.js"></script>
	<script src="./assets/docs.js"></script>
	<script>$.each($q,function(i,f){$(f)});$q=null;</script>
</body>
</html>