// Hungarian (hu)
plupload.addI18n({"Stop Upload":"Stop Upload","Upload URL might be wrong or doesn't exist.":"A megadott URL hibás vagy nem létezik.","tb":"","Size":"<PERSON><PERSON><PERSON>","Close":"<PERSON><PERSON><PERSON><PERSON><PERSON>","Init error.":"Init hiba.","Add files to the upload queue and click the start button.":"Válaszd ki a fájlokat, majd kattints az Indítás gombra.","Filename":"Fájlnév","Image format either wrong or not supported.":"Image format either wrong or not supported.","Status":"Állapot","HTTP Error.":"HTTP hiba.","Start Upload":"Start Upload","mb":"","kb":"","Duplicate file error.":"","File size error.":"Hibás fájlméret.","N/A":"Nem elérhető","gb":"","Error: Invalid file extension:":"Error: Invalid file extension:","Select files":"Fájlok kiválasztása","%s already present in the queue.":"","File: %s":"Fájl: %s","b":"","Uploaded %d/%d files":"Feltöltött fájlok: %d/%d","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"A feltöltés egyszerre csak %d fájlt fogad el, a többi fájl nem lesz feltöltve.","%d files queued":"%d fájl sorbaállítva","File: %s, size: %d, max file size: %d":"","Drag files here.":"Húzd ide a fájlokat.","Runtime ran out of available memory.":"Runtime ran out of available memory.","File count error.":"A fájlok számával kapcsolatos hiba.","File extension error.":"Hibás fájlkiterjesztés.","Error: File too large:":"Error: File too large:","Add Files":"Add Files"});