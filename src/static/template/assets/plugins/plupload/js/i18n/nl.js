// Dutch (nl)
plupload.addI18n({"Stop Upload":"Stop Upload","Upload URL might be wrong or doesn't exist.":"Upload URL might be wrong or doesn't exist.","tb":"","Size":"<PERSON>root<PERSON>","Close":"Close","Init error.":"Initialisatie error.","Add files to the upload queue and click the start button.":"Voeg bestanden toe aan de wachtrij en druk op 'Start'.","Filename":"Bestandsnaam","Image format either wrong or not supported.":"Image format either wrong or not supported.","Status":"Status","HTTP Error.":"HTTP Error.","Start Upload":"Start Upload","mb":"","kb":"","Duplicate file error.":"","File size error.":"Bestandsgrootte Error.","N/A":"Niet beschikbaar","gb":"","Error: Invalid file extension:":"Error: Invalid file extension:","Select files":"Selecteer bestand(en):","%s already present in the queue.":"","File: %s":"File: %s","b":"","Uploaded %d/%d files":"%d/%d bestanden ge-upload","Upload element accepts only %d file(s) at a time. Extra files were stripped.":"Upload element accepts only %d file(s) at a time. Extra files were stripped.","%d files queued":"%d files queued","File: %s, size: %d, max file size: %d":"","Drag files here.":"Sleep bestanden hierheen.","Runtime ran out of available memory.":"Runtime ran out of available memory.","File count error.":"File count error.","File extension error.":"Ongeldig bestandstype.","Error: File too large:":"Error: File too large:","Add Files":"Add Files"});