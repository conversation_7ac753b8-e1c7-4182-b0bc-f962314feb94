<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="utf-8">
<title>Subscription Signup | Marketo</title>

<script src="../../lib/jquery.js"></script>
<script src="../../lib/jquery.mockjax.js"></script>
<script src="../../jquery.validate.js"></script>

<script src="jquery.maskedinput.js"></script>
<script src="mktSignup.js"></script>

<link rel="stylesheet" href="stylesheet.css" />
</head>
<body>
<!-- start page wrapper --><div id="letterbox">

<!-- start header container -->
<div id="header-background">
  <div class="nav-global-container">
    <div class="login"><a href="#"><span></span>Customer Login</a></div>
    <div class="logo"><a href="#"><img src="images/logo_marketo.gif" width="168" height="73"  alt="Marketo" /></a></div>
    <div class="nav-global">
      <ul>
        <li><a href="#" class="nav-g01"><span></span>Home</a></li>
        <li><a href="#" class="nav-g02"><span></span>Products</a></li>
        <li><a href="#" class="nav-g04"><span></span>B2B Marketing Resources</a></li>
        <li><a href="#" class="nav-g05"><span></span>About Marketo</a></li>
      </ul>
    </div>
  </div>
</div>
<!-- end header container -->
<div class="line-grey-tier"></div>

<!-- start page container 2 div-->
<div id="page-container" class="resize"><div id="page-content-inner" class="resize">

<!-- start col-main -->

<div id="col-main" class="resize" style="">



  <!-- start main content  -->
  <div class="main-content resize">

  <div class="action-container" style="display:none;"></div>


<h1>Step 2 of 2</h1>
<h2>Billing Information</h2>
<p>
</p>
<br clear="all" />
<div>
  <form id="billingForm" action="" method="get" >

    <div class="error" style="display:none;">
      <img src="images/warning.gif" alt="Warning!" width="24" height="24" style="float:left; margin: -5px 10px 0px 0px; " />

      <span></span>.<br clear="all" />
    </div>
    <table cellpadding="0" cellspacing="0" border="0">
      <tr>
        <td class="label" style="vertical-align: top; padding-top: 8px;">Billing Address:</td>
        <td class="field" style="font-weight: normal">
          <div class="billingAddressControl">

            <input type="checkbox" id="bill_to_co" name="bill_to_co" class="toggleCheck" checked="checked" style="width: auto;" tabindex="1" />
            <label for="bill_to_co" style="cursor:pointer">Same as Company Address</label>
          </div>
        </td>
      </tr>
      <tr class="subTable">
        <td colspan="2">
          <div style="background-color: #EEEEEE; border: 1px solid #CCCCCC; padding: 10px;" class="subTableDiv">
            <table cellpadding="0" cellspacing="0" border="0">
              <tr>
                <td class="label"><label for="bill_first_name">First Name:</label></td>
                <td class="field">
                  <input  maxlength="40" class="billingRequired" name="bill_first_name" size="20" type="text" tabindex="2" value="" />
                </td>
              </tr>
              <tr>
                <td class="label"><label for="bill_last_name">Last Name:</label></td>
                <td class="field">
                  <input  maxlength="40" class="billingRequired" name="bill_last_name" size="20" type="text" tabindex="3" value="" />
                </td>
              </tr>
              <tr>
                <td class="label"><label for="bill_email">Email:</label></td>
                <td class="field">
                  <input  maxlength="40" class="billingRequired email" remote="emails.action" name="email" size="20" type="text" tabindex="4" value="" />
                  <div class="formError"></div>
                </td>
              </tr>
              <tr>
                <td class="label"><label for="bill_address1">Address:</label></td>
                <td class="field">
                  <input  maxlength="40" class="billingRequired" name="bill_address1" size="20" type="text" tabindex="5" value="" />
                </td>
              </tr>
              <tr>
                <td class="label"></td>
                <td class="field">
                  <input  maxlength="40" name="bill_address2" size="20" type="text" tabindex="6" value="" />
                </td>
              </tr>
              <tr>
                <td class="label"><label for="bill_city">City:</label></td>
                <td class="field">
                  <input  maxlength="40" class="billingRequired" name="bill_city" size="20" type="text" tabindex="7" value="" />
                </td>
              </tr>
              <tr>
                <td class="label"><label for="bill_state">State:</label></td>
                <td class="field">
                  <select id="bill_state" class="billingRequired" name="bill_state" style="margin-left: 4px;" tabindex="8">
					<option value="">Choose State</option>
                    <option value="AL">Alabama</option><option value="AK">Alaska</option><option value="AZ">Arizona</option><option value="AR">Arkansas</option><option value="CA">California</option><option value="CO">Colorado</option><option value="CT">Connecticut</option><option value="DE">Delaware</option><option value="FL">Florida</option><option value="GA">Georgia</option><option value="HI">Hawaii</option><option value="ID">Idaho</option><option value="IL">Illinois</option><option value="IN">Indiana</option><option value="IA">Iowa</option><option value="KS">Kansas</option><option value="KY">Kentucky</option><option value="LA">Louisiana</option><option value="ME">Maine</option><option value="MD">Maryland</option><option value="MA">Massachusetts</option><option value="MI">Michigan</option><option value="MN">Minnesota</option><option value="MS">Mississippi</option><option value="MO">Missouri</option><option value="MT">Montana</option><option value="NE">Nebraska</option><option value="NV">Nevada</option><option value="NH">New Hampshire</option><option value="NJ">New Jersey</option><option value="NM">New Mexico</option><option value="NY">New York</option><option value="NC">North Carolina</option><option value="ND">North Dakota</option><option value="OH">Ohio</option><option value="OK">Oklahoma</option><option value="OR">Oregon</option><option value="PA">Pennsylvania</option><option value="RI">Rhode Island</option><option value="SC">South Carolina</option><option value="SD">South Dakota</option><option value="TN">Tennessee</option><option value="TX">Texas</option><option value="UT">Utah</option><option value="VT">Vermont</option><option value="VA">Virginia</option><option value="WA">Washington</option><option value="WV">West Virginia</option><option value="WI">Wisconsin</option><option value="WY">Wyoming</option>
                  </select>
                </td>
              </tr>

              <tr>
                <td class="label"><label for="bill_zip">Zip:</label></td>
                <td class="field">
                  <input  maxlength="10" class="billingRequired zipcode" name="bill_zip"  style="width: 100px" type="text" class="zipcode" tabindex="9" value="" />
                </td>
              </tr>

              <tr>
                <td class="label"><label for="bill_phone">Phone:</label></td>
                <td class="field">
                  <input  maxlength="14" class="billingRequired phone" name="bill_phone" style="width: 100px" type="text" class="phone" tabindex="10" value="" />
                </td>
              </tr>
            </table>
          </div>
        </td>
      </tr>
      <tr>
        <td class="label">Credit Card Type:</td>
        <td class="field">
          <select id="cc_type" class="required" name="cc_type" class="creditCardType" tabindex="11">
            <option value="">Choose Credit Card</option>
            <option value="amex">American Express</option>
            <option value="discover">Discover</option>
            <option value="mastercard">MasterCard</option>
            <option value="visa">Visa</option>
          </select>
        </td>
      </tr>
      <tr>
        <td class="label">Expiration:</td>
        <td class="field">
          <select id="cc_exp_month" name="cc_exp_month" title="ExpirationMonth" tabindex="12">
            <option value="01">01 - Jan</option>
            <option value="02">02 - Feb</option>
            <option value="03">03 - Mar</option>
            <option value="04">04 - Apr</option>
            <option value="05">05 - May</option>
            <option value="06">06 - Jun</option>
            <option value="07">07 - Jul</option>
            <option value="08">08 - Aug</option>
            <option value="09">09 - Sep</option>
            <option value="10">10 - Oct</option>
            <option value="11">11 - Nov</option>
            <option value="12">12 - Dec</option>
          </select>
          <select id="cc_exp_year" name="cc_exp_year" title="ExpirationYear" tabindex="13">
            <option value="2007">2007</option>
            <option value="2008" selected="selected">2008</option>
            <option value="2009">2009</option>
            <option value="2010">2010</option>
            <option value="2011">2011</option>
            <option value="2012">2012</option>
            <option value="2013">2013</option>
            <option value="2014">2014</option>
            <option value="2015">2015</option>
            <option value="2016">2016</option>
          </select>
        </td>
      </tr>
      <tr>
        <td class="label"><label for="credit_card">Credit Card Number:</label></td>
        <td class="field">
          <input maxlength="40" id="creditcard" class="required" name="credit_card" size="20" type="text" tabindex="14" />
        </td>
      </tr>
      <tr>
        <td class="label"><label for="cc_cvv">Security Code:</label></td>
        <td class="field">
          <input id="ccNumber" class="required" maxlength="4" name="cc_cvv" style="width: 30px;" type="text" style="vertical-align: top;" tabindex="16" value="" />
        </td>
      </tr>
      <tr>
        <td></td>
        <td>
          <div class="buttonSubmit">
            <span></span>
            <input class="formButton" type="submit" value="Finish" style="width: 180px" />
          </div><br clear="all"/>

        </td>
      </tr>
    </table>
  </form>
  <br clear="all" />

</div>



	</div>	<!-- end main content  -->
	<br />
</div> <!-- end col-main -->

<!-- start left col -->
<div id="col-left" class="nav-left-back empty resize" style="position: absolute; min-height: 450px;">
  <div class="col-left-header-tab" style="position: absolute;">Signup</div>
  <div class="nav-left">


  </div>


      <div class="left-nav-callout png" style="top: 15px; margin-bottom: 100px;">
        <img src="images/left-nav-callout-long.png"  class="png" alt="" />
        <h6>Sign Up Process</h6>
        <a style="background-image: url(images/step1-24.gif); font-weight: normal; text-decoration: none; cursor: default;">Sign up with a valid credit card.</a>
        <a style="background-image: url(images/step2-24.gif); font-weight: normal; text-decoration: none; cursor: default;">Connect to your Google AdWords account.  You will need your AdWords Customer ID.</a>

        <a target="_blank" style="background-image: url(images/step3-24.gif); font-weight: normal; text-decoration: none; cursor: default;">Start your 30 day trial.  No payments until trial ends.</a>
      </div>

<div class="footerAddress">
<b>Marketo Inc.</b><br />
1710 S. Amphlett Blvd.<br />
San Mateo, CA 94402 USA<br />
</div>
<br clear="all"/>
</div>	<!-- end left col -->

</div>  </div>  <!-- end page container 2 divs-->

  <div id="footer-container" align="center">
   <div class="footer">
		<ul>
		<li><a href="..">Home</a></li>
		<li class="line-off"><a href=".">Back to first step</a></li>
		</ul>
		</div></div>



<!-- end page wrapper -->
</div>

    </body>
</html>
