# <pre>
# This file is in the public domain, so clarified as of
# 2009-05-17 by <PERSON>.

# This data is by no means authoritative; if you think you know better,
# go ahead and edit the file (and please send any changes to
# <EMAIL> for general use in the future).

# From <PERSON> (2006-03-22):
#
# A good source for time zone historical data outside the U.S. is
# <PERSON> and <PERSON><PERSON>, The International Atlas (6th edition),
# San Diego: ACS Publications, Inc. (2003).
#
# <PERSON><PERSON><PERSON> writes that a good source
# for recent time zone data is the International Air Transport
# Association's Standard Schedules Information Manual (IATA SSIM),
# published semiannually.  <PERSON> sent in several helpful summaries
# of the IATA's data after 1990.
#
# Except where otherwise noted, <PERSON>ks & Pottenger is the source for
# entries through 1990, and IATA SSIM is the source for entries afterwards.
#
# Another source occasionally used is <PERSON>, World Time Differences,
# Whitman Publishing Co, 2 Niagara Av, Ealing, London (undated), which
# I found in the UCLA library.
#
# A reliable and entertaining source about time zones is
# <PERSON>, Greenwich time and longitude, <PERSON> (1997).
#
# I invented the abbreviations marked `*' in the following table;
# the rest are from earlier versions of this file, or from other sources.
# Corrections are welcome!
#	     std  dst
#	     LMT	Local Mean Time
#	2:00 EET  EEST	Eastern European Time
#	2:00 IST  IDT	Israel
#	3:00 AST  ADT	Arabia*
#	3:30 IRST IRDT	Iran
#	4:00 GST	Gulf*
#	5:30 IST	India
#	7:00 ICT	Indochina*
#	7:00 WIT	west Indonesia
#	8:00 CIT	central Indonesia
#	8:00 CST	China
#	9:00 CJT	Central Japanese Time (1896/1937)*
#	9:00 EIT	east Indonesia
#	9:00 JST  JDT	Japan
#	9:00 KST  KDT	Korea
#	9:30 CST	(Australian) Central Standard Time
#
# See the `europe' file for Russia and Turkey in Asia.

# From Guy Harris:
# Incorporates data for Singapore from Robert Elz' asia 1.1, as well as
# additional information from Tom Yap, Sun Microsystems Intercontinental
# Technical Support (including a page from the Official Airline Guide -
# Worldwide Edition).  The names for time zones are guesses.

###############################################################################

# These rules are stolen from the `europe' file.
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	EUAsia	1981	max	-	Mar	lastSun	 1:00u	1:00	S
Rule	EUAsia	1979	1995	-	Sep	lastSun	 1:00u	0	-
Rule	EUAsia	1996	max	-	Oct	lastSun	 1:00u	0	-
Rule E-EurAsia	1981	max	-	Mar	lastSun	 0:00	1:00	S
Rule E-EurAsia	1979	1995	-	Sep	lastSun	 0:00	0	-
Rule E-EurAsia	1996	max	-	Oct	lastSun	 0:00	0	-
Rule RussiaAsia	1981	1984	-	Apr	1	 0:00	1:00	S
Rule RussiaAsia	1981	1983	-	Oct	1	 0:00	0	-
Rule RussiaAsia	1984	1991	-	Sep	lastSun	 2:00s	0	-
Rule RussiaAsia	1985	1991	-	Mar	lastSun	 2:00s	1:00	S
Rule RussiaAsia	1992	only	-	Mar	lastSat	23:00	1:00	S
Rule RussiaAsia	1992	only	-	Sep	lastSat	23:00	0	-
Rule RussiaAsia	1993	max	-	Mar	lastSun	 2:00s	1:00	S
Rule RussiaAsia	1993	1995	-	Sep	lastSun	 2:00s	0	-
Rule RussiaAsia	1996	max	-	Oct	lastSun	 2:00s	0	-

# Afghanistan
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Kabul	4:36:48 -	LMT	1890
			4:00	-	AFT	1945
			4:30	-	AFT

# Armenia
# From Paul Eggert (2006-03-22):
# Shanks & Pottenger have Yerevan switching to 3:00 (with Russian DST)
# in spring 1991, then to 4:00 with no DST in fall 1995, then
# readopting Russian DST in 1997.  Go with Shanks & Pottenger, even
# when they disagree with others.  Edgar Der-Danieliantz
# reported (1996-05-04) that Yerevan probably wouldn't use DST
# in 1996, though it did use DST in 1995.  IATA SSIM (1991/1998) reports that
# Armenia switched from 3:00 to 4:00 in 1998 and observed DST after 1991,
# but started switching at 3:00s in 1998.

# From Arthur David Olson (2011-06-15):
# While Russia abandoned DST in 2011, Armenia may choose to
# follow Russia's "old" rules.

# From Alexander Krivenyshev (2012-02-10):
# According to News Armenia, on Feb 9, 2012,
# http://newsarmenia.ru/society/20120209/42609695.html
#
# The Armenia National Assembly adopted final reading of Amendments to the
# Law "On procedure of calculation time on the territory of the Republic of
# Armenia" according to which Armenia [is] abolishing Daylight Saving Time.
# or
# (brief)
# http://www.worldtimezone.com/dst_news/dst_news_armenia03.html
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Yerevan	2:58:00 -	LMT	1924 May  2
			3:00	-	YERT	1957 Mar    # Yerevan Time
			4:00 RussiaAsia YER%sT	1991 Mar 31 2:00s
			3:00	1:00	YERST	1991 Sep 23 # independence
			3:00 RussiaAsia	AM%sT	1995 Sep 24 2:00s
			4:00	-	AMT	1997
			4:00 RussiaAsia	AM%sT	2012 Mar 25 2:00s
			4:00	-	AMT

# Azerbaijan
# From Rustam Aliyev of the Azerbaijan Internet Forum (2005-10-23):
# According to the resolution of Cabinet of Ministers, 1997
# Resolution available at: http://aif.az/docs/daylight_res.pdf
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Azer	1997	max	-	Mar	lastSun	 4:00	1:00	S
Rule	Azer	1997	max	-	Oct	lastSun	 5:00	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Baku	3:19:24 -	LMT	1924 May  2
			3:00	-	BAKT	1957 Mar    # Baku Time
			4:00 RussiaAsia BAK%sT	1991 Mar 31 2:00s
			3:00	1:00	BAKST	1991 Aug 30 # independence
			3:00 RussiaAsia	AZ%sT	1992 Sep lastSat 23:00
			4:00	-	AZT	1996 # Azerbaijan time
			4:00	EUAsia	AZ%sT	1997
			4:00	Azer	AZ%sT

# Bahrain
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Bahrain	3:22:20 -	LMT	1920		# Al Manamah
			4:00	-	GST	1972 Jun
			3:00	-	AST

# Bangladesh
# From Alexander Krivenyshev (2009-05-13):
# According to newspaper Asian Tribune (May 6, 2009) Bangladesh may introduce
# Daylight Saving Time from June 16 to Sept 30
#
# Bangladesh to introduce daylight saving time likely from June 16
# <a href="http://www.asiantribune.com/?q=node/17288">
# http://www.asiantribune.com/?q=node/17288
# </a>
# or
# <a href="http://www.worldtimezone.com/dst_news/dst_news_bangladesh02.html">
# http://www.worldtimezone.com/dst_news/dst_news_bangladesh02.html
# </a>
#
# "... Bangladesh government has decided to switch daylight saving time from
# June
# 16 till September 30 in a bid to ensure maximum use of daylight to cope with
# crippling power crisis. "
#
# The switch will remain in effect from June 16 to Sept 30 (2009) but if
# implemented the next year, it will come in force from April 1, 2010

# From Steffen Thorsen (2009-06-02):
# They have finally decided now, but changed the start date to midnight between
# the 19th and 20th, and they have not set the end date yet.
#
# Some sources:
# <a href="http://in.reuters.com/article/southAsiaNews/idINIndia-40017620090601">
# http://in.reuters.com/article/southAsiaNews/idINIndia-40017620090601
# </a>
# <a href="http://bdnews24.com/details.php?id=85889&cid=2">
# http://bdnews24.com/details.php?id=85889&cid=2
# </a>
#
# Our wrap-up:
# <a href="http://www.timeanddate.com/news/time/bangladesh-daylight-saving-2009.html">
# http://www.timeanddate.com/news/time/bangladesh-daylight-saving-2009.html
# </a>

# From A. N. M. Kamrus Saadat (2009-06-15):
# Finally we've got the official mail regarding DST start time where DST start
# time is mentioned as Jun 19 2009, 23:00 from BTRC (Bangladesh
# Telecommunication Regulatory Commission).
#
# No DST end date has been announced yet.

# From Alexander Krivenyshev (2009-09-25):
# Bangladesh won't go back to Standard Time from October 1, 2009,
# instead it will continue DST measure till the cabinet makes a fresh decision.
#
# Following report by same newspaper-"The Daily Star Friday":
# "DST change awaits cabinet decision-Clock won't go back by 1-hr from Oct 1"
# <a href="http://www.thedailystar.net/newDesign/news-details.php?nid=107021">
# http://www.thedailystar.net/newDesign/news-details.php?nid=107021
# </a>
# or
# <a href="http://www.worldtimezone.com/dst_news/dst_news_bangladesh04.html">
# http://www.worldtimezone.com/dst_news/dst_news_bangladesh04.html
# </a>

# From Steffen Thorsen (2009-10-13):
# IANS (Indo-Asian News Service) now reports:
# Bangladesh has decided that the clock advanced by an hour to make
# maximum use of daylight hours as an energy saving measure would
# "continue for an indefinite period."
#
# One of many places where it is published:
# <a href="http://www.thaindian.com/newsportal/business/bangladesh-to-continue-indefinitely-with-advanced-time_100259987.html">
# http://www.thaindian.com/newsportal/business/bangladesh-to-continue-indefinitely-with-advanced-time_100259987.html
# </a>

# From Alexander Krivenyshev (2009-12-24):
# According to Bangladesh newspaper "The Daily Star,"
# Bangladesh will change its clock back to Standard Time on Dec 31, 2009.
#
# Clock goes back 1-hr on Dec 31 night.
# <a href="http://www.thedailystar.net/newDesign/news-details.php?nid=119228">
# http://www.thedailystar.net/newDesign/news-details.php?nid=119228
# </a>
# and
# <a href="http://www.worldtimezone.com/dst_news/dst_news_bangladesh05.html">
# http://www.worldtimezone.com/dst_news/dst_news_bangladesh05.html
# </a>
#
# "...The government yesterday decided to put the clock back by one hour
# on December 31 midnight and the new time will continue until March 31,
# 2010 midnight. The decision came at a cabinet meeting at the Prime
# Minister's Office last night..."

# From Alexander Krivenyshev (2010-03-22):
# According to Bangladesh newspaper "The Daily Star,"
# Cabinet cancels Daylight Saving Time
# <a href="http://www.thedailystar.net/newDesign/latest_news.php?nid=22817">
# http://www.thedailystar.net/newDesign/latest_news.php?nid=22817
# </a>
# or
# <a href="http://www.worldtimezone.com/dst_news/dst_news_bangladesh06.html">
# http://www.worldtimezone.com/dst_news/dst_news_bangladesh06.html
# </a>

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Dhaka	2009	only	-	Jun	19	23:00	1:00	S
Rule	Dhaka	2009	only	-	Dec	31	23:59	0	-

# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Dhaka	6:01:40 -	LMT	1890
			5:53:20	-	HMT	1941 Oct    # Howrah Mean Time?
			6:30	-	BURT	1942 May 15 # Burma Time
			5:30	-	IST	1942 Sep
			6:30	-	BURT	1951 Sep 30
			6:00	-	DACT	1971 Mar 26 # Dacca Time
			6:00	-	BDT	2009
			6:00	Dhaka	BD%sT

# Bhutan
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Thimphu	5:58:36 -	LMT	1947 Aug 15 # or Thimbu
			5:30	-	IST	1987 Oct
			6:00	-	BTT	# Bhutan Time

# British Indian Ocean Territory
# Whitman and the 1995 CIA time zone map say 5:00, but the
# 1997 and later maps say 6:00.  Assume the switch occurred in 1996.
# We have no information as to when standard time was introduced;
# assume it occurred in 1907, the same year as Mauritius (which
# then contained the Chagos Archipelago).
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Indian/Chagos	4:49:40	-	LMT	1907
			5:00	-	IOT	1996 # BIOT Time
			6:00	-	IOT

# Brunei
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Brunei	7:39:40 -	LMT	1926 Mar   # Bandar Seri Begawan
			7:30	-	BNT	1933
			8:00	-	BNT

# Burma / Myanmar
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Rangoon	6:24:40 -	LMT	1880		# or Yangon
			6:24:36	-	RMT	1920	   # Rangoon Mean Time?
			6:30	-	BURT	1942 May   # Burma Time
			9:00	-	JST	1945 May 3
			6:30	-	MMT		   # Myanmar Time

# Cambodia
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Phnom_Penh	6:59:40 -	LMT	1906 Jun  9
			7:06:20	-	SMT	1911 Mar 11 0:01 # Saigon MT?
			7:00	-	ICT	1912 May
			8:00	-	ICT	1931 May
			7:00	-	ICT

# China

# From Guy Harris:
# People's Republic of China.  Yes, they really have only one time zone.

# From Bob Devine (1988-01-28):
# No they don't.  See TIME mag, 1986-02-17 p.52.  Even though
# China is across 4 physical time zones, before Feb 1, 1986 only the
# Peking (Bejing) time zone was recognized.  Since that date, China
# has two of 'em -- Peking's and Urumqi (named after the capital of
# the Xinjiang Uyghur Autonomous Region).  I don't know about DST for it.
#
# . . .I just deleted the DST table and this editor makes it too
# painful to suck in another copy..  So, here is what I have for
# DST start/end dates for Peking's time zone (info from AP):
#
#     1986 May 4 - Sept 14
#     1987 mid-April - ??

# From U. S. Naval Observatory (1989-01-19):
# CHINA               8 H  AHEAD OF UTC  ALL OF CHINA, INCL TAIWAN
# CHINA               9 H  AHEAD OF UTC  APR 17 - SEP 10

# From Paul Eggert (2006-03-22):
# Shanks & Pottenger write that China (except for Hong Kong and Macau)
# has had a single time zone since 1980 May 1, observing summer DST
# from 1986 through 1991; this contradicts Devine's
# note about Time magazine, though apparently _something_ happened in 1986.
# Go with Shanks & Pottenger for now.  I made up names for the other
# pre-1980 time zones.

# From Shanks & Pottenger:
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Shang	1940	only	-	Jun	 3	0:00	1:00	D
Rule	Shang	1940	1941	-	Oct	 1	0:00	0	S
Rule	Shang	1941	only	-	Mar	16	0:00	1:00	D
Rule	PRC	1986	only	-	May	 4	0:00	1:00	D
Rule	PRC	1986	1991	-	Sep	Sun>=11	0:00	0	S
Rule	PRC	1987	1991	-	Apr	Sun>=10	0:00	1:00	D

# From Anthony Fok (2001-12-20):
# BTW, I did some research on-line and found some info regarding these five
# historic timezones from some Taiwan websites.  And yes, there are official
# Chinese names for these locales (before 1949).
#
# From Jesper Norgaard Welen (2006-07-14):
# I have investigated the timezones around 1970 on the
# http://www.astro.com/atlas site [with provinces and county
# boundaries summarized below]....  A few other exceptions were two
# counties on the Sichuan side of the Xizang-Sichuan border,
# counties Dege and Baiyu which lies on the Sichuan side and are
# therefore supposed to be GMT+7, Xizang region being GMT+6, but Dege
# county is GMT+8 according to astro.com while Baiyu county is GMT+6
# (could be true), for the moment I am assuming that those two
# counties are mistakes in the astro.com data.

# From Paul Eggert (2008-02-11):
# I just now checked Google News for western news sources that talk
# about China's single time zone, and couldn't find anything before 1986
# talking about China being in one time zone.  (That article was: Jim
# Mann, "A clumsy embrace for another western custom: China on daylight
# time--sort of", Los Angeles Times, 1986-05-05.  By the way, this
# article confirms the tz database's data claiming that China began
# observing daylight saving time in 1986.
#
# From Thomas S. Mullaney (2008-02-11):
# I think you're combining two subjects that need to treated
# separately: daylight savings (which, you're correct, wasn't
# implemented until the 1980s) and the unified time zone centered near
# Beijing (which was implemented in 1949). Briefly, there was also a
# "Lhasa Time" in Tibet and "Urumqi Time" in Xinjiang. The first was
# ceased, and the second eventually recognized (again, in the 1980s).
#
# From Paul Eggert (2008-06-30):
# There seems to be a good chance China switched to a single time zone in 1949
# rather than in 1980 as Shanks & Pottenger have it, but we don't have a
# reliable documentary source saying so yet, so for now we still go with
# Shanks & Pottenger.

# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
# Changbai Time ("Long-white Time", Long-white = Heilongjiang area)
# Heilongjiang (except Mohe county), Jilin
Zone	Asia/Harbin	8:26:44	-	LMT	1928 # or Haerbin
			8:30	-	CHAT	1932 Mar # Changbai Time
			8:00	-	CST	1940
			9:00	-	CHAT	1966 May
			8:30	-	CHAT	1980 May
			8:00	PRC	C%sT
# Zhongyuan Time ("Central plain Time")
# most of China
Zone	Asia/Shanghai	8:05:52	-	LMT	1928
			8:00	Shang	C%sT	1949
			8:00	PRC	C%sT
# Long-shu Time (probably due to Long and Shu being two names of that area)
# Guangxi, Guizhou, Hainan, Ningxia, Sichuan, Shaanxi, and Yunnan;
# most of Gansu; west Inner Mongolia; west Qinghai; and the Guangdong
# counties Deqing, Enping, Kaiping, Luoding, Taishan, Xinxing,
# Yangchun, Yangjiang, Yu'nan, and Yunfu.
Zone	Asia/Chongqing	7:06:20	-	LMT	1928 # or Chungking
			7:00	-	LONT	1980 May # Long-shu Time
			8:00	PRC	C%sT
# Xin-zang Time ("Xinjiang-Tibet Time")
# The Gansu counties Aksay, Anxi, Dunhuang, Subei; west Qinghai;
# the Guangdong counties  Xuwen, Haikang, Suixi, Lianjiang,
# Zhanjiang, Wuchuan, Huazhou, Gaozhou, Maoming, Dianbai, and Xinyi;
# east Tibet, including Lhasa, Chamdo, Shigaise, Jimsar, Shawan and Hutubi;
# east Xinjiang, including Urumqi, Turpan, Karamay, Korla, Minfeng, Jinghe,
# Wusu, Qiemo, Xinyan, Wulanwusu, Jinghe, Yumin, Tacheng, Tuoli, Emin,
# Shihezi, Changji, Yanqi, Heshuo, Tuokexun, Tulufan, Shanshan, Hami,
# Fukang, Kuitun, Kumukuli, Miquan, Qitai, and Turfan.
Zone	Asia/Urumqi	5:50:20	-	LMT	1928 # or Urumchi
			6:00	-	URUT	1980 May # Urumqi Time
			8:00	PRC	C%sT
# Kunlun Time
# West Tibet, including Pulan, Aheqi, Shufu, Shule;
# West Xinjiang, including Aksu, Atushi, Yining, Hetian, Cele, Luopu, Nileke,
# Zhaosu, Tekesi, Gongliu, Chabuchaer, Huocheng, Bole, Pishan, Suiding,
# and Yarkand.

# From Luther Ma (2009-10-17):
# Almost all (>99.9%) ethnic Chinese (properly ethnic Han) living in
# Xinjiang use Chinese Standard Time. Some are aware of Xinjiang time,
# but have no need of it. All planes, trains, and schools function on
# what is called "Beijing time." When Han make an appointment in Chinese
# they implicitly use Beijing time.
#
# On the other hand, ethnic Uyghurs, who make up about half the
# population of Xinjiang, typically use "Xinjiang time" which is two
# hours behind Beijing time, or UTC +0600. The government of the Xinjiang
# Uyghur Autonomous Region, (XAUR, or just Xinjiang for short) as well as
# local governments such as the Urumqi city government use both times in
# publications, referring to what is popularly called Xinjiang time as
# "Urumqi time." When Uyghurs make an appointment in the Uyghur language
# they almost invariably use Xinjiang time.
#
# (Their ethnic Han compatriots would typically have no clue of its
# widespread use, however, because so extremely few of them are fluent in
# Uyghur, comparable to the number of Anglo-Americans fluent in Navajo.)
#
# (...As with the rest of China there was a brief interval ending in 1990
# or 1991 when summer time was in use.  The confusion was severe, with
# the province not having dual times but four times in use at the same
# time. Some areas remained on standard Xinjiang time or Beijing time and
# others moving their clocks ahead.)
#
# ...an example of an official website using of Urumqi time.
#
# The first few lines of the Google translation of
# <a href="http://www.fjysgl.gov.cn/show.aspx?id=2379&cid=39">
# http://www.fjysgl.gov.cn/show.aspx?id=2379&cid=39
# </a>
# (retrieved 2009-10-13)
# > Urumqi fire seven people are missing the alleged losses of at least
# > 500 million yuan
# >
# > (Reporter Dong Liu) the day before 20:20 or so (Urumqi Time 18:20),
# > Urumqi City Department of International Plaza Luther Qiantang River
# > burst fire. As of yesterday, 18:30, Urumqi City Fire officers and men
# > have worked continuously for 22 hours...

# From Luther Ma (2009-11-19):
# With the risk of being redundant to previous answers these are the most common
# English "transliterations" (w/o using non-English symbols):
#
# 1. Wulumuqi...
# 2. Kashi...
# 3. Urumqi...
# 4. Kashgar...
# ...
# 5. It seems that Uyghurs in Urumqi has been using Xinjiang since at least the
# 1960's. I know of one Han, now over 50, who grew up in the surrounding
# countryside and used Xinjiang time as a child.
#
# 6. Likewise for Kashgar and the rest of south Xinjiang I don't know of any
# start date for Xinjiang time.
#
# Without having access to local historical records, nor the ability to legally
# publish them, I would go with October 1, 1949, when Xinjiang became the Uyghur
# Autonomous Region under the PRC. (Before that Uyghurs, of course, would also
# not be using Beijing time, but some local time.)

Zone	Asia/Kashgar	5:03:56	-	LMT	1928 # or Kashi or Kaxgar
			5:30	-	KAST	1940	 # Kashgar Time
			5:00	-	KAST	1980 May
			8:00	PRC	C%sT


# From Lee Yiu Chung (2009-10-24):
# I found there are some mistakes for the...DST rule for Hong
# Kong. [According] to the DST record from Hong Kong Observatory (actually,
# it is not [an] observatory, but the official meteorological agency of HK,
# and also serves as the official timing agency), there are some missing
# and incorrect rules. Although the exact switch over time is missing, I
# think 3:30 is correct. The official DST record for Hong Kong can be
# obtained from
# <a href="http://www.hko.gov.hk/gts/time/Summertime.htm">
# http://www.hko.gov.hk/gts/time/Summertime.htm
# </a>.

# From Arthur David Olson (2009-10-28):
# Here are the dates given at
# <a href="http://www.hko.gov.hk/gts/time/Summertime.htm">
# http://www.hko.gov.hk/gts/time/Summertime.htm
# </a>
# as of 2009-10-28:
# Year        Period
# 1941        1 Apr to 30 Sep
# 1942        Whole year
# 1943        Whole year
# 1944        Whole year
# 1945        Whole year
# 1946        20 Apr to 1 Dec
# 1947        13 Apr to 30 Dec
# 1948        2 May to 31 Oct
# 1949        3 Apr to 30 Oct
# 1950        2 Apr to 29 Oct
# 1951        1 Apr to 28 Oct
# 1952        6 Apr to 25 Oct
# 1953        5 Apr to 1 Nov
# 1954        21 Mar to 31 Oct
# 1955        20 Mar to 6 Nov
# 1956        18 Mar to 4 Nov
# 1957        24 Mar to 3 Nov
# 1958        23 Mar to 2 Nov
# 1959        22 Mar to 1 Nov
# 1960        20 Mar to 6 Nov
# 1961        19 Mar to 5 Nov
# 1962        18 Mar to 4 Nov
# 1963        24 Mar to 3 Nov
# 1964        22 Mar to 1 Nov
# 1965        18 Apr to 17 Oct
# 1966        17 Apr to 16 Oct
# 1967        16 Apr to 22 Oct
# 1968        21 Apr to 20 Oct
# 1969        20 Apr to 19 Oct
# 1970        19 Apr to 18 Oct
# 1971        18 Apr to 17 Oct
# 1972        16 Apr to 22 Oct
# 1973        22 Apr to 21 Oct
# 1973/74     30 Dec 73 to 20 Oct 74
# 1975        20 Apr to 19 Oct
# 1976        18 Apr to 17 Oct
# 1977        Nil
# 1978        Nil
# 1979        13 May to 21 Oct
# 1980 to Now Nil
# The page does not give start or end times of day.
# The page does not give a start date for 1942.
# The page does not givw an end date for 1945.
# The Japanese occupation of Hong Kong began on 1941-12-25.
# The Japanese surrender of Hong Kong was signed 1945-09-15.
# For lack of anything better, use start of those days as the transition times.

# Hong Kong (Xianggang)
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	HK	1941	only	-	Apr	1	3:30	1:00	S
Rule	HK	1941	only	-	Sep	30	3:30	0	-
Rule	HK	1946	only	-	Apr	20	3:30	1:00	S
Rule	HK	1946	only	-	Dec	1	3:30	0	-
Rule	HK	1947	only	-	Apr	13	3:30	1:00	S
Rule	HK	1947	only	-	Dec	30	3:30	0	-
Rule	HK	1948	only	-	May	2	3:30	1:00	S
Rule	HK	1948	1951	-	Oct	lastSun	3:30	0	-
Rule	HK	1952	only	-	Oct	25	3:30	0	-
Rule	HK	1949	1953	-	Apr	Sun>=1	3:30	1:00	S
Rule	HK	1953	only	-	Nov	1	3:30	0	-
Rule	HK	1954	1964	-	Mar	Sun>=18	3:30	1:00	S
Rule	HK	1954	only	-	Oct	31	3:30	0	-
Rule	HK	1955	1964	-	Nov	Sun>=1	3:30	0	-
Rule	HK	1965	1976	-	Apr	Sun>=16	3:30	1:00	S
Rule	HK	1965	1976	-	Oct	Sun>=16	3:30	0	-
Rule	HK	1973	only	-	Dec	30	3:30	1:00	S
Rule	HK	1979	only	-	May	Sun>=8	3:30	1:00	S
Rule	HK	1979	only	-	Oct	Sun>=16	3:30	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Hong_Kong	7:36:36 -	LMT	1904 Oct 30
			8:00	HK	HK%sT	1941 Dec 25
			9:00	-	JST	1945 Sep 15
			8:00	HK	HK%sT

###############################################################################

# Taiwan

# Shanks & Pottenger write that Taiwan observed DST during 1945, when it
# was still controlled by Japan.  This is hard to believe, but we don't
# have any other information.

# From smallufo (2010-04-03):
# According to Taiwan's CWB,
# <a href="http://www.cwb.gov.tw/V6/astronomy/cdata/summert.htm">
# http://www.cwb.gov.tw/V6/astronomy/cdata/summert.htm
# </a>
# Taipei has DST in 1979 between July 1st and Sep 30.

# From Arthur David Olson (2010-04-07):
# Here's Google's translation of the table at the bottom of the "summert.htm" page:
# Decade 	                                                    Name                      Start and end date
# Republic of China 34 years to 40 years (AD 1945-1951 years) Summer Time               May 1 to September 30
# 41 years of the Republic of China (AD 1952)                 Daylight Saving Time      March 1 to October 31
# Republic of China 42 years to 43 years (AD 1953-1954 years) Daylight Saving Time      April 1 to October 31
# In the 44 years to 45 years (AD 1955-1956 years)            Daylight Saving Time      April 1 to September 30
# Republic of China 46 years to 48 years (AD 1957-1959)       Summer Time               April 1 to September 30
# Republic of China 49 years to 50 years (AD 1960-1961)       Summer Time               June 1 to September 30
# Republic of China 51 years to 62 years (AD 1962-1973 years) Stop Summer Time
# Republic of China 63 years to 64 years (1974-1975 AD)       Daylight Saving Time      April 1 to September 30
# Republic of China 65 years to 67 years (1976-1978 AD)       Stop Daylight Saving Time
# Republic of China 68 years (AD 1979)                        Daylight Saving Time      July 1 to September 30
# Republic of China since 69 years (AD 1980)                  Stop Daylight Saving Time

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Taiwan	1945	1951	-	May	1	0:00	1:00	D
Rule	Taiwan	1945	1951	-	Oct	1	0:00	0	S
Rule	Taiwan	1952	only	-	Mar	1	0:00	1:00	D
Rule	Taiwan	1952	1954	-	Nov	1	0:00	0	S
Rule	Taiwan	1953	1959	-	Apr	1	0:00	1:00	D
Rule	Taiwan	1955	1961	-	Oct	1	0:00	0	S
Rule	Taiwan	1960	1961	-	Jun	1	0:00	1:00	D
Rule	Taiwan	1974	1975	-	Apr	1	0:00	1:00	D
Rule	Taiwan	1974	1975	-	Oct	1	0:00	0	S
Rule	Taiwan	1979	only	-	Jun	30	0:00	1:00	D
Rule	Taiwan	1979	only	-	Sep	30	0:00	0	S

# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Taipei	8:06:00 -	LMT	1896 # or Taibei or T'ai-pei
			8:00	Taiwan	C%sT

# Macau (Macao, Aomen)
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Macau	1961	1962	-	Mar	Sun>=16	3:30	1:00	S
Rule	Macau	1961	1964	-	Nov	Sun>=1	3:30	0	-
Rule	Macau	1963	only	-	Mar	Sun>=16	0:00	1:00	S
Rule	Macau	1964	only	-	Mar	Sun>=16	3:30	1:00	S
Rule	Macau	1965	only	-	Mar	Sun>=16	0:00	1:00	S
Rule	Macau	1965	only	-	Oct	31	0:00	0	-
Rule	Macau	1966	1971	-	Apr	Sun>=16	3:30	1:00	S
Rule	Macau	1966	1971	-	Oct	Sun>=16	3:30	0	-
Rule	Macau	1972	1974	-	Apr	Sun>=15	0:00	1:00	S
Rule	Macau	1972	1973	-	Oct	Sun>=15	0:00	0	-
Rule	Macau	1974	1977	-	Oct	Sun>=15	3:30	0	-
Rule	Macau	1975	1977	-	Apr	Sun>=15	3:30	1:00	S
Rule	Macau	1978	1980	-	Apr	Sun>=15	0:00	1:00	S
Rule	Macau	1978	1980	-	Oct	Sun>=15	0:00	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Macau	7:34:20 -	LMT	1912
			8:00	Macau	MO%sT	1999 Dec 20 # return to China
			8:00	PRC	C%sT


###############################################################################

# Cyprus
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Cyprus	1975	only	-	Apr	13	0:00	1:00	S
Rule	Cyprus	1975	only	-	Oct	12	0:00	0	-
Rule	Cyprus	1976	only	-	May	15	0:00	1:00	S
Rule	Cyprus	1976	only	-	Oct	11	0:00	0	-
Rule	Cyprus	1977	1980	-	Apr	Sun>=1	0:00	1:00	S
Rule	Cyprus	1977	only	-	Sep	25	0:00	0	-
Rule	Cyprus	1978	only	-	Oct	2	0:00	0	-
Rule	Cyprus	1979	1997	-	Sep	lastSun	0:00	0	-
Rule	Cyprus	1981	1998	-	Mar	lastSun	0:00	1:00	S
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Nicosia	2:13:28 -	LMT	1921 Nov 14
			2:00	Cyprus	EE%sT	1998 Sep
			2:00	EUAsia	EE%sT
# IATA SSIM (1998-09) has Cyprus using EU rules for the first time.

# Classically, Cyprus belongs to Asia; e.g. see Herodotus, Histories, I.72.
# However, for various reasons many users expect to find it under Europe.
Link	Asia/Nicosia	Europe/Nicosia

# Georgia
# From Paul Eggert (1994-11-19):
# Today's _Economist_ (p 60) reports that Georgia moved its clocks forward
# an hour recently, due to a law proposed by Zurab Murvanidze,
# an MP who went on a hunger strike for 11 days to force discussion about it!
# We have no details, but we'll guess they didn't move the clocks back in fall.
#
# From Mathew Englander, quoting AP (1996-10-23 13:05-04):
# Instead of putting back clocks at the end of October, Georgia
# will stay on daylight savings time this winter to save energy,
# President Eduard Shevardnadze decreed Wednesday.
#
# From the BBC via Joseph S. Myers (2004-06-27):
#
# Georgia moved closer to Western Europe on Sunday...  The former Soviet
# republic has changed its time zone back to that of Moscow.  As a result it
# is now just four hours ahead of Greenwich Mean Time, rather than five hours
# ahead.  The switch was decreed by the pro-Western president of Georgia,
# Mikhail Saakashvili, who said the change was partly prompted by the process
# of integration into Europe.

# From Teimuraz Abashidze (2005-11-07):
# Government of Georgia ... decided to NOT CHANGE daylight savings time on
# [Oct.] 30, as it was done before during last more than 10 years.
# Currently, we are in fact GMT +4:00, as before 30 October it was GMT
# +3:00.... The problem is, there is NO FORMAL LAW or governmental document
# about it.  As far as I can find, I was told, that there is no document,
# because we just DIDN'T ISSUE document about switching to winter time....
# I don't know what can be done, especially knowing that some years ago our
# DST rules where changed THREE TIMES during one month.


# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Tbilisi	2:59:16 -	LMT	1880
			2:59:16	-	TBMT	1924 May  2 # Tbilisi Mean Time
			3:00	-	TBIT	1957 Mar    # Tbilisi Time
			4:00 RussiaAsia TBI%sT	1991 Mar 31 2:00s
			3:00	1:00	TBIST	1991 Apr  9 # independence
			3:00 RussiaAsia GE%sT	1992 # Georgia Time
			3:00 E-EurAsia	GE%sT	1994 Sep lastSun
			4:00 E-EurAsia	GE%sT	1996 Oct lastSun
			4:00	1:00	GEST	1997 Mar lastSun
			4:00 E-EurAsia	GE%sT	2004 Jun 27
			3:00 RussiaAsia	GE%sT	2005 Mar lastSun 2:00
			4:00	-	GET

# East Timor

# See Indonesia for the 1945 transition.

# From Joao Carrascalao, brother of the former governor of East Timor, in
# <a href="http://etan.org/et99c/december/26-31/30ETMAY.htm">
# East Timor may be late for its millennium
# </a> (1999-12-26/31):
# Portugal tried to change the time forward in 1974 because the sun
# rises too early but the suggestion raised a lot of problems with the
# Timorese and I still don't think it would work today because it
# conflicts with their way of life.

# From Paul Eggert (2000-12-04):
# We don't have any record of the above attempt.
# Most likely our records are incomplete, but we have no better data.

# <a href="http://www.hri.org/news/world/undh/last/00-08-16.undh.html">
# From Manoel de Almeida e Silva, Deputy Spokesman for the UN Secretary-General
# (2000-08-16)</a>:
# The Cabinet of the East Timor Transition Administration decided
# today to advance East Timor's time by one hour.  The time change,
# which will be permanent, with no seasonal adjustment, will happen at
# midnight on Saturday, September 16.

# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Dili	8:22:20 -	LMT	1912
			8:00	-	TLT	1942 Feb 21 23:00 # E Timor Time
			9:00	-	JST	1945 Sep 23
			9:00	-	TLT	1976 May  3
			8:00	-	CIT	2000 Sep 17 00:00
			9:00	-	TLT

# India
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Kolkata	5:53:28 -	LMT	1880	# Kolkata
			5:53:20	-	HMT	1941 Oct    # Howrah Mean Time?
			6:30	-	BURT	1942 May 15 # Burma Time
			5:30	-	IST	1942 Sep
			5:30	1:00	IST	1945 Oct 15
			5:30	-	IST
# The following are like Asia/Kolkata:
#	Andaman Is
#	Lakshadweep (Laccadive, Minicoy and Amindivi Is)
#	Nicobar Is

# Indonesia
#
# From Gwillim Law (2001-05-28), overriding Shanks & Pottenger:
# <http://www.sumatera-inc.com/go_to_invest/about_indonesia.asp#standtime>
# says that Indonesia's time zones changed on 1988-01-01.  Looking at some
# time zone maps, I think that must refer to Western Borneo (Kalimantan Barat
# and Kalimantan Tengah) switching from UTC+8 to UTC+7.
#
# From Paul Eggert (2007-03-10):
# Here is another correction to Shanks & Pottenger.
# JohnTWB writes that Japanese forces did not surrender control in
# Indonesia until 1945-09-01 00:00 at the earliest (in Jakarta) and
# other formal surrender ceremonies were September 9, 11, and 13, plus
# September 12 for the regional surrender to Mountbatten in Singapore.
# These would be the earliest possible times for a change.
# Regimes horaires pour le monde entier, by Henri Le Corre, (Editions
# Traditionnelles, 1987, Paris) says that Java and Madura switched
# from JST to UTC+07:30 on 1945-09-23, and gives 1944-09-01 for Jayapura
# (Hollandia).  For now, assume all Indonesian locations other than Jayapura
# switched on 1945-09-23.
#
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone Asia/Jakarta	7:07:12 -	LMT	1867 Aug 10
# Shanks & Pottenger say the next transition was at 1924 Jan 1 0:13,
# but this must be a typo.
			7:07:12	-	JMT	1923 Dec 31 23:47:12 # Jakarta
			7:20	-	JAVT	1932 Nov	 # Java Time
			7:30	-	WIT	1942 Mar 23
			9:00	-	JST	1945 Sep 23
			7:30	-	WIT	1948 May
			8:00	-	WIT	1950 May
			7:30	-	WIT	1964
			7:00	-	WIT
Zone Asia/Pontianak	7:17:20	-	LMT	1908 May
			7:17:20	-	PMT	1932 Nov    # Pontianak MT
			7:30	-	WIT	1942 Jan 29
			9:00	-	JST	1945 Sep 23
			7:30	-	WIT	1948 May
			8:00	-	WIT	1950 May
			7:30	-	WIT	1964
			8:00	-	CIT	1988 Jan  1
			7:00	-	WIT
Zone Asia/Makassar	7:57:36 -	LMT	1920
			7:57:36	-	MMT	1932 Nov    # Macassar MT
			8:00	-	CIT	1942 Feb  9
			9:00	-	JST	1945 Sep 23
			8:00	-	CIT
Zone Asia/Jayapura	9:22:48 -	LMT	1932 Nov
			9:00	-	EIT	1944 Sep  1
			9:30	-	CST	1964
			9:00	-	EIT

# Iran

# From Roozbeh Pournader (2003-03-15):
# This is an English translation of what I just found (originally in Persian).
# The Gregorian dates in brackets are mine:
#
#	Official Newspaper No. 13548-1370/6/25 [1991-09-16]
#	No. 16760/T233 H				1370/6/10 [1991-09-01]
#
#	The Rule About Change of the Official Time of the Country
#
#	The Board of Ministers, in the meeting dated 1370/5/23 [1991-08-14],
#	based on the suggestion number 2221/D dated 1370/4/22 [1991-07-13]
#	of the Country's Organization for Official and Employment Affairs,
#	and referring to the law for equating the working hours of workers
#	and officers in the whole country dated 1359/4/23 [1980-07-14], and
#	for synchronizing the official times of the country, agreed that:
#
#	The official time of the country will should move forward one hour
#	at the 24[:00] hours of the first day of Farvardin and should return
#	to its previous state at the 24[:00] hours of the 30th day of
#	Shahrivar.
#
#	First Deputy to the President - Hassan Habibi
#
# From personal experience, that agrees with what has been followed
# for at least the last 5 years.  Before that, for a few years, the
# date used was the first Thursday night of Farvardin and the last
# Thursday night of Shahrivar, but I can't give exact dates....
# I have also changed the abbreviations to what is considered correct
# here in Iran, IRST for regular time and IRDT for daylight saving time.
#
# From Roozbeh Pournader (2005-04-05):
# The text of the Iranian law, in effect since 1925, clearly mentions
# that the true solar year is the measure, and there is no arithmetic
# leap year calculation involved.  There has never been any serious
# plan to change that law....
#
# From Paul Eggert (2006-03-22):
# Go with Shanks & Pottenger before Sept. 1991, and with Pournader thereafter.
# I used Ed Reingold's cal-persia in GNU Emacs 21.2 to check Persian dates,
# stopping after 2037 when 32-bit time_t's overflow.
# That cal-persia used Birashk's approximation, which disagrees with the solar
# calendar predictions for the year 2025, so I corrected those dates by hand.
#
# From Oscar van Vlijmen (2005-03-30), writing about future
# discrepancies between cal-persia and the Iranian calendar:
# For 2091 solar-longitude-after yields 2091-03-20 08:40:07.7 UT for
# the vernal equinox and that gets so close to 12:00 some local
# Iranian time that the definition of the correct location needs to be
# known exactly, amongst other factors.  2157 is even closer:
# 2157-03-20 08:37:15.5 UT.  But the Gregorian year 2025 should give
# no interpretation problem whatsoever.  By the way, another instant
# in the near future where there will be a discrepancy between
# arithmetical and astronomical Iranian calendars will be in 2058:
# vernal equinox on 2058-03-20 09:03:05.9 UT.  The Java version of
# Reingold's/Dershowitz' calculator gives correctly the Gregorian date
# 2058-03-21 for 1 Farvardin 1437 (astronomical).
#
# From Steffen Thorsen (2006-03-22):
# Several of my users have reported that Iran will not observe DST anymore:
# http://www.irna.ir/en/news/view/line-17/0603193812164948.htm
#
# From Reuters (2007-09-16), with a heads-up from Jesper Norgaard Welen:
# ... the Guardian Council ... approved a law on Sunday to re-introduce
# daylight saving time ...
# http://uk.reuters.com/article/oilRpt/idUKBLA65048420070916
#
# From Roozbeh Pournader (2007-11-05):
# This is quoted from Official Gazette of the Islamic Republic of
# Iran, Volume 63, Number 18242, dated Tuesday 1386/6/24
# [2007-10-16]. I am doing the best translation I can:...
# The official time of the country will be moved forward for one hour
# on the 24 hours of the first day of the month of Farvardin and will
# be changed back to its previous state on the 24 hours of the
# thirtieth day of Shahrivar.
#
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Iran	1978	1980	-	Mar	21	0:00	1:00	D
Rule	Iran	1978	only	-	Oct	21	0:00	0	S
Rule	Iran	1979	only	-	Sep	19	0:00	0	S
Rule	Iran	1980	only	-	Sep	23	0:00	0	S
Rule	Iran	1991	only	-	May	 3	0:00	1:00	D
Rule	Iran	1992	1995	-	Mar	22	0:00	1:00	D
Rule	Iran	1991	1995	-	Sep	22	0:00	0	S
Rule	Iran	1996	only	-	Mar	21	0:00	1:00	D
Rule	Iran	1996	only	-	Sep	21	0:00	0	S
Rule	Iran	1997	1999	-	Mar	22	0:00	1:00	D
Rule	Iran	1997	1999	-	Sep	22	0:00	0	S
Rule	Iran	2000	only	-	Mar	21	0:00	1:00	D
Rule	Iran	2000	only	-	Sep	21	0:00	0	S
Rule	Iran	2001	2003	-	Mar	22	0:00	1:00	D
Rule	Iran	2001	2003	-	Sep	22	0:00	0	S
Rule	Iran	2004	only	-	Mar	21	0:00	1:00	D
Rule	Iran	2004	only	-	Sep	21	0:00	0	S
Rule	Iran	2005	only	-	Mar	22	0:00	1:00	D
Rule	Iran	2005	only	-	Sep	22	0:00	0	S
Rule	Iran	2008	only	-	Mar	21	0:00	1:00	D
Rule	Iran	2008	only	-	Sep	21	0:00	0	S
Rule	Iran	2009	2011	-	Mar	22	0:00	1:00	D
Rule	Iran	2009	2011	-	Sep	22	0:00	0	S
Rule	Iran	2012	only	-	Mar	21	0:00	1:00	D
Rule	Iran	2012	only	-	Sep	21	0:00	0	S
Rule	Iran	2013	2015	-	Mar	22	0:00	1:00	D
Rule	Iran	2013	2015	-	Sep	22	0:00	0	S
Rule	Iran	2016	only	-	Mar	21	0:00	1:00	D
Rule	Iran	2016	only	-	Sep	21	0:00	0	S
Rule	Iran	2017	2019	-	Mar	22	0:00	1:00	D
Rule	Iran	2017	2019	-	Sep	22	0:00	0	S
Rule	Iran	2020	only	-	Mar	21	0:00	1:00	D
Rule	Iran	2020	only	-	Sep	21	0:00	0	S
Rule	Iran	2021	2023	-	Mar	22	0:00	1:00	D
Rule	Iran	2021	2023	-	Sep	22	0:00	0	S
Rule	Iran	2024	only	-	Mar	21	0:00	1:00	D
Rule	Iran	2024	only	-	Sep	21	0:00	0	S
Rule	Iran	2025	2027	-	Mar	22	0:00	1:00	D
Rule	Iran	2025	2027	-	Sep	22	0:00	0	S
Rule	Iran	2028	2029	-	Mar	21	0:00	1:00	D
Rule	Iran	2028	2029	-	Sep	21	0:00	0	S
Rule	Iran	2030	2031	-	Mar	22	0:00	1:00	D
Rule	Iran	2030	2031	-	Sep	22	0:00	0	S
Rule	Iran	2032	2033	-	Mar	21	0:00	1:00	D
Rule	Iran	2032	2033	-	Sep	21	0:00	0	S
Rule	Iran	2034	2035	-	Mar	22	0:00	1:00	D
Rule	Iran	2034	2035	-	Sep	22	0:00	0	S
Rule	Iran	2036	2037	-	Mar	21	0:00	1:00	D
Rule	Iran	2036	2037	-	Sep	21	0:00	0	S
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Tehran	3:25:44	-	LMT	1916
			3:25:44	-	TMT	1946	# Tehran Mean Time
			3:30	-	IRST	1977 Nov
			4:00	Iran	IR%sT	1979
			3:30	Iran	IR%sT


# Iraq
#
# From Jonathan Lennox (2000-06-12):
# An article in this week's Economist ("Inside the Saddam-free zone", p. 50 in
# the U.S. edition) on the Iraqi Kurds contains a paragraph:
# "The three northern provinces ... switched their clocks this spring and
# are an hour ahead of Baghdad."
#
# But Rives McDow (2000-06-18) quotes a contact in Iraqi-Kurdistan as follows:
# In the past, some Kurdish nationalists, as a protest to the Iraqi
# Government, did not adhere to daylight saving time.  They referred
# to daylight saving as Saddam time.  But, as of today, the time zone
# in Iraqi-Kurdistan is on standard time with Baghdad, Iraq.
#
# So we'll ignore the Economist's claim.

# From Steffen Thorsen (2008-03-10):
# The cabinet in Iraq abolished DST last week, according to the following
# news sources (in Arabic):
# <a href="http://www.aljeeran.net/wesima_articles/news-20080305-98602.html">
# http://www.aljeeran.net/wesima_articles/news-20080305-98602.html
# </a>
# <a href="http://www.aswataliraq.info/look/article.tpl?id=2047&IdLanguage=17&IdPublication=4&NrArticle=71743&NrIssue=1&NrSection=10">
# http://www.aswataliraq.info/look/article.tpl?id=2047&IdLanguage=17&IdPublication=4&NrArticle=71743&NrIssue=1&NrSection=10
# </a>
#
# We have published a short article in English about the change:
# <a href="http://www.timeanddate.com/news/time/iraq-dumps-daylight-saving.html">
# http://www.timeanddate.com/news/time/iraq-dumps-daylight-saving.html
# </a>

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Iraq	1982	only	-	May	1	0:00	1:00	D
Rule	Iraq	1982	1984	-	Oct	1	0:00	0	S
Rule	Iraq	1983	only	-	Mar	31	0:00	1:00	D
Rule	Iraq	1984	1985	-	Apr	1	0:00	1:00	D
Rule	Iraq	1985	1990	-	Sep	lastSun	1:00s	0	S
Rule	Iraq	1986	1990	-	Mar	lastSun	1:00s	1:00	D
# IATA SSIM (1991/1996) says Apr 1 12:01am UTC; guess the `:01' is a typo.
# Shanks & Pottenger say Iraq did not observe DST 1992/1997; ignore this.
#
Rule	Iraq	1991	2007	-	Apr	 1	3:00s	1:00	D
Rule	Iraq	1991	2007	-	Oct	 1	3:00s	0	S
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Baghdad	2:57:40	-	LMT	1890
			2:57:36	-	BMT	1918	    # Baghdad Mean Time?
			3:00	-	AST	1982 May
			3:00	Iraq	A%sT


###############################################################################

# Israel

# From Ephraim Silverberg (2001-01-11):
#
# I coined "IST/IDT" circa 1988.  Until then there were three
# different abbreviations in use:
#
# JST  Jerusalem Standard Time [Danny Braniss, Hebrew University]
# IZT  Israel Zonal (sic) Time [Prof. Haim Papo, Technion]
# EEST Eastern Europe Standard Time [used by almost everyone else]
#
# Since timezones should be called by country and not capital cities,
# I ruled out JST.  As Israel is in Asia Minor and not Eastern Europe,
# EEST was equally unacceptable.  Since "zonal" was not compatible with
# any other timezone abbreviation, I felt that 'IST' was the way to go
# and, indeed, it has received almost universal acceptance in timezone
# settings in Israeli computers.
#
# In any case, I am happy to share timezone abbreviations with India,
# high on my favorite-country list (and not only because my wife's
# family is from India).

# From Shanks & Pottenger:
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Zion	1940	only	-	Jun	 1	0:00	1:00	D
Rule	Zion	1942	1944	-	Nov	 1	0:00	0	S
Rule	Zion	1943	only	-	Apr	 1	2:00	1:00	D
Rule	Zion	1944	only	-	Apr	 1	0:00	1:00	D
Rule	Zion	1945	only	-	Apr	16	0:00	1:00	D
Rule	Zion	1945	only	-	Nov	 1	2:00	0	S
Rule	Zion	1946	only	-	Apr	16	2:00	1:00	D
Rule	Zion	1946	only	-	Nov	 1	0:00	0	S
Rule	Zion	1948	only	-	May	23	0:00	2:00	DD
Rule	Zion	1948	only	-	Sep	 1	0:00	1:00	D
Rule	Zion	1948	1949	-	Nov	 1	2:00	0	S
Rule	Zion	1949	only	-	May	 1	0:00	1:00	D
Rule	Zion	1950	only	-	Apr	16	0:00	1:00	D
Rule	Zion	1950	only	-	Sep	15	3:00	0	S
Rule	Zion	1951	only	-	Apr	 1	0:00	1:00	D
Rule	Zion	1951	only	-	Nov	11	3:00	0	S
Rule	Zion	1952	only	-	Apr	20	2:00	1:00	D
Rule	Zion	1952	only	-	Oct	19	3:00	0	S
Rule	Zion	1953	only	-	Apr	12	2:00	1:00	D
Rule	Zion	1953	only	-	Sep	13	3:00	0	S
Rule	Zion	1954	only	-	Jun	13	0:00	1:00	D
Rule	Zion	1954	only	-	Sep	12	0:00	0	S
Rule	Zion	1955	only	-	Jun	11	2:00	1:00	D
Rule	Zion	1955	only	-	Sep	11	0:00	0	S
Rule	Zion	1956	only	-	Jun	 3	0:00	1:00	D
Rule	Zion	1956	only	-	Sep	30	3:00	0	S
Rule	Zion	1957	only	-	Apr	29	2:00	1:00	D
Rule	Zion	1957	only	-	Sep	22	0:00	0	S
Rule	Zion	1974	only	-	Jul	 7	0:00	1:00	D
Rule	Zion	1974	only	-	Oct	13	0:00	0	S
Rule	Zion	1975	only	-	Apr	20	0:00	1:00	D
Rule	Zion	1975	only	-	Aug	31	0:00	0	S
Rule	Zion	1985	only	-	Apr	14	0:00	1:00	D
Rule	Zion	1985	only	-	Sep	15	0:00	0	S
Rule	Zion	1986	only	-	May	18	0:00	1:00	D
Rule	Zion	1986	only	-	Sep	 7	0:00	0	S
Rule	Zion	1987	only	-	Apr	15	0:00	1:00	D
Rule	Zion	1987	only	-	Sep	13	0:00	0	S
Rule	Zion	1988	only	-	Apr	 9	0:00	1:00	D
Rule	Zion	1988	only	-	Sep	 3	0:00	0	S

# From Ephraim Silverberg
# (1997-03-04, 1998-03-16, 1998-12-28, 2000-01-17, 2000-07-25, 2004-12-22,
# and 2005-02-17):

# According to the Office of the Secretary General of the Ministry of
# Interior, there is NO set rule for Daylight-Savings/Standard time changes.
# One thing is entrenched in law, however: that there must be at least 150
# days of daylight savings time annually.  From 1993-1998, the change to
# daylight savings time was on a Friday morning from midnight IST to
# 1 a.m IDT; up until 1998, the change back to standard time was on a
# Saturday night from midnight daylight savings time to 11 p.m. standard
# time.  1996 is an exception to this rule where the change back to standard
# time took place on Sunday night instead of Saturday night to avoid
# conflicts with the Jewish New Year.  In 1999, the change to
# daylight savings time was still on a Friday morning but from
# 2 a.m. IST to 3 a.m. IDT; furthermore, the change back to standard time
# was also on a Friday morning from 2 a.m. IDT to 1 a.m. IST for
# 1999 only.  In the year 2000, the change to daylight savings time was
# similar to 1999, but although the change back will be on a Friday, it
# will take place from 1 a.m. IDT to midnight IST.  Starting in 2001, all
# changes to/from will take place at 1 a.m. old time, but now there is no
# rule as to what day of the week it will take place in as the start date
# (except in 2003) is the night after the Passover Seder (i.e. the eve
# of the 16th of Nisan in the lunar Hebrew calendar) and the end date
# (except in 2002) is three nights before Yom Kippur [Day of Atonement]
# (the eve of the 7th of Tishrei in the lunar Hebrew calendar).

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Zion	1989	only	-	Apr	30	0:00	1:00	D
Rule	Zion	1989	only	-	Sep	 3	0:00	0	S
Rule	Zion	1990	only	-	Mar	25	0:00	1:00	D
Rule	Zion	1990	only	-	Aug	26	0:00	0	S
Rule	Zion	1991	only	-	Mar	24	0:00	1:00	D
Rule	Zion	1991	only	-	Sep	 1	0:00	0	S
Rule	Zion	1992	only	-	Mar	29	0:00	1:00	D
Rule	Zion	1992	only	-	Sep	 6	0:00	0	S
Rule	Zion	1993	only	-	Apr	 2	0:00	1:00	D
Rule	Zion	1993	only	-	Sep	 5	0:00	0	S

# The dates for 1994-1995 were obtained from Office of the Spokeswoman for the
# Ministry of Interior, Jerusalem, Israel.  The spokeswoman can be reached by
# calling the office directly at 972-2-6701447 or 972-2-6701448.

# Rule	NAME    FROM    TO      TYPE    IN      ON      AT      SAVE    LETTER/S
Rule	Zion	1994	only	-	Apr	 1	0:00	1:00	D
Rule	Zion	1994	only	-	Aug	28	0:00	0	S
Rule	Zion	1995	only	-	Mar	31	0:00	1:00	D
Rule	Zion	1995	only	-	Sep	 3	0:00	0	S

# The dates for 1996 were determined by the Minister of Interior of the
# time, Haim Ramon.  The official announcement regarding 1996-1998
# (with the dates for 1997-1998 no longer being relevant) can be viewed at:
#
#   ftp://ftp.cs.huji.ac.il/pub/tz/announcements/1996-1998.ramon.ps.gz
#
# The dates for 1997-1998 were altered by his successor, Rabbi Eli Suissa.
#
# The official announcements for the years 1997-1999 can be viewed at:
#
#   ftp://ftp.cs.huji.ac.il/pub/tz/announcements/YYYY.ps.gz
#
#       where YYYY is the relevant year.

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Zion	1996	only	-	Mar	15	0:00	1:00	D
Rule	Zion	1996	only	-	Sep	16	0:00	0	S
Rule	Zion	1997	only	-	Mar	21	0:00	1:00	D
Rule	Zion	1997	only	-	Sep	14	0:00	0	S
Rule	Zion	1998	only	-	Mar	20	0:00	1:00	D
Rule	Zion	1998	only	-	Sep	 6	0:00	0	S
Rule	Zion	1999	only	-	Apr	 2	2:00	1:00	D
Rule	Zion	1999	only	-	Sep	 3	2:00	0	S

# The Knesset Interior Committee has changed the dates for 2000 for
# the third time in just over a year and have set new dates for the
# years 2001-2004 as well.
#
# The official announcement for the start date of 2000 can be viewed at:
#
#	ftp://ftp.cs.huji.ac.il/pub/tz/announcements/2000-start.ps.gz
#
# The official announcement for the end date of 2000 and the dates
# for the years 2001-2004 can be viewed at:
#
#	ftp://ftp.cs.huji.ac.il/pub/tz/announcements/2000-2004.ps.gz

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Zion	2000	only	-	Apr	14	2:00	1:00	D
Rule	Zion	2000	only	-	Oct	 6	1:00	0	S
Rule	Zion	2001	only	-	Apr	 9	1:00	1:00	D
Rule	Zion	2001	only	-	Sep	24	1:00	0	S
Rule	Zion	2002	only	-	Mar	29	1:00	1:00	D
Rule	Zion	2002	only	-	Oct	 7	1:00	0	S
Rule	Zion	2003	only	-	Mar	28	1:00	1:00	D
Rule	Zion	2003	only	-	Oct	 3	1:00	0	S
Rule	Zion	2004	only	-	Apr	 7	1:00	1:00	D
Rule	Zion	2004	only	-	Sep	22	1:00	0	S

# The proposed law agreed upon by the Knesset Interior Committee on
# 2005-02-14 is that, for 2005 and beyond, DST starts at 02:00 the
# last Friday before April 2nd (i.e. the last Friday in March or April
# 1st itself if it falls on a Friday) and ends at 02:00 on the Saturday
# night _before_ the fast of Yom Kippur.
#
# Those who can read Hebrew can view the announcement at:
#
#	ftp://ftp.cs.huji.ac.il/pub/tz/announcements/2005+beyond.ps

# From Paul Eggert (2012-10-26):
# I used Ephraim Silverberg's dst-israel.el program
# <ftp://ftp.cs.huji.ac.il/pub/tz/software/dst-israel.el> (2005-02-20)
# along with Ed Reingold's cal-hebrew in GNU Emacs 21.4,
# to generate the transitions from 2005 through 2012.
# (I replaced "lastFri" with "Fri>=26" by hand.)
# The spring transitions all correspond to the following Rule:
#
# Rule	Zion	2005	2012	-	Mar	Fri>=26	2:00	1:00	D
#
# but older zic implementations (e.g., Solaris 8) do not support
# "Fri>=26" to mean April 1 in years like 2005, so for now we list the
# springtime transitions explicitly.

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Zion	2005	only	-	Apr	 1	2:00	1:00	D
Rule	Zion	2005	only	-	Oct	 9	2:00	0	S
Rule	Zion	2006	2010	-	Mar	Fri>=26	2:00	1:00	D
Rule	Zion	2006	only	-	Oct	 1	2:00	0	S
Rule	Zion	2007	only	-	Sep	16	2:00	0	S
Rule	Zion	2008	only	-	Oct	 5	2:00	0	S
Rule	Zion	2009	only	-	Sep	27	2:00	0	S
Rule	Zion	2010	only	-	Sep	12	2:00	0	S
Rule	Zion	2011	only	-	Apr	 1	2:00	1:00	D
Rule	Zion	2011	only	-	Oct	 2	2:00	0	S
Rule	Zion	2012	only	-	Mar	Fri>=26	2:00	1:00	D
Rule	Zion	2012	only	-	Sep	23	2:00	0	S

# From Ephraim Silverberg (2012-10-18):
# Yesterday, the Interior Ministry Committee, after more than a year
# past, approved sending the proposed June 2011 changes to the Time
# Decree Law back to the Knesset for second and third (final) votes
# before the upcoming elections on Jan. 22, 2013.  Hence, although the
# changes are not yet law, they are expected to be so before February 2013.
#
# As of 2013, DST starts at 02:00 on the Friday before the last Sunday in March.
# DST ends at 02:00 on the first Sunday after October 1, unless it occurs on the
# second day of the Jewish Rosh Hashana holiday, in which case DST ends a day
# later (i.e. at 02:00 the first Monday after October 2).
# [Rosh Hashana holidays are factored in until 2100.]

# From Ephraim Silverberg (2012-11-05):
# The Knesset passed today (in second and final readings) the amendment to the
# Time Decree Law making the changes ... law.

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Zion	2013	max	-	Mar	Fri>=23	2:00	1:00	D
Rule	Zion	2013	2026	-	Oct	Sun>=2	2:00	0	S
Rule	Zion	2027	only	-	Oct	Mon>=3	2:00	0	S
Rule	Zion	2028	max	-	Oct	Sun>=2	2:00	0	S
# The following rules are commented out for now, as they break older
# versions of zic that support only signed 32-bit timestamps, i.e.,
# through 2038-01-19 03:14:07 UTC.
#Rule	Zion	2028	2053	-	Oct	Sun>=2	2:00	0	S
#Rule	Zion	2054	only	-	Oct	Mon>=3	2:00	0	S
#Rule	Zion	2055	2080	-	Oct	Sun>=2	2:00	0	S
#Rule	Zion	2081	only	-	Oct	Mon>=3	2:00	0	S
#Rule	Zion	2082	max	-	Oct	Sun>=2	2:00	0	S

# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Jerusalem	2:20:56 -	LMT	1880
			2:20:40	-	JMT	1918	# Jerusalem Mean Time?
			2:00	Zion	I%sT



###############################################################################

# Japan

# `9:00' and `JST' is from Guy Harris.

# From Paul Eggert (1995-03-06):
# Today's _Asahi Evening News_ (page 4) reports that Japan had
# daylight saving between 1948 and 1951, but ``the system was discontinued
# because the public believed it would lead to longer working hours.''

# From Mayumi Negishi in the 2005-08-10 Japan Times
# <http://www.japantimes.co.jp/cgi-bin/getarticle.pl5?nn20050810f2.htm>:
# Occupation authorities imposed daylight-saving time on Japan on
# [1948-05-01]....  But lack of prior debate and the execution of
# daylight-saving time just three days after the bill was passed generated
# deep hatred of the concept....  The Diet unceremoniously passed a bill to
# dump the unpopular system in October 1951, less than a month after the San
# Francisco Peace Treaty was signed.  (A government poll in 1951 showed 53%
# of the Japanese wanted to scrap daylight-saving time, as opposed to 30% who
# wanted to keep it.)

# From Paul Eggert (2006-03-22):
# Shanks & Pottenger write that DST in Japan during those years was as follows:
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Japan	1948	only	-	May	Sun>=1	2:00	1:00	D
Rule	Japan	1948	1951	-	Sep	Sat>=8	2:00	0	S
Rule	Japan	1949	only	-	Apr	Sun>=1	2:00	1:00	D
Rule	Japan	1950	1951	-	May	Sun>=1	2:00	1:00	D
# but the only locations using it (for birth certificates, presumably, since
# their audience is astrologers) were US military bases.  For now, assume
# that for most purposes daylight-saving time was observed; otherwise, what
# would have been the point of the 1951 poll?

# From Hideyuki Suzuki (1998-11-09):
# 'Tokyo' usually stands for the former location of Tokyo Astronomical
# Observatory: E 139 44' 40".90 (9h 18m 58s.727), N 35 39' 16".0.
# This data is from 'Rika Nenpyou (Chronological Scientific Tables) 1996'
# edited by National Astronomical Observatory of Japan....
# JST (Japan Standard Time) has been used since 1888-01-01 00:00 (JST).
# The law is enacted on 1886-07-07.

# From Hideyuki Suzuki (1998-11-16):
# The ordinance No. 51 (1886) established "standard time" in Japan,
# which stands for the time on E 135 degree.
# In the ordinance No. 167 (1895), "standard time" was renamed to "central
# standard time".  And the same ordinance also established "western standard
# time", which stands for the time on E 120 degree....  But "western standard
# time" was abolished in the ordinance No. 529 (1937).  In the ordinance No.
# 167, there is no mention regarding for what place western standard time is
# standard....
#
# I wrote "ordinance" above, but I don't know how to translate.
# In Japanese it's "chokurei", which means ordinance from emperor.

# Shanks & Pottenger claim JST in use since 1896, and that a few
# places (e.g. Ishigaki) use +0800; go with Suzuki.  Guess that all
# ordinances took effect on Jan 1.

# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Tokyo	9:18:59	-	LMT	1887 Dec 31 15:00u
			9:00	-	JST	1896
			9:00	-	CJT	1938
			9:00	Japan	J%sT
# Since 1938, all Japanese possessions have been like Asia/Tokyo.

# Jordan
#
# From <a href="http://star.arabia.com/990701/JO9.html">
# Jordan Week (1999-07-01) </a> via Steffen Thorsen (1999-09-09):
# Clocks in Jordan were forwarded one hour on Wednesday at midnight,
# in accordance with the government's decision to implement summer time
# all year round.
#
# From <a href="http://star.arabia.com/990930/JO9.html">
# Jordan Week (1999-09-30) </a> via Steffen Thorsen (1999-11-09):
# Winter time starts today Thursday, 30 September. Clocks will be turned back
# by one hour.  This is the latest government decision and it's final!
# The decision was taken because of the increase in working hours in
# government's departments from six to seven hours.
#
# From Paul Eggert (2005-11-22):
# Starting 2003 transitions are from Steffen Thorsen's web site timeanddate.com.
#
# From Steffen Thorsen (2005-11-23):
# For Jordan I have received multiple independent user reports every year
# about DST end dates, as the end-rule is different every year.
#
# From Steffen Thorsen (2006-10-01), after a heads-up from Hilal Malawi:
# http://www.petranews.gov.jo/nepras/2006/Sep/05/4000.htm
# "Jordan will switch to winter time on Friday, October 27".
#

# From Phil Pizzey (2009-04-02):
# ...I think I may have spotted an error in the timezone data for
# Jordan.
# The current (2009d) asia file shows Jordan going to daylight
# saving
# time on the last Thursday in March.
#
# Rule  Jordan      2000  max	-  Mar   lastThu     0:00s 1:00  S
#
# However timeanddate.com, which I usually find reliable, shows Jordan
# going to daylight saving time on the last Friday in March since 2002.
# Please see
# <a href="http://www.timeanddate.com/worldclock/timezone.html?n=11">
# http://www.timeanddate.com/worldclock/timezone.html?n=11
# </a>

# From Steffen Thorsen (2009-04-02):
# This single one might be good enough, (2009-03-24, Arabic):
# <a href="http://petra.gov.jo/Artical.aspx?Lng=2&Section=8&Artical=95279">
# http://petra.gov.jo/Artical.aspx?Lng=2&Section=8&Artical=95279
# </a>
#
# Google's translation:
#
# > The Council of Ministers decided in 2002 to adopt the principle of timely
# > submission of the summer at 60 minutes as of midnight on the last Thursday
# > of the month of March of each year.
#
# So - this means the midnight between Thursday and Friday since 2002.

# From Arthur David Olson (2009-04-06):
# We still have Jordan switching to DST on Thursdays in 2000 and 2001.

# From Steffen Thorsen (2012-10-25):
# Yesterday the government in Jordan announced that they will not
# switch back to standard time this winter, so the will stay on DST
# until about the same time next year (at least).
# http://www.petra.gov.jo/Public_News/Nws_NewsDetails.aspx?NewsID=88950
#
# From Paul Eggert (2012-10-25):
# For now, assume this is just a one-year measure.  If it becomes
# permanent, we should move Jordan from EET to AST effective tomorrow.

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Jordan	1973	only	-	Jun	6	0:00	1:00	S
Rule	Jordan	1973	1975	-	Oct	1	0:00	0	-
Rule	Jordan	1974	1977	-	May	1	0:00	1:00	S
Rule	Jordan	1976	only	-	Nov	1	0:00	0	-
Rule	Jordan	1977	only	-	Oct	1	0:00	0	-
Rule	Jordan	1978	only	-	Apr	30	0:00	1:00	S
Rule	Jordan	1978	only	-	Sep	30	0:00	0	-
Rule	Jordan	1985	only	-	Apr	1	0:00	1:00	S
Rule	Jordan	1985	only	-	Oct	1	0:00	0	-
Rule	Jordan	1986	1988	-	Apr	Fri>=1	0:00	1:00	S
Rule	Jordan	1986	1990	-	Oct	Fri>=1	0:00	0	-
Rule	Jordan	1989	only	-	May	8	0:00	1:00	S
Rule	Jordan	1990	only	-	Apr	27	0:00	1:00	S
Rule	Jordan	1991	only	-	Apr	17	0:00	1:00	S
Rule	Jordan	1991	only	-	Sep	27	0:00	0	-
Rule	Jordan	1992	only	-	Apr	10	0:00	1:00	S
Rule	Jordan	1992	1993	-	Oct	Fri>=1	0:00	0	-
Rule	Jordan	1993	1998	-	Apr	Fri>=1	0:00	1:00	S
Rule	Jordan	1994	only	-	Sep	Fri>=15	0:00	0	-
Rule	Jordan	1995	1998	-	Sep	Fri>=15	0:00s	0	-
Rule	Jordan	1999	only	-	Jul	 1	0:00s	1:00	S
Rule	Jordan	1999	2002	-	Sep	lastFri	0:00s	0	-
Rule	Jordan	2000	2001	-	Mar	lastThu	0:00s	1:00	S
Rule	Jordan	2002	max	-	Mar	lastThu	24:00	1:00	S
Rule	Jordan	2003	only	-	Oct	24	0:00s	0	-
Rule	Jordan	2004	only	-	Oct	15	0:00s	0	-
Rule	Jordan	2005	only	-	Sep	lastFri	0:00s	0	-
Rule	Jordan	2006	2011	-	Oct	lastFri	0:00s	0	-
Rule	Jordan	2013	max	-	Oct	lastFri	0:00s	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Amman	2:23:44 -	LMT	1931
			2:00	Jordan	EE%sT


# Kazakhstan

# From Paul Eggert (1996-11-22):
# Andrew Evtichov (1996-04-13) writes that Kazakhstan
# stayed in sync with Moscow after 1990, and that Aqtobe (formerly Aktyubinsk)
# and Aqtau (formerly Shevchenko) are the largest cities in their zones.
# Guess that Aqtau and Aqtobe diverged in 1995, since that's the first time
# IATA SSIM mentions a third time zone in Kazakhstan.

# From Paul Eggert (2006-03-22):
# German Iofis, ELSI, Almaty (2001-10-09) reports that Kazakhstan uses
# RussiaAsia rules, instead of switching at 00:00 as the IATA has it.
# Go with Shanks & Pottenger, who have them always using RussiaAsia rules.
# Also go with the following claims of Shanks & Pottenger:
#
# - Kazakhstan did not observe DST in 1991.
# - Qyzylorda switched from +5:00 to +6:00 on 1992-01-19 02:00.
# - Oral switched from +5:00 to +4:00 in spring 1989.

# <a href="http://www.kazsociety.org.uk/news/2005/03/30.htm">
# From Kazakhstan Embassy's News Bulletin #11 (2005-03-21):
# </a>
# The Government of Kazakhstan passed a resolution March 15 abolishing
# daylight saving time citing lack of economic benefits and health
# complications coupled with a decrease in productivity.
#
# From Branislav Kojic (in Astana) via Gwillim Law (2005-06-28):
# ... what happened was that the former Kazakhstan Eastern time zone
# was "blended" with the Central zone.  Therefore, Kazakhstan now has
# two time zones, and difference between them is one hour.  The zone
# closer to UTC is the former Western zone (probably still called the
# same), encompassing four provinces in the west: Aqtobe, Atyrau,
# Mangghystau, and West Kazakhstan.  The other zone encompasses
# everything else....  I guess that would make Kazakhstan time zones
# de jure UTC+5 and UTC+6 respectively.

#
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
#
# Almaty (formerly Alma-Ata), representing most locations in Kazakhstan
Zone	Asia/Almaty	5:07:48 -	LMT	1924 May  2 # or Alma-Ata
			5:00	-	ALMT	1930 Jun 21 # Alma-Ata Time
			6:00 RussiaAsia ALM%sT	1991
			6:00	-	ALMT	1992
			6:00 RussiaAsia	ALM%sT	2005 Mar 15
			6:00	-	ALMT
# Qyzylorda (aka Kyzylorda, Kizilorda, Kzyl-Orda, etc.)
Zone	Asia/Qyzylorda	4:21:52 -	LMT	1924 May  2
			4:00	-	KIZT	1930 Jun 21 # Kizilorda Time
			5:00	-	KIZT	1981 Apr  1
			5:00	1:00	KIZST	1981 Oct  1
			6:00	-	KIZT	1982 Apr  1
			5:00 RussiaAsia	KIZ%sT	1991
			5:00	-	KIZT	1991 Dec 16 # independence
			5:00	-	QYZT	1992 Jan 19 2:00
			6:00 RussiaAsia	QYZ%sT	2005 Mar 15
			6:00	-	QYZT
# Aqtobe (aka Aktobe, formerly Akt'ubinsk)
Zone	Asia/Aqtobe	3:48:40	-	LMT	1924 May  2
			4:00	-	AKTT	1930 Jun 21 # Aktyubinsk Time
			5:00	-	AKTT	1981 Apr  1
			5:00	1:00	AKTST	1981 Oct  1
			6:00	-	AKTT	1982 Apr  1
			5:00 RussiaAsia	AKT%sT	1991
			5:00	-	AKTT	1991 Dec 16 # independence
			5:00 RussiaAsia	AQT%sT	2005 Mar 15 # Aqtobe Time
			5:00	-	AQTT
# Mangghystau
# Aqtau was not founded until 1963, but it represents an inhabited region,
# so include time stamps before 1963.
Zone	Asia/Aqtau	3:21:04	-	LMT	1924 May  2
			4:00	-	FORT	1930 Jun 21 # Fort Shevchenko T
			5:00	-	FORT	1963
			5:00	-	SHET	1981 Oct  1 # Shevchenko Time
			6:00	-	SHET	1982 Apr  1
			5:00 RussiaAsia	SHE%sT	1991
			5:00	-	SHET	1991 Dec 16 # independence
			5:00 RussiaAsia	AQT%sT	1995 Mar lastSun 2:00 # Aqtau Time
			4:00 RussiaAsia	AQT%sT	2005 Mar 15
			5:00	-	AQTT
# West Kazakhstan
Zone	Asia/Oral	3:25:24	-	LMT	1924 May  2 # or Ural'sk
			4:00	-	URAT	1930 Jun 21 # Ural'sk time
			5:00	-	URAT	1981 Apr  1
			5:00	1:00	URAST	1981 Oct  1
			6:00	-	URAT	1982 Apr  1
			5:00 RussiaAsia	URA%sT	1989 Mar 26 2:00
			4:00 RussiaAsia	URA%sT	1991
			4:00	-	URAT	1991 Dec 16 # independence
			4:00 RussiaAsia	ORA%sT	2005 Mar 15 # Oral Time
			5:00	-	ORAT

# Kyrgyzstan (Kirgizstan)
# Transitions through 1991 are from Shanks & Pottenger.

# From Paul Eggert (2005-08-15):
# According to an article dated today in the Kyrgyzstan Development Gateway
# <http://eng.gateway.kg/cgi-bin/page.pl?id=1&story_name=doc9979.shtml>
# Kyrgyzstan is canceling the daylight saving time system.  I take the article
# to mean that they will leave their clocks at 6 hours ahead of UTC.
# From Malik Abdugaliev (2005-09-21):
# Our government cancels daylight saving time 6th of August 2005.
# From 2005-08-12 our GMT-offset is +6, w/o any daylight saving.

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Kyrgyz	1992	1996	-	Apr	Sun>=7	0:00s	1:00	S
Rule	Kyrgyz	1992	1996	-	Sep	lastSun	0:00	0	-
Rule	Kyrgyz	1997	2005	-	Mar	lastSun	2:30	1:00	S
Rule	Kyrgyz	1997	2004	-	Oct	lastSun	2:30	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Bishkek	4:58:24 -	LMT	1924 May  2
			5:00	-	FRUT	1930 Jun 21 # Frunze Time
			6:00 RussiaAsia FRU%sT	1991 Mar 31 2:00s
			5:00	1:00	FRUST	1991 Aug 31 2:00 # independence
			5:00	Kyrgyz	KG%sT	2005 Aug 12    # Kyrgyzstan Time
			6:00	-	KGT

###############################################################################

# Korea (North and South)

# From Annie I. Bang (2006-07-10) in
# <http://www.koreaherald.co.kr/SITE/data/html_dir/2006/07/10/200607100012.asp>:
# The Ministry of Commerce, Industry and Energy has already
# commissioned a research project [to reintroduce DST] and has said
# the system may begin as early as 2008....  Korea ran a daylight
# saving program from 1949-61 but stopped it during the 1950-53 Korean War.

# From Shanks & Pottenger:
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	ROK	1960	only	-	May	15	0:00	1:00	D
Rule	ROK	1960	only	-	Sep	13	0:00	0	S
Rule	ROK	1987	1988	-	May	Sun>=8	0:00	1:00	D
Rule	ROK	1987	1988	-	Oct	Sun>=8	0:00	0	S

# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Seoul	8:27:52	-	LMT	1890
			8:30	-	KST	1904 Dec
			9:00	-	KST	1928
			8:30	-	KST	1932
			9:00	-	KST	1954 Mar 21
			8:00	ROK	K%sT	1961 Aug 10
			8:30	-	KST	1968 Oct
			9:00	ROK	K%sT
Zone	Asia/Pyongyang	8:23:00 -	LMT	1890
			8:30	-	KST	1904 Dec
			9:00	-	KST	1928
			8:30	-	KST	1932
			9:00	-	KST	1954 Mar 21
			8:00	-	KST	1961 Aug 10
			9:00	-	KST

###############################################################################

# Kuwait
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
# From the Arab Times (2007-03-14):
# The Civil Service Commission (CSC) has approved a proposal forwarded
# by MP Ahmad Baqer on implementing the daylight saving time (DST) in
# Kuwait starting from April until the end of Sept this year, reports Al-Anba.
# <http://www.arabtimesonline.com/arabtimes/kuwait/Viewdet.asp?ID=9950>.
# From Paul Eggert (2007-03-29):
# We don't know the details, or whether the approval means it'll happen,
# so for now we assume no DST.
Zone	Asia/Kuwait	3:11:56 -	LMT	1950
			3:00	-	AST

# Laos
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Vientiane	6:50:24 -	LMT	1906 Jun  9 # or Viangchan
			7:06:20	-	SMT	1911 Mar 11 0:01 # Saigon MT?
			7:00	-	ICT	1912 May
			8:00	-	ICT	1931 May
			7:00	-	ICT

# Lebanon
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Lebanon	1920	only	-	Mar	28	0:00	1:00	S
Rule	Lebanon	1920	only	-	Oct	25	0:00	0	-
Rule	Lebanon	1921	only	-	Apr	3	0:00	1:00	S
Rule	Lebanon	1921	only	-	Oct	3	0:00	0	-
Rule	Lebanon	1922	only	-	Mar	26	0:00	1:00	S
Rule	Lebanon	1922	only	-	Oct	8	0:00	0	-
Rule	Lebanon	1923	only	-	Apr	22	0:00	1:00	S
Rule	Lebanon	1923	only	-	Sep	16	0:00	0	-
Rule	Lebanon	1957	1961	-	May	1	0:00	1:00	S
Rule	Lebanon	1957	1961	-	Oct	1	0:00	0	-
Rule	Lebanon	1972	only	-	Jun	22	0:00	1:00	S
Rule	Lebanon	1972	1977	-	Oct	1	0:00	0	-
Rule	Lebanon	1973	1977	-	May	1	0:00	1:00	S
Rule	Lebanon	1978	only	-	Apr	30	0:00	1:00	S
Rule	Lebanon	1978	only	-	Sep	30	0:00	0	-
Rule	Lebanon	1984	1987	-	May	1	0:00	1:00	S
Rule	Lebanon	1984	1991	-	Oct	16	0:00	0	-
Rule	Lebanon	1988	only	-	Jun	1	0:00	1:00	S
Rule	Lebanon	1989	only	-	May	10	0:00	1:00	S
Rule	Lebanon	1990	1992	-	May	1	0:00	1:00	S
Rule	Lebanon	1992	only	-	Oct	4	0:00	0	-
Rule	Lebanon	1993	max	-	Mar	lastSun	0:00	1:00	S
Rule	Lebanon	1993	1998	-	Sep	lastSun	0:00	0	-
Rule	Lebanon	1999	max	-	Oct	lastSun	0:00	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Beirut	2:22:00 -	LMT	1880
			2:00	Lebanon	EE%sT

# Malaysia
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	NBorneo	1935	1941	-	Sep	14	0:00	0:20	TS # one-Third Summer
Rule	NBorneo	1935	1941	-	Dec	14	0:00	0	-
#
# peninsular Malaysia
# The data here are taken from Mok Ly Yng (2003-10-30)
# <http://www.math.nus.edu.sg/aslaksen/teaching/timezone.html>.
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone Asia/Kuala_Lumpur	6:46:46 -	LMT	1901 Jan  1
			6:55:25	-	SMT	1905 Jun  1 # Singapore M.T.
			7:00	-	MALT	1933 Jan  1 # Malaya Time
			7:00	0:20	MALST	1936 Jan  1
			7:20	-	MALT	1941 Sep  1
			7:30	-	MALT	1942 Feb 16
			9:00	-	JST	1945 Sep 12
			7:30	-	MALT	1982 Jan  1
			8:00	-	MYT	# Malaysia Time
# Sabah & Sarawak
# From Paul Eggert (2006-03-22):
# The data here are mostly from Shanks & Pottenger, but the 1942, 1945 and 1982
# transition dates are from Mok Ly Yng.
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone Asia/Kuching	7:21:20	-	LMT	1926 Mar
			7:30	-	BORT	1933	# Borneo Time
			8:00	NBorneo	BOR%sT	1942 Feb 16
			9:00	-	JST	1945 Sep 12
			8:00	-	BORT	1982 Jan  1
			8:00	-	MYT

# Maldives
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Indian/Maldives	4:54:00 -	LMT	1880	# Male
			4:54:00	-	MMT	1960	# Male Mean Time
			5:00	-	MVT		# Maldives Time

# Mongolia

# Shanks & Pottenger say that Mongolia has three time zones, but
# usno1995 and the CIA map Standard Time Zones of the World (2005-03)
# both say that it has just one.

# From Oscar van Vlijmen (1999-12-11):
# <a href="http://www.mongoliatourism.gov.mn/general.htm">
# General Information Mongolia
# </a> (1999-09)
# "Time: Mongolia has two time zones. Three westernmost provinces of
# Bayan-Ulgii, Uvs, and Hovd are one hour earlier than the capital city, and
# the rest of the country follows the Ulaanbaatar time, which is UTC/GMT plus
# eight hours."

# From Rives McDow (1999-12-13):
# Mongolia discontinued the use of daylight savings time in 1999; 1998
# being the last year it was implemented.  The dates of implementation I am
# unsure of, but most probably it was similar to Russia, except for the time
# of implementation may have been different....
# Some maps in the past have indicated that there was an additional time
# zone in the eastern part of Mongolia, including the provinces of Dornod,
# Suhbaatar, and possibly Khentij.

# From Paul Eggert (1999-12-15):
# Naming and spelling is tricky in Mongolia.
# We'll use Hovd (also spelled Chovd and Khovd) to represent the west zone;
# the capital of the Hovd province is sometimes called Hovd, sometimes Dund-Us,
# and sometimes Jirgalanta (with variant spellings), but the name Hovd
# is good enough for our purposes.

# From Rives McDow (2001-05-13):
# In addition to Mongolia starting daylight savings as reported earlier
# (adopted DST on 2001-04-27 02:00 local time, ending 2001-09-28),
# there are three time zones.
#
# Provinces [at 7:00]: Bayan-ulgii, Uvs, Khovd, Zavkhan, Govi-Altai
# Provinces [at 8:00]: Khovsgol, Bulgan, Arkhangai, Khentii, Tov,
#	Bayankhongor, Ovorkhangai, Dundgovi, Dornogovi, Omnogovi
# Provinces [at 9:00]: Dornod, Sukhbaatar
#
# [The province of Selenge is omitted from the above lists.]

# From Ganbold Ts., Ulaanbaatar (2004-04-17):
# Daylight saving occurs at 02:00 local time last Saturday of March.
# It will change back to normal at 02:00 local time last Saturday of
# September.... As I remember this rule was changed in 2001.
#
# From Paul Eggert (2004-04-17):
# For now, assume Rives McDow's informant got confused about Friday vs
# Saturday, and that his 2001 dates should have 1 added to them.

# From Paul Eggert (2005-07-26):
# We have wildly conflicting information about Mongolia's time zones.
# Bill Bonnet (2005-05-19) reports that the US Embassy in Ulaanbaatar says
# there is only one time zone and that DST is observed, citing Microsoft
# Windows XP as the source.  Risto Nykanen (2005-05-16) reports that
# travelmongolia.org says there are two time zones (UTC+7, UTC+8) with no DST.
# Oscar van Vlijmen (2005-05-20) reports that the Mongolian Embassy in
# Washington, DC says there are two time zones, with DST observed.
# He also found
# <http://ubpost.mongolnews.mn/index.php?subaction=showcomments&id=1111634894&archive=&start_from=&ucat=1&>
# which also says that there is DST, and which has a comment by "Toddius"
# (2005-03-31 06:05 +0700) saying "Mongolia actually has 3.5 time zones.
# The West (OLGII) is +7 GMT, most of the country is ULAT is +8 GMT
# and some Eastern provinces are +9 GMT but Sukhbaatar Aimag is SUHK +8.5 GMT.
# The SUKH timezone is new this year, it is one of the few things the
# parliament passed during the tumultuous winter session."
# For now, let's ignore this information, until we have more confirmation.

# From Ganbold Ts. (2007-02-26):
# Parliament of Mongolia has just changed the daylight-saving rule in February.
# They decided not to adopt daylight-saving time....
# http://www.mongolnews.mn/index.php?module=unuudur&sec=view&id=15742

# From Deborah Goldsmith (2008-03-30):
# We received a bug report claiming that the tz database UTC offset for
# Asia/Choibalsan (GMT+09:00) is incorrect, and that it should be GMT
# +08:00 instead. Different sources appear to disagree with the tz
# database on this, e.g.:
#
# <a href="http://www.timeanddate.com/worldclock/city.html?n=1026">
# http://www.timeanddate.com/worldclock/city.html?n=1026
# </a>
# <a href="http://www.worldtimeserver.com/current_time_in_MN.aspx">
# http://www.worldtimeserver.com/current_time_in_MN.aspx
# </a>
#
# both say GMT+08:00.

# From Steffen Thorsen (2008-03-31):
# eznis airways, which operates several domestic flights, has a flight
# schedule here:
# <a href="http://www.eznis.com/Container.jsp?id=112">
# http://www.eznis.com/Container.jsp?id=112
# </a>
# (click the English flag for English)
#
# There it appears that flights between Choibalsan and Ulaanbatar arrive
# about 1:35 - 1:50 hours later in local clock time, no matter the
# direction, while Ulaanbaatar-Khvod takes 2 hours in the Eastern
# direction and 3:35 back, which indicates that Ulaanbatar and Khvod are
# in different time zones (like we know about), while Choibalsan and
# Ulaanbatar are in the same time zone (correction needed).

# From Arthur David Olson (2008-05-19):
# Assume that Choibalsan is indeed offset by 8:00.
# XXX--in the absence of better information, assume that transition
# was at the start of 2008-03-31 (the day of Steffen Thorsen's report);
# this is almost surely wrong.

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Mongol	1983	1984	-	Apr	1	0:00	1:00	S
Rule	Mongol	1983	only	-	Oct	1	0:00	0	-
# Shanks & Pottenger and IATA SSIM say 1990s switches occurred at 00:00,
# but McDow says the 2001 switches occurred at 02:00.  Also, IATA SSIM
# (1996-09) says 1996-10-25.  Go with Shanks & Pottenger through 1998.
#
# Shanks & Pottenger say that the Sept. 1984 through Sept. 1990 switches
# in Choibalsan (more precisely, in Dornod and Sukhbaatar) took place
# at 02:00 standard time, not at 00:00 local time as in the rest of
# the country.  That would be odd, and possibly is a result of their
# correction of 02:00 (in the previous edition) not being done correctly
# in the latest edition; so ignore it for now.

Rule	Mongol	1985	1998	-	Mar	lastSun	0:00	1:00	S
Rule	Mongol	1984	1998	-	Sep	lastSun	0:00	0	-
# IATA SSIM (1999-09) says Mongolia no longer observes DST.
Rule	Mongol	2001	only	-	Apr	lastSat	2:00	1:00	S
Rule	Mongol	2001	2006	-	Sep	lastSat	2:00	0	-
Rule	Mongol	2002	2006	-	Mar	lastSat	2:00	1:00	S

# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
# Hovd, a.k.a. Chovd, Dund-Us, Dzhargalant, Khovd, Jirgalanta
Zone	Asia/Hovd	6:06:36 -	LMT	1905 Aug
			6:00	-	HOVT	1978	# Hovd Time
			7:00	Mongol	HOV%sT
# Ulaanbaatar, a.k.a. Ulan Bataar, Ulan Bator, Urga
Zone	Asia/Ulaanbaatar 7:07:32 -	LMT	1905 Aug
			7:00	-	ULAT	1978	# Ulaanbaatar Time
			8:00	Mongol	ULA%sT
# Choibalsan, a.k.a. Bajan Tuemen, Bajan Tumen, Chojbalsan,
# Choybalsan, Sanbejse, Tchoibalsan
Zone	Asia/Choibalsan	7:38:00 -	LMT	1905 Aug
			7:00	-	ULAT	1978
			8:00	-	ULAT	1983 Apr
			9:00	Mongol	CHO%sT	2008 Mar 31 # Choibalsan Time
			8:00	Mongol	CHO%sT

# Nepal
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Kathmandu	5:41:16 -	LMT	1920
			5:30	-	IST	1986
			5:45	-	NPT	# Nepal Time

# Oman
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Muscat	3:54:20 -	LMT	1920
			4:00	-	GST

# Pakistan

# From Rives McDow (2002-03-13):
# I have been advised that Pakistan has decided to adopt dst on a
# TRIAL basis for one year, starting 00:01 local time on April 7, 2002
# and ending at 00:01 local time October 6, 2002.  This is what I was
# told, but I believe that the actual time of change may be 00:00; the
# 00:01 was to make it clear which day it was on.

# From Paul Eggert (2002-03-15):
# Jesper Norgaard found this URL:
# http://www.pak.gov.pk/public/news/app/app06_dec.htm
# (dated 2001-12-06) which says that the Cabinet adopted a scheme "to
# advance the clocks by one hour on the night between the first
# Saturday and Sunday of April and revert to the original position on
# 15th October each year".  This agrees with McDow's 04-07 at 00:00,
# but disagrees about the October transition, and makes it sound like
# it's not on a trial basis.  Also, the "between the first Saturday
# and Sunday of April" phrase, if taken literally, means that the
# transition takes place at 00:00 on the first Sunday on or after 04-02.

# From Paul Eggert (2003-02-09):
# DAWN <http://www.dawn.com/2002/10/06/top13.htm> reported on 2002-10-05
# that 2002 DST ended that day at midnight.  Go with McDow for now.

# From Steffen Thorsen (2003-03-14):
# According to http://www.dawn.com/2003/03/07/top15.htm
# there will be no DST in Pakistan this year:
#
# ISLAMABAD, March 6: Information and Media Development Minister Sheikh
# Rashid Ahmed on Thursday said the cabinet had reversed a previous
# decision to advance clocks by one hour in summer and put them back by
# one hour in winter with the aim of saving light hours and energy.
#
# The minister told a news conference that the experiment had rather
# shown 8 per cent higher consumption of electricity.

# From Alex Krivenyshev (2008-05-15):
#
# Here is an article that Pakistan plan to introduce Daylight Saving Time
# on June 1, 2008 for 3 months.
#
# "... The federal cabinet on Wednesday announced a new conservation plan to help
# reduce load shedding by approving the closure of commercial centres at 9pm and
# moving clocks forward by one hour for the next three months.
# ...."
#
# <a href="http://www.worldtimezone.net/dst_news/dst_news_pakistan01.html">
# http://www.worldtimezone.net/dst_news/dst_news_pakistan01.html
# </a>
# OR
# <a href="http://www.dailytimes.com.pk/default.asp?page=2008%5C05%5C15%5Cstory_15-5-2008_pg1_4">
# http://www.dailytimes.com.pk/default.asp?page=2008%5C05%5C15%5Cstory_15-5-2008_pg1_4
# </a>

# From Arthur David Olson (2008-05-19):
# XXX--midnight transitions is a guess; 2008 only is a guess.

# From Alexander Krivenyshev (2008-08-28):
# Pakistan government has decided to keep the watches one-hour advanced
# for another 2 months--plan to return to Standard Time on October 31
# instead of August 31.
#
# <a href="http://www.worldtimezone.com/dst_news/dst_news_pakistan02.html">
# http://www.worldtimezone.com/dst_news/dst_news_pakistan02.html
# </a>
# OR
# <a href="http://dailymailnews.com/200808/28/news/dmbrn03.html">
# http://dailymailnews.com/200808/28/news/dmbrn03.html
# </a>

# From Alexander Krivenyshev (2009-04-08):
# Based on previous media reports that "... proposed plan to
# advance clocks by one hour from May 1 will cause disturbance
# to the working schedules rather than bringing discipline in
# official working."
# <a href="http://www.thenews.com.pk/daily_detail.asp?id=171280">
# http://www.thenews.com.pk/daily_detail.asp?id=171280
# </a>
#
# recent news that instead of May 2009 - Pakistan plan to
# introduce DST from April 15, 2009
#
# FYI: Associated Press Of Pakistan
# April 08, 2009
# Cabinet okays proposal to advance clocks by one hour from April 15
# <a href="http://www.app.com.pk/en_/index.php?option=com_content&task=view&id=73043&Itemid=1">
# http://www.app.com.pk/en_/index.php?option=com_content&task=view&id=73043&Itemid=1
# </a>
#
# or
#
# <a href="http://www.worldtimezone.com/dst_news/dst_news_pakistan05.html">
# http://www.worldtimezone.com/dst_news/dst_news_pakistan05.html
# </a>
#
# ....
# The Federal Cabinet on Wednesday approved the proposal to
# advance clocks in the country by one hour from April 15 to
# conserve energy"

# From Steffen Thorsen (2009-09-17):
# "The News International," Pakistan reports that: "The Federal
# Government has decided to restore the previous time by moving the
# clocks backward by one hour from October 1. A formal announcement to
# this effect will be made after the Prime Minister grants approval in
# this regard."
# <a href="http://www.thenews.com.pk/updates.asp?id=87168">
# http://www.thenews.com.pk/updates.asp?id=87168
# </a>

# From Alexander Krivenyshev (2009-09-28):
# According to Associated Press Of Pakistan, it is confirmed that
# Pakistan clocks across the country would be turned back by an hour from October
# 1, 2009.
#
# "Clocks to go back one hour from 1 Oct"
# <a href="http://www.app.com.pk/en_/index.php?option=com_content&task=view&id=86715&Itemid=2">
# http://www.app.com.pk/en_/index.php?option=com_content&task=view&id=86715&Itemid=2
# </a>
# or
# <a href="http://www.worldtimezone.com/dst_news/dst_news_pakistan07.htm">
# http://www.worldtimezone.com/dst_news/dst_news_pakistan07.htm
# </a>

# From Steffen Thorsen (2009-09-29):
# Alexander Krivenyshev wrote:
# > According to Associated Press Of Pakistan, it is confirmed that
# > Pakistan clocks across the country would be turned back by an hour from October
# > 1, 2009.
#
# Now they seem to have changed their mind, November 1 is the new date:
# <a href="http://www.thenews.com.pk/top_story_detail.asp?Id=24742">
# http://www.thenews.com.pk/top_story_detail.asp?Id=24742
# </a>
# "The country's clocks will be reversed by one hour on November 1.
# Officials of Federal Ministry for Interior told this to Geo News on
# Monday."
#
# And more importantly, it seems that these dates will be kept every year:
# "It has now been decided that clocks will be wound forward by one hour
# on April 15 and reversed by an hour on November 1 every year without
# obtaining prior approval, the officials added."
#
# We have confirmed this year's end date with both with the Ministry of
# Water and Power and the Pakistan Electric Power Company:
# <a href="http://www.timeanddate.com/news/time/pakistan-ends-dst09.html">
# http://www.timeanddate.com/news/time/pakistan-ends-dst09.html
# </a>

# From Christoph Goehre (2009-10-01):
# [T]he German Consulate General in Karachi reported me today that Pakistan
# will go back to standard time on 1st of November.

# From Steffen Thorsen (2010-03-26):
# Steffen Thorsen wrote:
# > On Thursday (2010-03-25) it was announced that DST would start in
# > Pakistan on 2010-04-01.
# >
# > Then today, the president said that they might have to revert the
# > decision if it is not supported by the parliament. So at the time
# > being, it seems unclear if DST will be actually observed or not - but
# > April 1 could be a more likely date than April 15.
# Now, it seems that the decision to not observe DST in final:
#
# "Govt Withdraws Plan To Advance Clocks"
# <a href="http://www.apakistannews.com/govt-withdraws-plan-to-advance-clocks-172041">
# http://www.apakistannews.com/govt-withdraws-plan-to-advance-clocks-172041
# </a>
#
# "People laud PM's announcement to end DST"
# <a href="http://www.app.com.pk/en_/index.php?option=com_content&task=view&id=99374&Itemid=2">
# http://www.app.com.pk/en_/index.php?option=com_content&task=view&id=99374&Itemid=2
# </a>

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule Pakistan	2002	only	-	Apr	Sun>=2	0:01	1:00	S
Rule Pakistan	2002	only	-	Oct	Sun>=2	0:01	0	-
Rule Pakistan	2008	only	-	Jun	1	0:00	1:00	S
Rule Pakistan	2008	only	-	Nov	1	0:00	0	-
Rule Pakistan	2009	only	-	Apr	15	0:00	1:00	S
Rule Pakistan	2009	only	-	Nov	1	0:00	0	-

# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Karachi	4:28:12 -	LMT	1907
			5:30	-	IST	1942 Sep
			5:30	1:00	IST	1945 Oct 15
			5:30	-	IST	1951 Sep 30
			5:00	-	KART	1971 Mar 26 # Karachi Time
			5:00 Pakistan	PK%sT	# Pakistan Time

# Palestine

# From Amos Shapir (1998-02-15):
#
# From 1917 until 1948-05-15, all of Palestine, including the parts now
# known as the Gaza Strip and the West Bank, was under British rule.
# Therefore the rules given for Israel for that period, apply there too...
#
# The Gaza Strip was under Egyptian rule between 1948-05-15 until 1967-06-05
# (except a short occupation by Israel from 1956-11 till 1957-03, but no
# time zone was affected then).  It was never formally annexed to Egypt,
# though.
#
# The rest of Palestine was under Jordanian rule at that time, formally
# annexed in 1950 as the West Bank (and the word "Trans" was dropped from
# the country's previous name of "the Hashemite Kingdom of the
# Trans-Jordan").  So the rules for Jordan for that time apply.  Major
# towns in that area are Nablus (Shchem), El-Halil (Hebron), Ramallah, and
# East Jerusalem.
#
# Both areas were occupied by Israel in June 1967, but not annexed (except
# for East Jerusalem).  They were on Israel time since then; there might
# have been a Military Governor's order about time zones, but I'm not aware
# of any (such orders may have been issued semi-annually whenever summer
# time was in effect, but maybe the legal aspect of time was just neglected).
#
# The Palestinian Authority was established in 1993, and got hold of most
# towns in the West Bank and Gaza by 1995.  I know that in order to
# demonstrate...independence, they have been switching to
# summer time and back on a different schedule than Israel's, but I don't
# know when this was started, or what algorithm is used (most likely the
# Jordanian one).
#
# To summarize, the table should probably look something like that:
#
# Area \ when | 1918-1947 | 1948-1967 | 1967-1995 | 1996-
# ------------+-----------+-----------+-----------+-----------
# Israel      | Zion      | Zion      | Zion      | Zion
# West bank   | Zion      | Jordan    | Zion      | Jordan
# Gaza        | Zion      | Egypt     | Zion      | Jordan
#
# I guess more info may be available from the PA's web page (if/when they
# have one).

# From Paul Eggert (2006-03-22):
# Shanks & Pottenger write that Gaza did not observe DST until 1957, but go
# with Shapir and assume that it observed DST from 1940 through 1947,
# and that it used Jordanian rules starting in 1996.
# We don't yet need a separate entry for the West Bank, since
# the only differences between it and Gaza that we know about
# occurred before our cutoff date of 1970.
# However, as we get more information, we may need to add entries
# for parts of the West Bank as they transitioned from Israel's rules
# to Palestine's rules.

# From IINS News Service - Israel - 1998-03-23 10:38:07 Israel time,
# forwarded by Ephraim Silverberg:
#
# Despite the fact that Israel changed over to daylight savings time
# last week, the PLO Authority (PA) has decided not to turn its clocks
# one-hour forward at this time.  As a sign of independence from Israeli rule,
# the PA has decided to implement DST in April.

# From Paul Eggert (1999-09-20):
# Daoud Kuttab writes in
# <a href="http://www.jpost.com/com/Archive/22.Apr.1999/Opinion/Article-2.html">
# Holiday havoc
# </a> (Jerusalem Post, 1999-04-22) that
# the Palestinian National Authority changed to DST on 1999-04-15.
# I vaguely recall that they switch back in October (sorry, forgot the source).
# For now, let's assume that the spring switch was at 24:00,
# and that they switch at 0:00 on the 3rd Fridays of April and October.

# From Paul Eggert (2005-11-22):
# Starting 2004 transitions are from Steffen Thorsen's web site timeanddate.com.

# From Steffen Thorsen (2005-11-23):
# A user from Gaza reported that Gaza made the change early because of
# the Ramadan.  Next year Ramadan will be even earlier, so I think
# there is a good chance next year's end date will be around two weeks
# earlier--the same goes for Jordan.

# From Steffen Thorsen (2006-08-17):
# I was informed by a user in Bethlehem that in Bethlehem it started the
# same day as Israel, and after checking with other users in the area, I
# was informed that they started DST one day after Israel.  I was not
# able to find any authoritative sources at the time, nor details if
# Gaza changed as well, but presumed Gaza to follow the same rules as
# the West Bank.

# From Steffen Thorsen (2006-09-26):
# according to the Palestine News Network (2006-09-19):
# http://english.pnn.ps/index.php?option=com_content&task=view&id=596&Itemid=5
# > The Council of Ministers announced that this year its winter schedule
# > will begin early, as of midnight Thursday.  It is also time to turn
# > back the clocks for winter.  Friday will begin an hour late this week.
# I guess it is likely that next year's date will be moved as well,
# because of the Ramadan.

# From Jesper Norgaard Welen (2007-09-18):
# According to Steffen Thorsen's web site the Gaza Strip and the rest of the
# Palestinian territories left DST early on 13.th. of September at 2:00.

# From Paul Eggert (2007-09-20):
# My understanding is that Gaza and the West Bank disagree even over when
# the weekend is (Thursday+Friday versus Friday+Saturday), so I'd be a bit
# surprised if they agreed about DST.  But for now, assume they agree.
# For lack of better information, predict that future changes will be
# the 2nd Thursday of September at 02:00.

# From Alexander Krivenyshev (2008-08-28):
# Here is an article, that Mideast running on different clocks at Ramadan.
#
# Gaza Strip (as Egypt) ended DST at midnight Thursday (Aug 28, 2008), while
# the West Bank will end Daylight Saving Time at midnight Sunday (Aug 31, 2008).
#
# <a href="http://www.guardian.co.uk/world/feedarticle/7759001">
# http://www.guardian.co.uk/world/feedarticle/7759001
# </a>
# <a href="http://www.abcnews.go.com/International/wireStory?id=5676087">
# http://www.abcnews.go.com/International/wireStory?id=5676087
# </a>
# or
# <a href="http://www.worldtimezone.com/dst_news/dst_news_gazastrip01.html">
# http://www.worldtimezone.com/dst_news/dst_news_gazastrip01.html
# </a>

# From Alexander Krivenyshev (2009-03-26):
# According to the Palestine News Network (arabic.pnn.ps), Palestinian
# government decided to start Daylight Time on Thursday night March
# 26 and continue until the night of 27 September 2009.
#
# (in Arabic)
# <a href="http://arabic.pnn.ps/index.php?option=com_content&task=view&id=50850">
# http://arabic.pnn.ps/index.php?option=com_content&task=view&id=50850
# </a>
#
# or
# (English translation)
# <a href="http://www.worldtimezone.com/dst_news/dst_news_westbank01.html">
# http://www.worldtimezone.com/dst_news/dst_news_westbank01.html
# </a>

# From Steffen Thorsen (2009-08-31):
# Palestine's Council of Ministers announced that they will revert back to
# winter time on Friday, 2009-09-04.
#
# One news source:
# <a href="http://www.safa.ps/ara/?action=showdetail&seid=4158">
# http://www.safa.ps/ara/?action=showdetail&seid=4158
# </a>
# (Palestinian press agency, Arabic),
# Google translate: "Decided that the Palestinian government in Ramallah
# headed by Salam Fayyad, the start of work in time for the winter of
# 2009, starting on Friday approved the fourth delay Sept. clock sixty
# minutes per hour as of Friday morning."
#
# We are not sure if Gaza will do the same, last year they had a different
# end date, we will keep this page updated:
# <a href="http://www.timeanddate.com/news/time/westbank-gaza-dst-2009.html">
# http://www.timeanddate.com/news/time/westbank-gaza-dst-2009.html
# </a>

# From Alexander Krivenyshev (2009-09-02):
# Seems that Gaza Strip will go back to Winter Time same date as West Bank.
#
# According to Palestinian Ministry Of Interior, West Bank and Gaza Strip plan
# to change time back to Standard time on September 4, 2009.
#
# "Winter time unite the West Bank and Gaza"
# (from Palestinian National Authority):
# <a href="http://www.moi.gov.ps/en/?page=633167343250594025&nid=11505
# http://www.moi.gov.ps/en/?page=633167343250594025&nid=11505
# </a>
# or
# <a href="http://www.worldtimezone.com/dst_news/dst_news_gazastrip02.html>
# http://www.worldtimezone.com/dst_news/dst_news_gazastrip02.html
# </a>

# From Alexander Krivenyshev (2010-03-19):
# According to Voice of Palestine DST will last for 191 days, from March
# 26, 2010 till "the last Sunday before the tenth day of Tishri
# (October), each year" (October 03, 2010?)
#
# <a href="http://palvoice.org/forums/showthread.php?t=245697">
# http://palvoice.org/forums/showthread.php?t=245697
# </a>
# (in Arabic)
# or
# <a href="http://www.worldtimezone.com/dst_news/dst_news_westbank03.html">
# http://www.worldtimezone.com/dst_news/dst_news_westbank03.html
# </a>

# From Steffen Thorsen (2010-03-24):
# ...Ma'an News Agency reports that Hamas cabinet has decided it will
# start one day later, at 12:01am. Not sure if they really mean 12:01am or
# noon though:
#
# <a href="http://www.maannews.net/eng/ViewDetails.aspx?ID=271178">
# http://www.maannews.net/eng/ViewDetails.aspx?ID=271178
# </a>
# (Ma'an News Agency)
# "At 12:01am Friday, clocks in Israel and the West Bank will change to
# 1:01am, while Gaza clocks will change at 12:01am Saturday morning."

# From Steffen Thorsen (2010-08-11):
# According to several sources, including
# <a href="http://www.maannews.net/eng/ViewDetails.aspx?ID=306795">
# http://www.maannews.net/eng/ViewDetails.aspx?ID=306795
# </a>
# the clocks were set back one hour at 2010-08-11 00:00:00 local time in
# Gaza and the West Bank.
# Some more background info:
# <a href="http://www.timeanddate.com/news/time/westbank-gaza-end-dst-2010.html">
# http://www.timeanddate.com/news/time/westbank-gaza-end-dst-2010.html
# </a>

# From Steffen Thorsen (2011-08-26):
# Gaza and the West Bank did go back to standard time in the beginning of
# August, and will now enter daylight saving time again on 2011-08-30
# 00:00 (so two periods of DST in 2011). The pause was because of
# Ramadan.
#
# <a href="http://www.maannews.net/eng/ViewDetails.aspx?ID=416217">
# http://www.maannews.net/eng/ViewDetails.aspx?ID=416217
# </a>
# Additional info:
# <a href="http://www.timeanddate.com/news/time/palestine-dst-2011.html">
# http://www.timeanddate.com/news/time/palestine-dst-2011.html
# </a>

# From Alexander Krivenyshev (2011-08-27):
# According to the article in The Jerusalem Post:
# "...Earlier this month, the Palestinian government in the West Bank decided to
# move to standard time for 30 days, during Ramadan. The Palestinians in the
# Gaza Strip accepted the change and also moved their clocks one hour back.
# The Hamas government said on Saturday that it won't observe summertime after
# the Muslim feast of Id al-Fitr, which begins on Tuesday..."
# ...
# <a href="http://www.jpost.com/MiddleEast/Article.aspx?id=235650">
# http://www.jpost.com/MiddleEast/Article.aspx?id=235650
# </a>
# or
# <a href="http://www.worldtimezone.com/dst_news/dst_news_gazastrip05.html">
# http://www.worldtimezone.com/dst_news/dst_news_gazastrip05.html
# </a>
# The rules for Egypt are stolen from the `africa' file.

# From Steffen Thorsen (2011-09-30):
# West Bank did end Daylight Saving Time this morning/midnight (2011-09-30
# 00:00).
# So West Bank and Gaza now have the same time again.
#
# Many sources, including:
# <a href="http://www.maannews.net/eng/ViewDetails.aspx?ID=424808">
# http://www.maannews.net/eng/ViewDetails.aspx?ID=424808
# </a>

# From Steffen Thorsen (2012-03-26):
# Palestinian news sources tell that both Gaza and West Bank will start DST
# on Friday (Thursday midnight, 2012-03-29 24:00).
# Some of many sources in Arabic:
# <a href="http://www.samanews.com/index.php?act=Show&id=122638">
# http://www.samanews.com/index.php?act=Show&id=122638
# </a>
#
# <a href="http://safa.ps/details/news/74352/%D8%A8%D8%AF%D8%A1-%D8%A7%D9%84%D8%AA%D9%88%D9%82%D9%8A%D8%AA-%D8%A7%D9%84%D8%B5%D9%8A%D9%81%D9%8A-%D8%A8%D8%A7%D9%84%D8%B6%D9%81%D8%A9-%D9%88%D8%BA%D8%B2%D8%A9-%D9%84%D9%8A%D9%84%D8%A9-%D8%A7%D9%84%D8%AC%D9%85%D8%B9%D8%A9.html">
# http://safa.ps/details/news/74352/%D8%A8%D8%AF%D8%A1-%D8%A7%D9%84%D8%AA%D9%88%D9%82%D9%8A%D8%AA-%D8%A7%D9%84%D8%B5%D9%8A%D9%81%D9%8A-%D8%A8%D8%A7%D9%84%D8%B6%D9%81%D8%A9-%D9%88%D8%BA%D8%B2%D8%A9-%D9%84%D9%8A%D9%84%D8%A9-%D8%A7%D9%84%D8%AC%D9%85%D8%B9%D8%A9.html
# </a>
#
# Our brief summary:
# <a href="http://www.timeanddate.com/news/time/gaza-west-bank-dst-2012.html">
# http://www.timeanddate.com/news/time/gaza-west-bank-dst-2012.html
# </a>

# From Arthur David Olson (2012-03-27):
# The timeanddate article for 2012 says that "the end date has not yet been
# announced" and that "Last year, both...paused daylight saving time during...
# Ramadan. It is not yet known [for] 2012."
# For now, assume both switch back on the last Friday in September. XXX

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule EgyptAsia	1957	only	-	May	10	0:00	1:00	S
Rule EgyptAsia	1957	1958	-	Oct	 1	0:00	0	-
Rule EgyptAsia	1958	only	-	May	 1	0:00	1:00	S
Rule EgyptAsia	1959	1967	-	May	 1	1:00	1:00	S
Rule EgyptAsia	1959	1965	-	Sep	30	3:00	0	-
Rule EgyptAsia	1966	only	-	Oct	 1	3:00	0	-

Rule Palestine	1999	2005	-	Apr	Fri>=15	0:00	1:00	S
Rule Palestine	1999	2003	-	Oct	Fri>=15	0:00	0	-
Rule Palestine	2004	only	-	Oct	 1	1:00	0	-
Rule Palestine	2005	only	-	Oct	 4	2:00	0	-
Rule Palestine	2006	2008	-	Apr	 1	0:00	1:00	S
Rule Palestine	2006	only	-	Sep	22	0:00	0	-
Rule Palestine	2007	only	-	Sep	Thu>=8	2:00	0	-
Rule Palestine	2008	only	-	Aug	lastFri	0:00	0	-
Rule Palestine	2009	only	-	Mar	lastFri	0:00	1:00	S
Rule Palestine	2009	only	-	Sep	Fri>=1	2:00	0	-
Rule Palestine	2010	only	-	Mar	lastSat	0:01	1:00	S
Rule Palestine	2010	only	-	Aug	11	0:00	0	-

# From Arthur David Olson (2011-09-20):
# 2011 transitions per http://www.timeanddate.com as of 2011-09-20.
# From Paul Eggert (2012-10-12):
# 2012 transitions per http://www.timeanddate.com as of 2012-10-12.

# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Gaza	2:17:52	-	LMT	1900 Oct
			2:00	Zion	EET	1948 May 15
			2:00 EgyptAsia	EE%sT	1967 Jun  5
			2:00	Zion	I%sT	1996
			2:00	Jordan	EE%sT	1999
			2:00 Palestine	EE%sT	2011 Apr  2 12:01
			2:00	1:00	EEST	2011 Aug  1
			2:00	-	EET	2012 Mar 30
			2:00	1:00	EEST	2012 Sep 21 1:00
			2:00	-	EET

Zone	Asia/Hebron	2:20:23	-	LMT	1900 Oct
			2:00	Zion	EET	1948 May 15
			2:00 EgyptAsia	EE%sT	1967 Jun  5
			2:00	Zion	I%sT	1996
			2:00	Jordan	EE%sT	1999
			2:00 Palestine	EE%sT	2008 Aug
			2:00 	1:00	EEST	2008 Sep
			2:00 Palestine	EE%sT	2011 Apr  1 12:01
			2:00	1:00	EEST	2011 Aug  1
			2:00	-	EET	2011 Aug 30
			2:00	1:00	EEST	2011 Sep 30 3:00
			2:00	-	EET	2012 Mar 30
			2:00	1:00	EEST	2012 Sep 21 1:00
			2:00	-	EET

# Paracel Is
# no information

# Philippines
# On 1844-08-16, Narciso Claveria, governor-general of the
# Philippines, issued a proclamation announcing that 1844-12-30 was to
# be immediately followed by 1845-01-01.  Robert H. van Gent has a
# transcript of the decree in <http://www.phys.uu.nl/~vgent/idl/idl.htm>.
# The rest of the data are from Shanks & Pottenger.

# From Paul Eggert (2006-04-25):
# Tomorrow's Manila Standard reports that the Philippines Department of
# Trade and Industry is considering adopting DST this June when the
# rainy season begins.  See
# <http://www.manilastandardtoday.com/?page=politics02_april26_2006>.
# For now, we'll ignore this, since it's not definite and we lack details.
#
# From Jesper Norgaard Welen (2006-04-26):
# ... claims that Philippines had DST last time in 1990:
# http://story.philippinetimes.com/p.x/ct/9/id/145be20cc6b121c0/cid/3e5bbccc730d258c/
# [a story dated 2006-04-25 by Cris Larano of Dow Jones Newswires,
# but no details]

# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Phil	1936	only	-	Nov	1	0:00	1:00	S
Rule	Phil	1937	only	-	Feb	1	0:00	0	-
Rule	Phil	1954	only	-	Apr	12	0:00	1:00	S
Rule	Phil	1954	only	-	Jul	1	0:00	0	-
Rule	Phil	1978	only	-	Mar	22	0:00	1:00	S
Rule	Phil	1978	only	-	Sep	21	0:00	0	-
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Manila	-15:56:00 -	LMT	1844 Dec 31
			8:04:00 -	LMT	1899 May 11
			8:00	Phil	PH%sT	1942 May
			9:00	-	JST	1944 Nov
			8:00	Phil	PH%sT

# Qatar
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Qatar	3:26:08 -	LMT	1920	# Al Dawhah / Doha
			4:00	-	GST	1972 Jun
			3:00	-	AST

# Saudi Arabia
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Riyadh	3:06:52 -	LMT	1950
			3:00	-	AST

# Singapore
# The data here are taken from Mok Ly Yng (2003-10-30)
# <http://www.math.nus.edu.sg/aslaksen/teaching/timezone.html>.
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Singapore	6:55:25 -	LMT	1901 Jan  1
			6:55:25	-	SMT	1905 Jun  1 # Singapore M.T.
			7:00	-	MALT	1933 Jan  1 # Malaya Time
			7:00	0:20	MALST	1936 Jan  1
			7:20	-	MALT	1941 Sep  1
			7:30	-	MALT	1942 Feb 16
			9:00	-	JST	1945 Sep 12
			7:30	-	MALT	1965 Aug  9 # independence
			7:30	-	SGT	1982 Jan  1 # Singapore Time
			8:00	-	SGT

# Spratly Is
# no information

# Sri Lanka
# From Paul Eggert (1996-09-03):
# "Sri Lanka advances clock by an hour to avoid blackout"
# (www.virtual-pc.com/lankaweb/news/items/240596-2.html, 1996-05-24,
# no longer available as of 1999-08-17)
# reported ``the country's standard time will be put forward by one hour at
# midnight Friday (1830 GMT) `in the light of the present power crisis'.''
#
# From Dharmasiri Senanayake, Sri Lanka Media Minister (1996-10-24), as quoted
# by Shamindra in
# <a href="news:54rka5$<EMAIL>">
# Daily News - Hot News Section (1996-10-26)
# </a>:
# With effect from 12.30 a.m. on 26th October 1996
# Sri Lanka will be six (06) hours ahead of GMT.

# From Jesper Norgaard Welen (2006-04-14), quoting Sri Lanka News Online
# <http://news.sinhalaya.com/wmview.php?ArtID=11002> (2006-04-13):
# 0030 hrs on April 15, 2006 (midnight of April 14, 2006 +30 minutes)
# at present, become 2400 hours of April 14, 2006 (midnight of April 14, 2006).

# From Peter Apps and Ranga Sirila of Reuters (2006-04-12) in:
# <http://today.reuters.co.uk/news/newsArticle.aspx?type=scienceNews&storyID=2006-04-12T172228Z_01_COL295762_RTRIDST_0_SCIENCE-SRILANKA-TIME-DC.XML>
# [The Tamil Tigers] never accepted the original 1996 time change and simply
# kept their clocks set five and a half hours ahead of Greenwich Mean
# Time (GMT), in line with neighbor India.
# From Paul Eggert (2006-04-18):
# People who live in regions under Tamil control can use [TZ='Asia/Kolkata'],
# as that zone has agreed with the Tamil areas since our cutoff date of 1970.

# From K Sethu (2006-04-25):
# I think the abbreviation LKT originated from the world of computers at
# the time of or subsequent to the time zone changes by SL Government
# twice in 1996 and probably SL Government or its standardization
# agencies never declared an abbreviation as a national standard.
#
# I recollect before the recent change the government annoucemments
# mentioning it as simply changing Sri Lanka Standard Time or Sri Lanka
# Time and no mention was made about the abbreviation.
#
# If we look at Sri Lanka Department of Government's "Official News
# Website of Sri Lanka" ... http://www.news.lk/ we can see that they
# use SLT as abbreviation in time stamp at the beginning of each news
# item....
#
# Within Sri Lanka I think LKT is well known among computer users and
# adminsitrators.  In my opinion SLT may not be a good choice because the
# nation's largest telcom / internet operator Sri Lanka Telcom is well
# known by that abbreviation - simply as SLT (there IP domains are
# slt.lk and sltnet.lk).
#
# But if indeed our government has adopted SLT as standard abbreviation
# (that we have not known so far) then  it is better that it be used for
# all computers.

# From Paul Eggert (2006-04-25):
# One possibility is that we wait for a bit for the dust to settle down
# and then see what people actually say in practice.

# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Colombo	5:19:24 -	LMT	1880
			5:19:32	-	MMT	1906	# Moratuwa Mean Time
			5:30	-	IST	1942 Jan  5
			5:30	0:30	IHST	1942 Sep
			5:30	1:00	IST	1945 Oct 16 2:00
			5:30	-	IST	1996 May 25 0:00
			6:30	-	LKT	1996 Oct 26 0:30
			6:00	-	LKT	2006 Apr 15 0:30
			5:30	-	IST

# Syria
# Rule	NAME	FROM	TO	TYPE	IN	ON	AT	SAVE	LETTER/S
Rule	Syria	1920	1923	-	Apr	Sun>=15	2:00	1:00	S
Rule	Syria	1920	1923	-	Oct	Sun>=1	2:00	0	-
Rule	Syria	1962	only	-	Apr	29	2:00	1:00	S
Rule	Syria	1962	only	-	Oct	1	2:00	0	-
Rule	Syria	1963	1965	-	May	1	2:00	1:00	S
Rule	Syria	1963	only	-	Sep	30	2:00	0	-
Rule	Syria	1964	only	-	Oct	1	2:00	0	-
Rule	Syria	1965	only	-	Sep	30	2:00	0	-
Rule	Syria	1966	only	-	Apr	24	2:00	1:00	S
Rule	Syria	1966	1976	-	Oct	1	2:00	0	-
Rule	Syria	1967	1978	-	May	1	2:00	1:00	S
Rule	Syria	1977	1978	-	Sep	1	2:00	0	-
Rule	Syria	1983	1984	-	Apr	9	2:00	1:00	S
Rule	Syria	1983	1984	-	Oct	1	2:00	0	-
Rule	Syria	1986	only	-	Feb	16	2:00	1:00	S
Rule	Syria	1986	only	-	Oct	9	2:00	0	-
Rule	Syria	1987	only	-	Mar	1	2:00	1:00	S
Rule	Syria	1987	1988	-	Oct	31	2:00	0	-
Rule	Syria	1988	only	-	Mar	15	2:00	1:00	S
Rule	Syria	1989	only	-	Mar	31	2:00	1:00	S
Rule	Syria	1989	only	-	Oct	1	2:00	0	-
Rule	Syria	1990	only	-	Apr	1	2:00	1:00	S
Rule	Syria	1990	only	-	Sep	30	2:00	0	-
Rule	Syria	1991	only	-	Apr	 1	0:00	1:00	S
Rule	Syria	1991	1992	-	Oct	 1	0:00	0	-
Rule	Syria	1992	only	-	Apr	 8	0:00	1:00	S
Rule	Syria	1993	only	-	Mar	26	0:00	1:00	S
Rule	Syria	1993	only	-	Sep	25	0:00	0	-
# IATA SSIM (1998-02) says 1998-04-02;
# (1998-09) says 1999-03-29 and 1999-09-29; (1999-02) says 1999-04-02,
# 2000-04-02, and 2001-04-02; (1999-09) says 2000-03-31 and 2001-03-31;
# (2006) says 2006-03-31 and 2006-09-22;
# for now ignore all these claims and go with Shanks & Pottenger,
# except for the 2006-09-22 claim (which seems right for Ramadan).
Rule	Syria	1994	1996	-	Apr	 1	0:00	1:00	S
Rule	Syria	1994	2005	-	Oct	 1	0:00	0	-
Rule	Syria	1997	1998	-	Mar	lastMon	0:00	1:00	S
Rule	Syria	1999	2006	-	Apr	 1	0:00	1:00	S
# From Stephen Colebourne (2006-09-18):
# According to IATA data, Syria will change DST on 21st September [21:00 UTC]
# this year [only]....  This is probably related to Ramadan, like Egypt.
Rule	Syria	2006	only	-	Sep	22	0:00	0	-
# From Paul Eggert (2007-03-29):
# Today the AP reported "Syria will switch to summertime at midnight Thursday."
# http://www.iht.com/articles/ap/2007/03/29/africa/ME-GEN-Syria-Time-Change.php
Rule	Syria	2007	only	-	Mar	lastFri	0:00	1:00	S
# From Jesper Norgard (2007-10-27):
# The sister center ICARDA of my work CIMMYT is confirming that Syria DST will
# not take place 1.st November at 0:00 o'clock but 1.st November at 24:00 or
# rather Midnight between Thursday and Friday. This does make more sence than
# having it between Wednesday and Thursday (two workdays in Syria) since the
# weekend in Syria is not Saturday and Sunday, but Friday and Saturday. So now
# it is implemented at midnight of the last workday before weekend...
#
# From Steffen Thorsen (2007-10-27):
# Jesper Norgaard Welen wrote:
#
# > "Winter local time in Syria will be observed at midnight of Thursday 1
# > November 2007, and the clock will be put back 1 hour."
#
# I found confirmation on this in this gov.sy-article (Arabic):
# http://wehda.alwehda.gov.sy/_print_veiw.asp?FileName=12521710520070926111247
#
# which using Google's translate tools says:
# Council of Ministers also approved the commencement of work on
# identifying the winter time as of Friday, 2/11/2007 where the 60th
# minute delay at midnight Thursday 1/11/2007.
Rule	Syria	2007	only	-	Nov	 Fri>=1	0:00	0	-

# From Stephen Colebourne (2008-03-17):
# For everyone's info, I saw an IATA time zone change for [Syria] for
# this month (March 2008) in the last day or so...This is the data IATA
# are now using:
# Country     Time Standard   --- DST Start ---   --- DST End ---  DST
# Name        Zone Variation   Time    Date        Time    Date
# Variation
# Syrian Arab
# Republic    SY    +0200      2200  03APR08       2100  30SEP08   +0300
#                              2200  02APR09       2100  30SEP09   +0300
#                              2200  01APR10       2100  30SEP10   +0300

# From Arthur David Olson (2008-03-17):
# Here's a link to English-language coverage by the Syrian Arab News
# Agency (SANA)...
# <a href="http://www.sana.sy/eng/21/2008/03/11/165173.htm">
# http://www.sana.sy/eng/21/2008/03/11/165173.htm
# </a>...which reads (in part) "The Cabinet approved the suggestion of the
# Ministry of Electricity to begin daylight savings time on Friday April
# 4th, advancing clocks one hour ahead on midnight of Thursday April 3rd."
# Since Syria is two hours east of UTC, the 2200 and 2100 transition times
# shown above match up with midnight in Syria.

# From Arthur David Olson (2008-03-18):
# My buest guess at a Syrian rule is "the Friday nearest April 1";
# coding that involves either using a "Mar Fri>=29" construct that old time zone
# compilers can't handle  or having multiple Rules (a la Israel).
# For now, use "Apr Fri>=1", and go with IATA on a uniform Sep 30 end.

# From Steffen Thorsen (2008-10-07):
# Syria has now officially decided to end DST on 2008-11-01 this year,
# according to the following article in the Syrian Arab News Agency (SANA).
#
# The article is in Arabic, and seems to tell that they will go back to
# winter time on 2008-11-01 at 00:00 local daylight time (delaying/setting
# clocks back 60 minutes).
#
# <a href="http://sana.sy/ara/2/2008/10/07/195459.htm">
# http://sana.sy/ara/2/2008/10/07/195459.htm
# </a>

# From Steffen Thorsen (2009-03-19):
# Syria will start DST on 2009-03-27 00:00 this year according to many sources,
# two examples:
#
# <a href="http://www.sana.sy/eng/21/2009/03/17/217563.htm">
# http://www.sana.sy/eng/21/2009/03/17/217563.htm
# </a>
# (English, Syrian Arab News # Agency)
# <a href="http://thawra.alwehda.gov.sy/_View_news2.asp?FileName=94459258720090318012209">
# http://thawra.alwehda.gov.sy/_View_news2.asp?FileName=94459258720090318012209
# </a>
# (Arabic, gov-site)
#
# We have not found any sources saying anything about when DST ends this year.
#
# Our summary
# <a href="http://www.timeanddate.com/news/time/syria-dst-starts-march-27-2009.html">
# http://www.timeanddate.com/news/time/syria-dst-starts-march-27-2009.html
# </a>

# From Steffen Thorsen (2009-10-27):
# The Syrian Arab News Network on 2009-09-29 reported that Syria will
# revert back to winter (standard) time on midnight between Thursday
# 2009-10-29 and Friday 2009-10-30:
# <a href="http://www.sana.sy/ara/2/2009/09/29/247012.htm">
# http://www.sana.sy/ara/2/2009/09/29/247012.htm (Arabic)
# </a>

# From Arthur David Olson (2009-10-28):
# We'll see if future DST switching times turn out to be end of the last
# Thursday of the month or the start of the last Friday of the month or
# something else. For now, use the start of the last Friday.

# From Steffen Thorsen (2010-03-17):
# The "Syrian News Station" reported on 2010-03-16 that the Council of
# Ministers has decided that Syria will start DST on midnight Thursday
# 2010-04-01: (midnight between Thursday and Friday):
# <a href="http://sns.sy/sns/?path=news/read/11421">
# http://sns.sy/sns/?path=news/read/11421 (Arabic)
# </a>

# From Steffen Thorsen (2012-03-26):
# Today, Syria's government announced that they will start DST early on Friday
# (00:00). This is a bit earlier than the past two years.
#
# From Syrian Arab News Agency, in Arabic:
# <a href="http://www.sana.sy/ara/2/2012/03/26/408215.htm">
# http://www.sana.sy/ara/2/2012/03/26/408215.htm
# </a>
#
# Our brief summary:
# <a href="http://www.timeanddate.com/news/time/syria-dst-2012.html">
# http://www.timeanddate.com/news/time/syria-dst-2012.html
# </a>

# From Arthur David Olson (2012-03-27):
# Assume last Friday in March going forward XXX.

Rule	Syria	2008	only	-	Apr	Fri>=1	0:00	1:00	S
Rule	Syria	2008	only	-	Nov	1	0:00	0	-
Rule	Syria	2009	only	-	Mar	lastFri	0:00	1:00	S
Rule	Syria	2010	2011	-	Apr	Fri>=1	0:00	1:00	S
Rule	Syria	2012	max	-	Mar	lastFri	0:00	1:00	S
Rule	Syria	2009	max	-	Oct	lastFri	0:00	0	-

# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Damascus	2:25:12 -	LMT	1920	# Dimashq
			2:00	Syria	EE%sT

# Tajikistan
# From Shanks & Pottenger.
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Dushanbe	4:35:12 -	LMT	1924 May  2
			5:00	-	DUST	1930 Jun 21 # Dushanbe Time
			6:00 RussiaAsia DUS%sT	1991 Mar 31 2:00s
			5:00	1:00	DUSST	1991 Sep  9 2:00s
			5:00	-	TJT		    # Tajikistan Time

# Thailand
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Bangkok	6:42:04	-	LMT	1880
			6:42:04	-	BMT	1920 Apr # Bangkok Mean Time
			7:00	-	ICT

# Turkmenistan
# From Shanks & Pottenger.
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Ashgabat	3:53:32 -	LMT	1924 May  2 # or Ashkhabad
			4:00	-	ASHT	1930 Jun 21 # Ashkhabad Time
			5:00 RussiaAsia	ASH%sT	1991 Mar 31 2:00
			4:00 RussiaAsia	ASH%sT	1991 Oct 27 # independence
			4:00 RussiaAsia	TM%sT	1992 Jan 19 2:00
			5:00	-	TMT

# United Arab Emirates
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Dubai	3:41:12 -	LMT	1920
			4:00	-	GST

# Uzbekistan
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Samarkand	4:27:12 -	LMT	1924 May  2
			4:00	-	SAMT	1930 Jun 21 # Samarkand Time
			5:00	-	SAMT	1981 Apr  1
			5:00	1:00	SAMST	1981 Oct  1
			6:00	-	TAST	1982 Apr  1 # Tashkent Time
			5:00 RussiaAsia	SAM%sT	1991 Sep  1 # independence
			5:00 RussiaAsia	UZ%sT	1992
			5:00	-	UZT
Zone	Asia/Tashkent	4:37:12 -	LMT	1924 May  2
			5:00	-	TAST	1930 Jun 21 # Tashkent Time
			6:00 RussiaAsia	TAS%sT	1991 Mar 31 2:00
			5:00 RussiaAsia	TAS%sT	1991 Sep  1 # independence
			5:00 RussiaAsia	UZ%sT	1992
			5:00	-	UZT

# Vietnam

# From Arthur David Olson (2008-03-18):
# The English-language name of Vietnam's most populous city is "Ho Chi Min City";
# we use Ho_Chi_Minh below to avoid a name of more than 14 characters.

# From Shanks & Pottenger:
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Ho_Chi_Minh	7:06:40 -	LMT	1906 Jun  9
			7:06:20	-	SMT	1911 Mar 11 0:01 # Saigon MT?
			7:00	-	ICT	1912 May
			8:00	-	ICT	1931 May
			7:00	-	ICT

# Yemen
# Zone	NAME		GMTOFF	RULES	FORMAT	[UNTIL]
Zone	Asia/Aden	3:00:48	-	LMT	1950
			3:00	-	AST
