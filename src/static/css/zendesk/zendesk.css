/* line 1, ../../../sass/zendesk/zendesk.scss */
.zendesk_container {
  display: none;
  padding: 20px 30px 0 30px;
}
/* line 5, ../../../sass/zendesk/zendesk.scss */
.zendesk_container.active {
  display: block;
}
/* line 9, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .top_wrapper {
  margin-bottom: 5px;
}
/* line 12, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .top_wrapper .title {
  font-family: Source Sans Pro, sans-serif;
  font-weight: 600;
  font-size: 20px;
  color: #4D4F5C;
}
/* line 19, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .top_wrapper .new_client_wrapper {
  display: inline-flex;
  align-items: center;
  margin-left: 20px;
}
/* line 24, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .top_wrapper .new_client_wrapper .new_client_btn {
  font-size: 14px;
  padding: 1px 18px;
  border: 0.5px solid #707070;
  cursor: pointer;
}
/* line 30, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .top_wrapper .new_client_wrapper .new_client_btn:first-child {
  border-top-left-radius: 6px !important;
  border-bottom-left-radius: 6px !important;
  border-right: none;
}
/* line 35, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .top_wrapper .new_client_wrapper .new_client_btn:first-child.active {
  border-right: 0.5px solid #F28E2A;
}
/* line 40, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .top_wrapper .new_client_wrapper .new_client_btn:last-child {
  border-top-right-radius: 6px !important;
  border-bottom-right-radius: 6px !important;
  border-left: none;
}
/* line 45, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .top_wrapper .new_client_wrapper .new_client_btn:last-child.active {
  border-left: 0.5px solid #F28E2A;
}
/* line 50, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .top_wrapper .new_client_wrapper .new_client_btn.active {
  background-color: #FCEEE2;
  border-color: #F28E2A;
}
/* line 58, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_form {
  display: flex;
  align-items: center;
}
/* line 62, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_form .inputs_wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 800px;
}
/* line 68, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_form .inputs_wrapper .input_wrapper {
  position: relative;
  border-radius: 4px !important;
  border: 1px solid #D7DAE2;
  background-color: white;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.05);
  margin: 10px 0;
  width: calc((100% - 40px) / 3);
  min-height: 40px;
}
/* line 78, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_form .inputs_wrapper .input_wrapper label {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  margin-left: 16px;
  padding: 0 4px;
  background-color: white;
  font-size: 16px;
  white-space: nowrap;
  pointer-events: none;
  transition: all 0.3s;
}
/* line 92, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_form .inputs_wrapper .input_wrapper label.error {
  transform: none;
  background: transparent;
  margin-left: 0;
  margin-top: 10px;
  top: initial !important;
  font-size: 16px !important;
  bottom: -30px;
  font-weight: 300;
  color: red;
}
/* line 105, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_form .inputs_wrapper .input_wrapper input, .zendesk_container .zendesk_form .inputs_wrapper .input_wrapper select {
  width: 100%;
  height: 100%;
  border: none;
  background: none;
  outline: none;
  padding: 0 20px;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 16px;
}
/* line 116, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_form .inputs_wrapper .input_wrapper select {
  width: 95%;
}
/* line 120, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_form .inputs_wrapper .input_wrapper.active label:not(.error) {
  top: 0;
  font-size: 12px;
}
/* line 127, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_form .buttons_block {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  width: 180px;
  margin-left: 100px;
}
/* line 135, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_form .buttons_block .search_loader {
  width: 215px;
  text-align: center;
  display: none;
}
/* line 140, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_form .buttons_block .search_loader img {
  width: 50px;
}
/* line 145, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_form .buttons_block div:not(.search_loader) {
  width: 100%;
  height: 40px;
  border: 1px solid #F28E2A;
  border-radius: 4px !important;
  background-color: white;
  margin: 10px 0;
  font-size: 18px;
  letter-spacing: 0.45px;
  color: #F28E2A;
  transition: opacity 0.3s;
}
/* line 157, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_form .buttons_block div:not(.search_loader).find_user_btn, .zendesk_container .zendesk_form .buttons_block div:not(.search_loader).create_user_btn {
  background-color: #F28E2A;
  color: white;
}
/* line 162, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_form .buttons_block div:not(.search_loader):hover {
  opacity: 0.7;
}
/* line 166, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_form .buttons_block div:not(.search_loader) button {
  appearance: none;
  width: 100%;
  height: 100%;
  border: none;
  background: none;
  font: inherit;
  color: inherit;
  cursor: pointer;
}
/* line 177, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_form .buttons_block div:not(.search_loader).hide {
  display: none;
}
/* line 182, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_form .buttons_block .create_user_btn {
  display: none;
}
/* line 188, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results {
  margin-top: 10px;
  border-bottom: 1px solid #D7DAE2;
  padding: 10px 0;
}
/* line 193, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .user_element {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #D7DAE2;
  border-radius: 12px !important;
  margin-bottom: 10px;
}
/* line 201, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .user_element .modify_form {
  width: calc(100% - 400px);
  display: flex;
  flex-wrap: wrap;
  padding: 10px 30px;
}
/* line 207, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .user_element .modify_form .field_element {
  width: calc(100%/3);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 30px;
  margin: 7px 0;
}
/* line 215, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .user_element .modify_form .field_element input, .zendesk_container .zendesk_results .user_element .modify_form .field_element select {
  width: calc(100% - 40px);
  border: none;
  background: none !important;
  outline: none;
  appearance: none;
  pointer-events: none;
}
/* line 224, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .user_element .modify_form .field_element label {
  display: none !important;
}
/* line 230, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .user_element .buttons_wrapper {
  width: 400px;
  padding: 0 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
/* line 237, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .user_element .buttons_wrapper div {
  width: calc(50% - 5px);
  height: 40px;
  border: 1px solid #43425D;
  border-radius: 4px !important;
  background-color: white;
  margin: 15px 0;
  font-size: 18px;
  letter-spacing: 0.45px;
  color: #43425D;
  transition: opacity 0.3s;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
/* line 253, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .user_element .buttons_wrapper div.select_user_btn, .zendesk_container .zendesk_results .user_element .buttons_wrapper div.cancel_user_btn {
  background-color: #43425D;
  color: white;
}
/* line 258, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .user_element .buttons_wrapper div:hover {
  opacity: 0.7;
}
/* line 263, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .user_element .buttons_wrapper .save_user_btn {
  display: none;
}
/* line 267, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .user_element .buttons_wrapper .cancel_user_btn {
  display: none;
}
/* line 272, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .user_element.zendesk_user_template {
  display: none;
}
/* line 279, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .user_element.modifying .modify_form .field_element:not(.user_language) input, .zendesk_container .zendesk_results .user_element.modifying .modify_form .field_element:not(.user_language) select {
  pointer-events: all;
  border: initial;
  outline: 1px solid #D7DAE2;
  appearance: auto;
}
/* line 285, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .user_element.modifying .modify_form .field_element:not(.user_language) input.error, .zendesk_container .zendesk_results .user_element.modifying .modify_form .field_element:not(.user_language) select.error {
  outline: 1px solid red;
}
/* line 293, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .user_element.modifying .buttons_wrapper .save_user_btn {
  display: flex;
}
/* line 297, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .user_element.modifying .buttons_wrapper .modify_user_btn {
  display: none;
}
/* line 305, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .user_element.selected .buttons_wrapper .cancel_user_btn {
  display: flex;
}
/* line 309, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .user_element.selected .buttons_wrapper .select_user_btn {
  display: none;
}
/* line 316, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .pagination {
  display: none;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  margin-top: 20px;
}
/* line 323, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .pagination .pages {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 10px;
}
/* line 329, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .pagination .pages .page_button {
  margin: 0 10px;
  cursor: pointer;
}
/* line 333, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .pagination .pages .page_button.active {
  color: #F28E2A;
}
/* line 339, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .pagination i {
  font-size: 24px;
  margin-top: 3px;
  cursor: pointer;
}
/* line 345, ../../../sass/zendesk/zendesk.scss */
.zendesk_container .zendesk_results .pagination.active {
  display: flex;
}
/* line 354, ../../../sass/zendesk/zendesk.scss */
.zendesk_container.new_client .zendesk_form .buttons_block .find_user_btn {
  display: none;
}
/* line 358, ../../../sass/zendesk/zendesk.scss */
.zendesk_container.new_client .zendesk_form .buttons_block .create_user_btn {
  display: block;
}
