/* line 1, ../../../sass/vacation_club/vacation_club.scss */
.toggle_vacation_club_search {
  position: relative;
  box-sizing: border-box;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}
/* line 9, ../../../sass/vacation_club/vacation_club.scss */
.toggle_vacation_club_search .vacation_club_search_label {
  font-size: 16px;
  letter-spacing: 0.45px;
}
/* line 14, ../../../sass/vacation_club/vacation_club.scss */
.toggle_vacation_club_search .vacation_club_switcher {
  position: relative;
  width: 68px;
  height: 33px;
  overflow: visible;
  border-radius: 100px;
}
/* line 21, ../../../sass/vacation_club/vacation_club.scss */
.toggle_vacation_club_search .vacation_club_switcher .vacation_club_search {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  opacity: 0;
  cursor: pointer;
  z-index: 3;
}
/* line 31, ../../../sass/vacation_club/vacation_club.scss */
.toggle_vacation_club_search .vacation_club_switcher .vacation_club_search:checked:active + .knobs:before {
  margin-left: -26px;
}
/* line 35, ../../../sass/vacation_club/vacation_club.scss */
.toggle_vacation_club_search .vacation_club_switcher .vacation_club_search:checked + .knobs:before {
  content: "\f00c";
  right: 3px;
  background-color: #F28E2A;
}
/* line 41, ../../../sass/vacation_club/vacation_club.scss */
.toggle_vacation_club_search .vacation_club_switcher .vacation_club_search:checked ~ .layer {
  background-color: transparent;
}
/* line 46, ../../../sass/vacation_club/vacation_club.scss */
.toggle_vacation_club_search .vacation_club_switcher .knobs,
.toggle_vacation_club_search .vacation_club_switcher .layer {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
/* line 55, ../../../sass/vacation_club/vacation_club.scss */
.toggle_vacation_club_search .vacation_club_switcher .knobs {
  z-index: 2;
}
/* line 58, ../../../sass/vacation_club/vacation_club.scss */
.toggle_vacation_club_search .vacation_club_switcher .knobs:before {
  content: "\f00d";
  font-family: "Font Awesome 5 Pro";
  position: absolute;
  top: 3px;
  left: auto;
  right: 37px;
  width: 27px;
  height: 27px;
  box-sizing: border-box;
  color: white;
  background-color: #92714f;
  font-size: 14px;
  font-weight: bold;
  text-align: center;
  line-height: 0.7;
  padding: 11px 4px;
  border-radius: 50%;
  transition: 0.3s ease all, left 0.3s cubic-bezier(0.18, 0.89, 0.35, 1.15);
}
/* line 80, ../../../sass/vacation_club/vacation_club.scss */
.toggle_vacation_club_search .vacation_club_switcher .layer {
  width: 100%;
  background-color: rgba(146, 113, 79, 0.2);
  border: 1px solid #F28E2A;
  transition: 0.3s ease all;
  z-index: 1;
  border-radius: 100px;
}

/* line 91, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container {
  display: none;
  padding: 20px 30px 0 30px;
}
/* line 95, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container.active {
  display: block;
}
/* line 99, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_form {
  display: flex;
  align-items: center;
}
/* line 103, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_form .inputs_wrapper {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  width: 800px;
}
/* line 109, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_form .inputs_wrapper .input_wrapper {
  position: relative;
  border-radius: 4px !important;
  border: 1px solid #D7DAE2;
  background-color: white;
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.05);
  margin: 10px 0;
  width: calc((100% - 40px) / 3);
  min-height: 40px;
}
/* line 119, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_form .inputs_wrapper .input_wrapper label {
  display: inline-block;
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  margin-left: 16px;
  padding: 0 4px;
  background-color: white;
  font-size: 16px;
  white-space: nowrap;
  pointer-events: none;
  transition: all 0.3s;
}
/* line 133, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_form .inputs_wrapper .input_wrapper label.error {
  transform: none;
  background: transparent;
  margin-left: 0;
  margin-top: 10px;
  top: initial !important;
  font-size: 16px !important;
  bottom: -30px;
  font-weight: 300;
  color: red;
}
/* line 146, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_form .inputs_wrapper .input_wrapper input,
.vacation_club_container .vacation_club_form .inputs_wrapper .input_wrapper select {
  width: 100%;
  height: 100%;
  border: none;
  background: none;
  outline: none;
  padding: 0 20px;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 16px;
}
/* line 158, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_form .inputs_wrapper .input_wrapper select {
  width: 95%;
}
/* line 162, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_form .inputs_wrapper .input_wrapper.active label:not(.error) {
  top: 0;
  font-size: 12px;
}
/* line 169, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_form .buttons_block {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: stretch;
  width: 180px;
  margin-left: 100px;
}
/* line 177, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_form .buttons_block .search_loader {
  width: 215px;
  text-align: center;
  display: none;
}
/* line 182, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_form .buttons_block .search_loader img {
  width: 50px;
}
/* line 187, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_form .buttons_block div:not(.search_loader) {
  width: 100%;
  height: 40px;
  border: 1px solid #F28E2A;
  border-radius: 4px !important;
  background-color: white;
  margin: 10px 0;
  font-size: 18px;
  letter-spacing: 0.45px;
  color: #F28E2A;
  transition: opacity 0.3s;
}
/* line 199, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_form .buttons_block div:not(.search_loader).find_user_btn {
  background-color: #F28E2A;
  color: white;
}
/* line 204, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_form .buttons_block div:not(.search_loader):hover {
  opacity: 0.7;
}
/* line 208, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_form .buttons_block div:not(.search_loader) button {
  appearance: none;
  width: 100%;
  height: 100%;
  border: none;
  background: none;
  font: inherit;
  color: inherit;
  cursor: pointer;
}
/* line 219, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_form .buttons_block div:not(.search_loader).hide {
  display: none;
}
/* line 226, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results {
  margin-top: 10px;
  border-bottom: 1px solid #D7DAE2;
  padding: 10px 0;
}
/* line 231, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results .user_element {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid #D7DAE2;
  border-radius: 12px !important;
  margin-bottom: 10px;
}
/* line 239, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results .user_element .user_info {
  width: calc(100% - 400px);
  display: flex;
  flex-wrap: wrap;
  padding: 10px 30px;
}
/* line 245, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results .user_element .user_info .field_element {
  width: calc(100%/3);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-right: 30px;
  margin: 7px 0;
}
/* line 253, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results .user_element .user_info .field_element input,
.vacation_club_container .vacation_club_results .user_element .user_info .field_element select {
  width: calc(100% - 40px);
  border: none;
  background: none !important;
  outline: none;
  appearance: none;
  pointer-events: none;
}
/* line 263, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results .user_element .user_info .field_element label {
  display: none !important;
}
/* line 269, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results .user_element .buttons_wrapper {
  width: 400px;
  padding: 0 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
/* line 276, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results .user_element .buttons_wrapper div {
  width: calc(50% - 5px);
  height: 40px;
  border: 1px solid #43425D;
  border-radius: 4px !important;
  background-color: white;
  margin: 15px 0;
  font-size: 18px;
  letter-spacing: 0.45px;
  color: #43425D;
  transition: opacity 0.3s;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
}
/* line 292, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results .user_element .buttons_wrapper div.select_user_btn, .vacation_club_container .vacation_club_results .user_element .buttons_wrapper div.cancel_user_btn {
  background-color: #43425D;
  color: white;
}
/* line 298, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results .user_element .buttons_wrapper div:hover {
  opacity: 0.7;
}
/* line 303, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results .user_element .buttons_wrapper .cancel_user_btn {
  display: none;
}
/* line 308, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results .user_element.vacation_club_user_template {
  display: none;
}
/* line 314, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results .user_element.selected .buttons_wrapper .cancel_user_btn {
  display: flex;
}
/* line 318, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results .user_element.selected .buttons_wrapper .select_user_btn {
  display: none;
}
/* line 325, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results .pagination {
  display: none;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  margin-top: 20px;
}
/* line 332, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results .pagination .pages {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 10px;
}
/* line 338, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results .pagination .pages .page_button {
  margin: 0 10px;
  cursor: pointer;
}
/* line 342, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results .pagination .pages .page_button.active {
  color: #F28E2A;
}
/* line 348, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results .pagination i {
  font-size: 24px;
  margin-top: 3px;
  cursor: pointer;
}
/* line 354, ../../../sass/vacation_club/vacation_club.scss */
.vacation_club_container .vacation_club_results .pagination.active {
  display: flex;
}
